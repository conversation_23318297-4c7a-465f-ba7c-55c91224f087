package com.official.fivegfastvpn.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.SearchView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.official.fivegfastvpn.R;

import com.official.fivegfastvpn.SplashActivity;
import com.official.fivegfastvpn.adapter.AllServersAdapter;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.model.Server;

import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AllServersFragment extends Fragment implements AllServersAdapter.OnSelectListener {

    private AllServersAdapter serverAdapter;
    private List<Server> originalServers;
    private SearchView searchView;
    private ImageView refreshButton;
    private ProgressBar progressBar;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View layout = inflater.inflate(R.layout.fragment_all_servers, container, false);
        setHasOptionsMenu(true);

        RecyclerView recyclerView = layout.findViewById(R.id.recycler_servers);
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        serverAdapter = new AllServersAdapter(getActivity());
        serverAdapter.setOnSelectListener(this);
        recyclerView.setAdapter(serverAdapter);
        Log.d("ServerDebug", "RecyclerView setup completed with LinearLayoutManager");

        searchView = layout.findViewById(R.id.searchView);
        refreshButton = layout.findViewById(R.id.refreshButton);
        progressBar = layout.findViewById(R.id.progressBar);

        refreshButton.setOnClickListener(v -> {
            loadServersWithAnimation();
            Intent intent = new Intent(getActivity(), SplashActivity.class);
            startActivity(intent);
        });

        setupSearchView();
        loadServers();

        return layout;
    }

    private void setupSearchView() {
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                filterList(query);
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                filterList(newText);
                return true;
            }
        });
    }

    private void loadServers() {
        Log.d("ServerDebug", "Starting to load servers");
        originalServers = new ArrayList<>();
        ArrayList<Server> servers = new ArrayList<>();
        try {
            Log.d("ServerDebug", "Const.SERVERS content: " + Const.SERVERS);
            if (Const.SERVERS == null || Const.SERVERS.isEmpty()) {
                Log.e("ServerDebug", "Server data is empty or null");
                return;
            }
            
            JSONArray jsonArray = new JSONArray(Const.SERVERS);
            Log.d("ServerDebug", "Number of servers found: " + jsonArray.length());
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = (JSONObject) jsonArray.get(i);
                Server server = new Server(
                    object.getString("name"),
                    Utils.imgUrl("flag/", object.getString("flagURL")),
                    object.getString("configFile"),
                    object.getString("username"),
                    object.getString("password")
                );
                // Add server type info
                server.setType(object.getString("type"));
                servers.add(server);
                originalServers.add(server);
                Log.d("ServerDebug", "Added server: " + server.getCountry());
            }
            Log.d("ServerDebug", "Total servers loaded: " + servers.size());
            serverAdapter.setServers(servers);
        } catch (Exception e) {
            Log.e("ServerDebug", "Error loading servers: " + e.getMessage(), e);
        }
    }

    private void loadServersWithAnimation() {
        progressBar.setVisibility(View.VISIBLE);
        loadServers();
        progressBar.setVisibility(View.GONE);
    }

    private void filterList(String query) {
        ArrayList<Server> filteredList = new ArrayList<>();
        if (originalServers != null) {
            for (Server server : originalServers) {
                if (server.getCountry().toLowerCase().contains(query.toLowerCase())) {
                    filteredList.add(server);
                }
            }
            serverAdapter.setServers(filteredList);
        }
    }

    @Override
    public void onSelected(Server server) {
        if (getActivity() != null) {
            Intent mIntent = new Intent();
            mIntent.putExtra("server", server);
            Pref pref = new Pref(getActivity());
            pref.saveServer(server);
            getActivity().setResult(getActivity().RESULT_OK, mIntent);
            getActivity().finish();
        }
    }
}
