<?php
/**
 * Custom Ads Provider Dashboard - Main Entry Point
 * Handles provider authentication and dashboard access
 */

session_start();
require_once '../admin_new/includes/config.php';

// Check if provider is logged in
function isProviderLoggedIn() {
    return isset($_SESSION['provider_id']) && !empty($_SESSION['provider_id']);
}

// Get provider information
function getProviderInfo($conn, $providerId) {
    $stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE provider_id = ? AND status = 'active'");
    $stmt->bind_param("s", $providerId);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: login.php");
    exit;
}

// Redirect to login if not logged in
if (!isProviderLoggedIn()) {
    header("Location: login.php");
    exit;
}

// Get provider information
$provider = getProviderInfo($conn, $_SESSION['provider_id']);
if (!$provider) {
    session_destroy();
    header("Location: login.php?error=invalid_provider");
    exit;
}

// Update last login
$stmt = $conn->prepare("UPDATE custom_ads_providers SET last_login = NOW() WHERE provider_id = ?");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();

// Get dashboard statistics
$stats = [];

// Total ads
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM custom_ads WHERE provider_id = (SELECT id FROM custom_ads_providers WHERE provider_id = ?)");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$result = $stmt->get_result();
$stats['total_ads'] = $result->fetch_assoc()['total'];

// Active ads
$stmt = $conn->prepare("SELECT COUNT(*) as active FROM custom_ads WHERE provider_id = (SELECT id FROM custom_ads_providers WHERE provider_id = ?) AND `on` = 1 AND is_approved = 1 AND (expires_at IS NULL OR expires_at > NOW())");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$result = $stmt->get_result();
$stats['active_ads'] = $result->fetch_assoc()['active'];

// Total views and clicks
$stmt = $conn->prepare("SELECT SUM(view_count) as total_views, SUM(click_count) as total_clicks FROM custom_ads WHERE provider_id = (SELECT id FROM custom_ads_providers WHERE provider_id = ?)");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$result = $stmt->get_result();
$analytics = $result->fetch_assoc();
$stats['total_views'] = $analytics['total_views'] ?? 0;
$stats['total_clicks'] = $analytics['total_clicks'] ?? 0;
$stats['ctr'] = $stats['total_views'] > 0 ? round(($stats['total_clicks'] / $stats['total_views']) * 100, 2) : 0;

// Pending payments
$stmt = $conn->prepare("SELECT COUNT(*) as pending FROM customer_payments WHERE customer_id = (SELECT id FROM customer_accounts WHERE whatsapp_number = ?) AND payment_status = 'pending'");
$stmt->bind_param("s", $provider['whatsapp_number']);
$stmt->execute();
$result = $stmt->get_result();
$stats['pending_payments'] = $result->fetch_assoc()['pending'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Provider Dashboard - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h5 class="text-white">5G Smart VPN</h5>
                    <small class="text-white-50">Custom Ads Provider</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link" href="ads.php">
                        <i class="fas fa-ad me-2"></i> My Ads
                    </a>
                    <a class="nav-link" href="create-ad.php">
                        <i class="fas fa-plus-circle me-2"></i> Create Ad
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a class="nav-link" href="packages.php">
                        <i class="fas fa-box me-2"></i> Packages
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i> Payments
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user me-2"></i> Profile
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="?logout=1">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>Welcome back, <?php echo htmlspecialchars($provider['contact_person']); ?>!</h2>
                        <p class="text-muted">Manage your custom ads and track performance</p>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">Last login: <?php echo $provider['last_login'] ? date('M j, Y g:i A', strtotime($provider['last_login'])) : 'First time'; ?></small>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3 class="mb-0"><?php echo $stats['total_ads']; ?></h3>
                                        <p class="mb-0">Total Ads</p>
                                    </div>
                                    <i class="fas fa-ad stat-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3 class="mb-0"><?php echo $stats['active_ads']; ?></h3>
                                        <p class="mb-0">Active Ads</p>
                                    </div>
                                    <i class="fas fa-check-circle stat-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3 class="mb-0"><?php echo number_format($stats['total_views']); ?></h3>
                                        <p class="mb-0">Total Views</p>
                                    </div>
                                    <i class="fas fa-eye stat-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3 class="mb-0"><?php echo $stats['ctr']; ?>%</h3>
                                        <p class="mb-0">Click Rate</p>
                                    </div>
                                    <i class="fas fa-mouse-pointer stat-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-rocket me-2"></i>Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="create-ad.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create New Ad
                                    </a>
                                    <a href="packages.php" class="btn btn-outline-primary">
                                        <i class="fas fa-shopping-cart me-2"></i>Buy Ad Package
                                    </a>
                                    <a href="analytics.php" class="btn btn-outline-info">
                                        <i class="fas fa-chart-line me-2"></i>View Analytics
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Account Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Business:</strong> <?php echo htmlspecialchars($provider['business_name']); ?>
                                </div>
                                <div class="mb-3">
                                    <strong>Status:</strong> 
                                    <span class="badge bg-success"><?php echo ucfirst($provider['status']); ?></span>
                                </div>
                                <div class="mb-3">
                                    <strong>Total Spent:</strong> ৳<?php echo number_format($provider['total_spent'], 2); ?>
                                </div>
                                <?php if ($stats['pending_payments'] > 0): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    You have <?php echo $stats['pending_payments']; ?> pending payment(s)
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
