<?php
/**
 * Custom Ads Provider - Payments Page
 */

session_start();
require_once '../admin_new/includes/config.php';

if (!isset($_SESSION['provider_id'])) {
    header("Location: login.php");
    exit;
}

// Get provider's payments
$stmt = $conn->prepare("SELECT cp.*, ap.package_name FROM customer_payments cp LEFT JOIN ad_packages ap ON cp.package_id = ap.id WHERE cp.customer_id = (SELECT id FROM customer_accounts WHERE whatsapp_number = ?) ORDER BY cp.created_at DESC");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h5 class="text-white">5G Smart VPN</h5>
                    <small class="text-white-50">Custom Ads Provider</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link" href="ads.php">
                        <i class="fas fa-ad me-2"></i> My Ads
                    </a>
                    <a class="nav-link" href="create-ad.php">
                        <i class="fas fa-plus-circle me-2"></i> Create Ad
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a class="nav-link" href="packages.php">
                        <i class="fas fa-box me-2"></i> Packages
                    </a>
                    <a class="nav-link active" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i> Payments
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user me-2"></i> Profile
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="?logout=1">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>Payment History</h2>
                        <p class="text-muted">Track your payments and transactions</p>
                    </div>
                    <div>
                        <a href="packages.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Buy Package
                        </a>
                    </div>
                </div>
                
                <!-- Payments Table -->
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($payments)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h4>No Payments Found</h4>
                                <p class="text-muted">You haven't made any payments yet.</p>
                                <a href="packages.php" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart me-2"></i>Browse Packages
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Package</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Status</th>
                                            <th>Transaction ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($payment['created_at'])); ?></td>
                                                <td><?php echo htmlspecialchars($payment['package_name'] ?? 'N/A'); ?></td>
                                                <td>৳<?php echo number_format($payment['amount'], 2); ?></td>
                                                <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $payment['status'] === 'verified' ? 'success' : 
                                                            ($payment['status'] === 'pending' ? 'warning' : 'danger'); 
                                                    ?>">
                                                        <?php echo ucfirst($payment['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($payment['transaction_id'] ?? 'N/A'); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
