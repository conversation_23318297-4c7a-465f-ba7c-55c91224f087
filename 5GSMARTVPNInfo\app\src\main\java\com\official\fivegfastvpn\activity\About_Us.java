package com.official.fivegfastvpn.activity;


import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.official.fivegfastvpn.R;


//Developer :--<PERSON><PERSON> <PERSON><PERSON><PERSON>
public class About_Us extends AppCompatActivity {

    TextView version;
    ImageView back;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about_us);
        version = findViewById(R.id.version);
        back = findViewById(R.id.tool_back);

        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        try {
            PackageInfo pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String version1 = pInfo.versionName;
            version.setText("Version : - "+version1);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();

        }
    }
}