<?php
/**
 * 5G Smart VPN Admin Panel - Scheduled Notifications Processor
 * Cron job to process scheduled and recurring notifications
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../includes/notification_functions.php';

// Log file for cron activities
$log_file = __DIR__ . '/cron_log.txt';

/**
 * Log message with timestamp
 */
function logMessage($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry;
}

/**
 * Calculate next scheduled time for recurring notifications
 */
function calculateNextScheduledTime($originalTime, $interval) {
    $now = new DateTime();
    $original = new DateTime($originalTime);
    
    switch ($interval) {
        case 'hourly':
            $next = clone $now;
            $next->add(new DateInterval('PT1H'));
            break;
            
        case 'daily':
            $next = clone $original;
            $next->setDate($now->format('Y'), $now->format('m'), $now->format('d'));
            if ($next <= $now) {
                $next->add(new DateInterval('P1D'));
            }
            break;
            
        case 'weekly':
            $next = clone $original;
            $next->setDate($now->format('Y'), $now->format('m'), $now->format('d'));
            $next->modify('next ' . $original->format('l'));
            if ($next <= $now) {
                $next->add(new DateInterval('P7D'));
            }
            break;
            
        case 'monthly':
            $next = clone $original;
            $next->setDate($now->format('Y'), $now->format('m'), $original->format('d'));
            if ($next <= $now) {
                $next->add(new DateInterval('P1M'));
            }
            break;
            
        default:
            $next = clone $now;
            $next->add(new DateInterval('P1D'));
    }
    
    return $next->format('Y-m-d H:i:s');
}

try {
    logMessage("Starting scheduled notifications processor...");
    
    // Get notifications that are due for processing
    $sql = "SELECT * FROM notifications 
            WHERE status = 'scheduled' 
            AND schedule_type IN ('scheduled', 'recurring') 
            AND next_run_time <= NOW()
            ORDER BY priority DESC, next_run_time ASC
            LIMIT 50"; // Process max 50 notifications per run
            
    $result = $conn->query($sql);
    $count = $result->num_rows;
    logMessage("Found $count notifications to process");
    
    $processed = 0;
    $successful = 0;
    $failed = 0;
    
    while ($notification = $result->fetch_assoc()) {
        $processed++;
        logMessage("Processing notification ID: {$notification['id']} - '{$notification['title']}'");
        
        try {
            // Parse data if exists
            $data = [];
            if (!empty($notification['data'])) {
                $data = json_decode($notification['data'], true) ?? [];
            }
            
            // Send the notification
            $sendResult = sendFCMNotification(
                $notification['title'], 
                $notification['message'], 
                $notification['sent_to'] ?? 'all',
                'immediate', // Force immediate sending for scheduled notifications
                null,
                null,
                $data
            );
            
            if ($sendResult['success']) {
                $successful++;
                logMessage("✅ Successfully sent notification ID: {$notification['id']}");
                
                // Update delivery counts
                $update_counts_sql = "UPDATE notifications 
                                     SET delivery_count = delivery_count + 1,
                                         success_count = success_count + 1
                                     WHERE id = ?";
                $update_stmt = $conn->prepare($update_counts_sql);
                $update_stmt->bind_param("i", $notification['id']);
                $update_stmt->execute();
                
                // Handle recurring vs one-time notifications
                if ($notification['schedule_type'] === 'recurring') {
                    // Calculate next run time
                    $nextTime = calculateNextScheduledTime(
                        $notification['scheduled_time'], 
                        $notification['recurring_interval']
                    );
                    
                    $sql = "UPDATE notifications 
                            SET next_run_time = ?, 
                                last_run_time = NOW(),
                                status = 'scheduled'
                            WHERE id = ?";
                                
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("si", $nextTime, $notification['id']);
                    $stmt->execute();
                    logMessage("📅 Updated recurring notification, next run: $nextTime");
                } else {
                    // Mark one-time scheduled notification as sent
                    $sql = "UPDATE notifications 
                            SET status = 'sent',
                                last_run_time = NOW()
                            WHERE id = ?";
                                
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("i", $notification['id']);
                    $stmt->execute();
                    logMessage("✅ Marked one-time notification as sent");
                }
                
                // Log the action
                logNotificationAction($notification['id'], 'sent_scheduled', 'Notification sent via scheduled processor');
                
            } else {
                $failed++;
                $error = $sendResult['error'] ?? 'Unknown error';
                logMessage("❌ Failed to send notification ID: {$notification['id']} - Error: $error");
                
                // Update failure count
                $update_counts_sql = "UPDATE notifications 
                                     SET delivery_count = delivery_count + 1,
                                         failure_count = failure_count + 1
                                     WHERE id = ?";
                $update_stmt = $conn->prepare($update_counts_sql);
                $update_stmt->bind_param("i", $notification['id']);
                $update_stmt->execute();
                
                // For failed notifications, retry logic
                $retry_count = $notification['failure_count'] + 1;
                if ($retry_count < 3) {
                    // Retry in 5 minutes
                    $retry_time = date('Y-m-d H:i:s', strtotime('+5 minutes'));
                    $sql = "UPDATE notifications 
                            SET next_run_time = ?,
                                status = 'scheduled'
                            WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("si", $retry_time, $notification['id']);
                    $stmt->execute();
                    logMessage("🔄 Scheduled retry for notification ID: {$notification['id']} at $retry_time");
                } else {
                    // Mark as failed after 3 attempts
                    $sql = "UPDATE notifications 
                            SET status = 'failed',
                                last_run_time = NOW()
                            WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("i", $notification['id']);
                    $stmt->execute();
                    logMessage("💀 Marked notification ID: {$notification['id']} as permanently failed");
                }
                
                // Log the failure
                logNotificationAction($notification['id'], 'failed_scheduled', $error);
            }
            
        } catch (Exception $e) {
            $failed++;
            $error = $e->getMessage();
            logMessage("💥 Exception processing notification ID: {$notification['id']} - Error: $error");
            
            // Log the exception
            logNotificationAction($notification['id'], 'exception', $error);
        }
        
        // Small delay to prevent overwhelming the FCM service
        usleep(100000); // 0.1 second delay
    }
    
    logMessage("📊 Processing completed:");
    logMessage("   - Total processed: $processed");
    logMessage("   - Successful: $successful");
    logMessage("   - Failed: $failed");
    
    // Clean up old notification logs (keep last 30 days)
    $cleanup_sql = "DELETE FROM notification_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $cleanup_result = $conn->query($cleanup_sql);
    $cleaned_logs = $conn->affected_rows;
    if ($cleaned_logs > 0) {
        logMessage("🧹 Cleaned up $cleaned_logs old log entries");
    }
    
    logMessage("✅ Scheduled notifications processor completed successfully");
    
} catch (Exception $e) {
    logMessage("💥 Fatal error in scheduled notifications processor: " . $e->getMessage());
    exit(1);
}

/**
 * Log notification action
 */
function logNotificationAction($notificationId, $action, $details) {
    global $conn;
    
    $sql = "INSERT INTO notification_logs (notification_id, action, details, user_agent, ip_address) VALUES (?, ?, ?, 'cron-processor', 'localhost')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $notificationId, $action, $details);
    $stmt->execute();
}
?>
