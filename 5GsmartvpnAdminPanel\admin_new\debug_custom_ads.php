<?php
/**
 * Debug Custom Ads - Check database status and API response
 */

require_once 'includes/config.php';

echo "<h2>Custom Ads Debug Information</h2>";

try {
    // Check database connection
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Check custom_ads table structure
    echo "<h3>1. Custom Ads Table Structure</h3>";
    $result = $conn->query("DESCRIBE custom_ads");
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Check all ads in database
    echo "<h3>2. All Ads in Database</h3>";
    $result = $conn->query("SELECT * FROM custom_ads ORDER BY id DESC");
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Text</th><th>Image</th><th>URL</th><th>Start</th><th>End</th><th>On</th><th>Approved</th><th>Priority</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['text'] ?? '', 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($row['image'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['url'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['date_start'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['date_end'] ?? '') . "</td>";
            echo "<td style='color: " . ($row['on'] ? 'green' : 'red') . ";'>" . ($row['on'] ? 'YES' : 'NO') . "</td>";
            echo "<td style='color: " . ($row['is_approved'] === null ? 'orange' : ($row['is_approved'] ? 'green' : 'red')) . ";'>" . 
                 ($row['is_approved'] === null ? 'PENDING' : ($row['is_approved'] ? 'YES' : 'NO')) . "</td>";
            echo "<td>" . htmlspecialchars($row['priority'] ?? '0') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>No ads found in database</p>";
    }

    // Check ad approval system queries
    echo "<h3>3. Ad Approval System Analysis</h3>";

    // Test pending ads query (same as ad-approval.php)
    echo "<h4>3.1 Pending Ads Query</h4>";
    $pending_query = "
        SELECT
            ca.*,
            cac.customer_name,
            cac.whatsapp_number,
            ap.package_name,
            cp.amount as payment_amount,
            cp.payment_status
        FROM custom_ads ca
        LEFT JOIN customer_accounts cac ON ca.customer_id = cac.id
        LEFT JOIN ad_packages ap ON ca.package_id = ap.id
        LEFT JOIN customer_payments cp ON ca.payment_id = cp.id
        WHERE ca.is_approved IS NULL
        ORDER BY ca.created_at DESC
    ";
    $pending_result = mysqli_query($conn, $pending_query);
    if ($pending_result && $pending_result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Found " . $pending_result->num_rows . " pending ads</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Customer</th><th>Package</th><th>Payment</th><th>Created</th></tr>";
        while ($row = $pending_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['customer_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['payment_status'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['created_at'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ No pending ads found</p>";
    }

    // Test approved ads query
    echo "<h4>3.2 Approved Ads Query</h4>";
    $approved_query = "
        SELECT
            ca.*,
            cac.customer_name,
            cac.whatsapp_number,
            ap.package_name,
            cp.amount as payment_amount,
            cp.payment_status
        FROM custom_ads ca
        LEFT JOIN customer_accounts cac ON ca.customer_id = cac.id
        LEFT JOIN ad_packages ap ON ca.package_id = ap.id
        LEFT JOIN customer_payments cp ON ca.payment_id = cp.id
        WHERE ca.is_approved = 1
        ORDER BY ca.created_at DESC
    ";
    $approved_result = mysqli_query($conn, $approved_query);
    if ($approved_result && $approved_result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Found " . $approved_result->num_rows . " approved ads</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Customer</th><th>Package</th><th>Payment</th><th>Approved At</th></tr>";
        while ($row = $approved_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['customer_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['payment_status'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['approved_at'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ No approved ads found</p>";
    }

    // Check active ads (what the API should return)
    echo "<h3>4. Active Ads (API Query)</h3>";
    $today = date("Y-m-d");
    $stmt = $conn->prepare("SELECT *,
        CASE
            WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 0
            ELSE 1
        END as is_active_now
        FROM custom_ads
        WHERE `on` = 1
        AND date_start <= ?
        AND date_end >= ?
        AND (is_approved = 1 OR is_approved IS NULL)
        AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY priority DESC, RAND()");
    $stmt->bind_param("ss", $today, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Found " . $result->num_rows . " active ads</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Text</th><th>Priority</th><th>Start</th><th>End</th><th>Approved</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['text'] ?? '', 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($row['priority'] ?? '0') . "</td>";
            echo "<td>" . htmlspecialchars($row['date_start'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['date_end'] ?? '') . "</td>";
            echo "<td style='color: green;'>YES</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ No active ads found</p>";
        echo "<p>Possible reasons:</p>";
        echo "<ul>";
        echo "<li>No ads have `on` = 1</li>";
        echo "<li>No ads are approved (is_approved = 1)</li>";
        echo "<li>Ads are outside date range</li>";
        echo "<li>Ads have expired (expires_at)</li>";
        echo "</ul>";
    }

    // Test API endpoint
    echo "<h3>5. API Endpoint Test</h3>";
    $api_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/custom_ads.php";
    echo "<p><strong>API URL:</strong> <a href='$api_url' target='_blank'>$api_url</a></p>";
    echo "<p><em>Note: API requires authentication. Check the link manually or use the Android app.</em></p>";

    // Check missing tables/columns that might cause approval issues
    echo "<h3>6. Database Schema Validation</h3>";

    // Check if required tables exist
    $tables_to_check = ['custom_ads', 'customer_accounts', 'ad_packages', 'customer_payments'];
    foreach ($tables_to_check as $table) {
        $check_table = $conn->query("SHOW TABLES LIKE '$table'");
        if ($check_table && $check_table->num_rows > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }

    // Check if required columns exist in custom_ads
    $required_columns = ['is_approved', 'approved_by', 'approved_at', 'customer_id', 'package_id', 'payment_id', 'created_at'];
    $columns_result = $conn->query("DESCRIBE custom_ads");
    $existing_columns = [];
    if ($columns_result) {
        while ($col = $columns_result->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }
    }

    foreach ($required_columns as $column) {
        if (in_array($column, $existing_columns)) {
            echo "<p style='color: green;'>✓ Column 'custom_ads.$column' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Column 'custom_ads.$column' missing</p>";
        }
    }

    // Quick fix suggestions
    echo "<h3>7. Quick Fix Actions</h3>";
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='approve_all' style='background: green; color: white; padding: 10px; border: none; border-radius: 5px;'>Approve All Ads</button>";
    echo "</form>";

    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='activate_all' style='background: blue; color: white; padding: 10px; border: none; border-radius: 5px;'>Activate All Ads</button>";
    echo "</form>";

    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='extend_dates' style='background: orange; color: white; padding: 10px; border: none; border-radius: 5px;'>Extend All Ad Dates</button>";
    echo "</form>";

    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='create_test_ad' style='background: purple; color: white; padding: 10px; border: none; border-radius: 5px;'>Create Test Ad for Approval</button>";
    echo "</form>";

    // Handle quick fix actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['approve_all'])) {
            $conn->query("UPDATE custom_ads SET is_approved = 1 WHERE is_approved IS NULL OR is_approved = 0");
            echo "<p style='color: green;'>✓ All ads approved</p>";
        }
        
        if (isset($_POST['activate_all'])) {
            $conn->query("UPDATE custom_ads SET `on` = 1");
            echo "<p style='color: green;'>✓ All ads activated</p>";
        }
        
        if (isset($_POST['extend_dates'])) {
            $conn->query("UPDATE custom_ads SET date_start = CURDATE(), date_end = DATE_ADD(CURDATE(), INTERVAL 30 DAY)");
            echo "<p style='color: green;'>✓ All ad dates extended</p>";
        }

        if (isset($_POST['create_test_ad'])) {
            // Create a test ad that needs approval
            $test_title = "Test Ad for Approval - " . date('Y-m-d H:i:s');
            $test_text = "This is a test advertisement created for testing the approval system.";
            $test_url = "https://example.com/test";
            $test_start = date('Y-m-d');
            $test_end = date('Y-m-d', strtotime('+30 days'));

            $insert_query = "INSERT INTO custom_ads (title, text, url, date_start, date_end, `on`, view_count, click_count, is_approved, priority, url_type, button_text, created_at)
                           VALUES (?, ?, ?, ?, ?, 1, 0, 0, NULL, 1, 'website', 'Visit', NOW())";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("sssss", $test_title, $test_text, $test_url, $test_start, $test_end);

            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Test ad created successfully (ID: " . $conn->insert_id . ")</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create test ad: " . $conn->error . "</p>";
            }
        }

        echo "<script>setTimeout(function(){ location.reload(); }, 1000);</script>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
