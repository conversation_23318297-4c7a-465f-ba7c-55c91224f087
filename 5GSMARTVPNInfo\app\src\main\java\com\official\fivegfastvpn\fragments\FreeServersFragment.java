package com.official.fivegfastvpn.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;

import androidx.appcompat.widget.SearchView;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.SplashActivity;
import com.official.fivegfastvpn.adapter.FreeServerAdapter;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;


//Developer :--Md Sadrul Hasan Dider

public class FreeServersFragment extends Fragment implements FreeServerAdapter.OnSelectListener {


    private FreeServerAdapter serverAdapter;

    private List<Server> originalServers;
    private SearchView searchView;
    private ImageView refreshButton;
    private ProgressBar progressBar;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View layout = inflater.inflate(R.layout.fragment_free_server, container, false);
        setHasOptionsMenu(true); // Enable options menu

        RecyclerView recyclerView = layout.findViewById(R.id.recycler_free);
        serverAdapter = new FreeServerAdapter(getActivity());
        serverAdapter.setOnSelectListener(this);
        recyclerView.setAdapter(serverAdapter);

        // Create the SearchView directly in the layout
        searchView = layout.findViewById(R.id.searchView1);
        refreshButton=layout.findViewById(R.id.refreshButton);
        progressBar = layout.findViewById(R.id.progressBar);

        refreshButton.setOnClickListener(v -> {

            loadServersWithAnimation();


            Intent intent = new Intent(getActivity(), SplashActivity.class);

            startActivity(intent);


        });
       /* // Find the Auto Select card by ID (assuming it's in card2)
        CardView autoSelectCard = layout.findViewById(R.id.card2);

        autoSelectCard.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                selectRandomServer();
            }
        });*/
        setupSearchView();

        loadServers();

        return layout;
    }
    private void loadServersWithAnimation() {
        progressBar.setVisibility(View.VISIBLE);
        refreshButton.animate().rotation(360).setDuration(1000).withEndAction(() -> {
            loadServers();
        }).start();
    }
    private void setupSearchView() {
        if (searchView != null) {
            searchView.setQueryHint(getString(R.string.search));
            searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String query) {
                    return false;
                }

                @Override
                public boolean onQueryTextChange(String newText) {
                    filterList(newText);
                    return true;
                }
            });
        }
    }
/*
    // Add a method to select a random server
    private void selectRandomServer() {
        if (originalServers != null && originalServers.size() > 0) {
            int randomIndex = new Random().nextInt(originalServers.size());
            Server randomServer = originalServers.get(randomIndex);
            onSelected(randomServer); // Trigger the server selection
        }
    }*/

    private void loadServers() {

        originalServers = new ArrayList<>(); // Initialize the original servers list

        ArrayList<Server> servers = new ArrayList<>();

        try {

            JSONArray jsonArray = new JSONArray(Const.SERVERS);

            for (int i = 0; i < jsonArray.length(); i++) {

                JSONObject object = jsonArray.getJSONObject(i);


                if (object.getString("type").contains("1")) {

                    servers.add(new Server(object.getString("name"),

                            Utils.imgUrl("flag/", object.getString("flagURL")),

                            object.getString("configFile"),

                            object.getString("username"),

                            object.getString("password")

                    ));

                    originalServers.add(new Server(object.getString("name"),

                            Utils.imgUrl("flag/", object.getString("flagURL")),

                            object.getString("configFile"),

                            object.getString("username"),

                            object.getString("password")

                    ));

                }

            }

        } catch (JSONException e) {

            e.printStackTrace();

        }

        progressBar.setVisibility(View.GONE);

        refreshButton.animate().rotation(0).setDuration(1000).start();

        serverAdapter.setData(servers);

    }


   /* private void loadServers() {
        originalServers = new ArrayList<>(); // Initialize the original servers list
        ArrayList<Server> servers = new ArrayList<>();
        try {
            JSONArray jsonArray = new JSONArray(Const.SERVERS);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);

                if (object.getString("type").contains("1")) {
                    servers.add(new Server(object.getString("name"),
                            Utils.imgUrl("flag/", object.getString("flagURL")),
                            object.getString("configFile"),
                            object.getString("username"),
                            object.getString("password")
                    ));
                    originalServers.add(new Server(object.getString("name"),
                            Utils.imgUrl("flag/", object.getString("flagURL")),
                            object.getString("configFile"),
                            object.getString("username"),
                            object.getString("password")
                    ));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        serverAdapter.setData(servers);
    }*/

    private void filterList(String query) {
        ArrayList<Server> filteredServers = new ArrayList<>();
        if (originalServers != null) {
            for (Server server : originalServers) {
                if (TextUtils.isEmpty(query) || server.getCountry().toLowerCase().contains(query.toLowerCase())) {
                    filteredServers.add(server);
                }
            }
        }
        serverAdapter.setData(filteredServers);
    }

    @Override
    public void onSelected(Server server) {
        if (getActivity() != null) {
            Intent mIntent = new Intent();
            mIntent.putExtra("server", server);
            Pref pref = new Pref(getActivity());
            pref.saveServer(server);
            getActivity().setResult(getActivity().RESULT_OK, mIntent);
            getActivity().finish();
        }
    }
}