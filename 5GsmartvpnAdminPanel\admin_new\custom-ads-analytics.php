<?php
/**
 * Admin Panel - Custom Ads Analytics
 * Analytics and reporting for custom ads system
 */

session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$page_title = "Custom Ads Analytics";
$success = '';
$error = '';

// Add Bootstrap CSS and JS for charts
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
    'https://cdn.jsdelivr.net/npm/chart.js'
];

// Get date range filter
$date_range = $_GET['range'] ?? '30';
$start_date = date('Y-m-d', strtotime("-{$date_range} days"));
$end_date = date('Y-m-d');

// Overall statistics
$stats = [];

// Revenue statistics
$revenue_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(CASE WHEN payment_status = 'verified' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as pending_revenue,
        AVG(CASE WHEN payment_status = 'verified' THEN amount ELSE NULL END) as avg_payment
    FROM customer_payments 
    WHERE created_at >= '$start_date' AND created_at <= '$end_date 23:59:59'
";
$revenue_result = mysqli_query($conn, $revenue_query);
$revenue_stats = mysqli_fetch_assoc($revenue_result);

// Check if required tables exist
$customer_accounts_exists = mysqli_query($conn, "SHOW TABLES LIKE 'customer_accounts'");
$customer_payments_exists = mysqli_query($conn, "SHOW TABLES LIKE 'customer_payments'");
$payment_methods_exists = mysqli_query($conn, "SHOW TABLES LIKE 'payment_methods'");
$ad_packages_exists = mysqli_query($conn, "SHOW TABLES LIKE 'ad_packages'");

// Customer statistics
$customer_stats = ['total_customers' => 0, 'paying_customers' => 0, 'new_customers' => 0];

if (mysqli_num_rows($customer_accounts_exists) > 0) {
    // Check if created_at column exists in customer_accounts table
    $customer_created_at_check = mysqli_query($conn, "SHOW COLUMNS FROM customer_accounts LIKE 'created_at'");
    $customer_has_created_at = mysqli_num_rows($customer_created_at_check) > 0;

    if (mysqli_num_rows($customer_payments_exists) > 0) {
        if ($customer_has_created_at) {
            $customer_query = "
                SELECT
                    COUNT(DISTINCT ca.id) as total_customers,
                    COUNT(DISTINCT CASE WHEN cp.payment_status = 'verified' THEN ca.id END) as paying_customers,
                    COUNT(DISTINCT CASE WHEN ca.created_at >= '$start_date' THEN ca.id END) as new_customers
                FROM customer_accounts ca
                LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
            ";
        } else {
            $customer_query = "
                SELECT
                    COUNT(DISTINCT ca.id) as total_customers,
                    COUNT(DISTINCT CASE WHEN cp.payment_status = 'verified' THEN ca.id END) as paying_customers,
                    0 as new_customers
                FROM customer_accounts ca
                LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
            ";
        }
    } else {
        if ($customer_has_created_at) {
            $customer_query = "
                SELECT
                    COUNT(DISTINCT ca.id) as total_customers,
                    0 as paying_customers,
                    COUNT(DISTINCT CASE WHEN ca.created_at >= '$start_date' THEN ca.id END) as new_customers
                FROM customer_accounts ca
            ";
        } else {
            $customer_query = "
                SELECT
                    COUNT(DISTINCT ca.id) as total_customers,
                    0 as paying_customers,
                    0 as new_customers
                FROM customer_accounts ca
            ";
        }
    }
    $customer_result = mysqli_query($conn, $customer_query);
    if ($customer_result) {
        $customer_stats = mysqli_fetch_assoc($customer_result);
    }
}

// Check if required columns exist in custom_ads table
$columns_check = mysqli_query($conn, "SHOW COLUMNS FROM custom_ads LIKE 'created_at'");
$has_created_at = mysqli_num_rows($columns_check) > 0;

$columns_check = mysqli_query($conn, "SHOW COLUMNS FROM custom_ads LIKE 'is_approved'");
$has_is_approved = mysqli_num_rows($columns_check) > 0;

// Ad statistics - handle missing columns gracefully
if ($has_created_at && $has_is_approved) {
    $ad_query = "
        SELECT
            COUNT(*) as total_ads,
            COUNT(CASE WHEN is_approved = 1 THEN 1 END) as approved_ads,
            COUNT(CASE WHEN is_approved = 0 THEN 1 END) as rejected_ads,
            COUNT(CASE WHEN is_approved IS NULL THEN 1 END) as pending_ads
        FROM custom_ads
        WHERE created_at >= '$start_date' AND created_at <= '$end_date 23:59:59'
    ";
} else if ($has_created_at) {
    $ad_query = "
        SELECT
            COUNT(*) as total_ads,
            0 as approved_ads,
            0 as rejected_ads,
            COUNT(*) as pending_ads
        FROM custom_ads
        WHERE created_at >= '$start_date' AND created_at <= '$end_date 23:59:59'
    ";
} else {
    $ad_query = "
        SELECT
            COUNT(*) as total_ads,
            0 as approved_ads,
            0 as rejected_ads,
            COUNT(*) as pending_ads
        FROM custom_ads
    ";
}
$ad_result = mysqli_query($conn, $ad_query);
$ad_stats = mysqli_fetch_assoc($ad_result);

// Package popularity
$package_stats = [];
if (mysqli_num_rows($ad_packages_exists) > 0) {
    if (mysqli_num_rows($customer_payments_exists) > 0) {
        $package_query = "
            SELECT
                ap.package_name,
                COUNT(cp.id) as purchase_count,
                SUM(CASE WHEN cp.payment_status = 'verified' THEN cp.amount ELSE 0 END) as revenue
            FROM ad_packages ap
            LEFT JOIN customer_payments cp ON ap.id = cp.package_id
                AND cp.created_at >= '$start_date' AND cp.created_at <= '$end_date 23:59:59'
            GROUP BY ap.id, ap.package_name
            ORDER BY revenue DESC
        ";
    } else {
        $package_query = "
            SELECT
                ap.package_name,
                0 as purchase_count,
                0 as revenue
            FROM ad_packages ap
            ORDER BY ap.package_name
        ";
    }
    $package_result = mysqli_query($conn, $package_query);
    if ($package_result) {
        $package_stats = mysqli_fetch_all($package_result, MYSQLI_ASSOC);
    }
}

// Daily revenue chart data
$daily_revenue = [];
if (mysqli_num_rows($customer_payments_exists) > 0) {
    $daily_revenue_query = "
        SELECT
            DATE(created_at) as date,
            SUM(CASE WHEN payment_status = 'verified' THEN amount ELSE 0 END) as revenue,
            COUNT(CASE WHEN payment_status = 'verified' THEN 1 END) as payments
        FROM customer_payments
        WHERE created_at >= '$start_date' AND created_at <= '$end_date 23:59:59'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ";
    $daily_revenue_result = mysqli_query($conn, $daily_revenue_query);
    if ($daily_revenue_result) {
        $daily_revenue = mysqli_fetch_all($daily_revenue_result, MYSQLI_ASSOC);
    }
}

// Payment method statistics
$payment_method_stats = [];
if (mysqli_num_rows($payment_methods_exists) > 0) {
    if (mysqli_num_rows($customer_payments_exists) > 0) {
        $payment_method_query = "
            SELECT
                pm.display_name,
                COUNT(cp.id) as payment_count,
                SUM(CASE WHEN cp.payment_status = 'verified' THEN cp.amount ELSE 0 END) as revenue
            FROM payment_methods pm
            LEFT JOIN customer_payments cp ON pm.method_code = cp.payment_method
                AND cp.created_at >= '$start_date' AND cp.created_at <= '$end_date 23:59:59'
            WHERE pm.is_active = 1
            GROUP BY pm.id, pm.display_name
            ORDER BY revenue DESC
        ";
    } else {
        $payment_method_query = "
            SELECT
                pm.display_name,
                0 as payment_count,
                0 as revenue
            FROM payment_methods pm
            WHERE pm.is_active = 1
            ORDER BY pm.display_name
        ";
    }
    $payment_method_result = mysqli_query($conn, $payment_method_query);
    if ($payment_method_result) {
        $payment_method_stats = mysqli_fetch_all($payment_method_result, MYSQLI_ASSOC);
    }
}

// Top customers
$top_customers = [];
if (mysqli_num_rows($customer_accounts_exists) > 0) {
    if (mysqli_num_rows($customer_payments_exists) > 0) {
        $top_customers_query = "
            SELECT
                ca.customer_name,
                ca.whatsapp_number,
                COUNT(cp.id) as total_payments,
                SUM(CASE WHEN cp.payment_status = 'verified' THEN cp.amount ELSE 0 END) as total_spent
            FROM customer_accounts ca
            LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
                AND cp.created_at >= '$start_date' AND cp.created_at <= '$end_date 23:59:59'
            GROUP BY ca.id
            HAVING total_spent > 0
            ORDER BY total_spent DESC
            LIMIT 10
        ";
        $top_customers_result = mysqli_query($conn, $top_customers_query);
        if ($top_customers_result) {
            $top_customers = mysqli_fetch_all($top_customers_result, MYSQLI_ASSOC);
        }
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Custom Ads Analytics</h1>
                <p class="page-subtitle">Analytics and insights for custom ads system</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <form method="GET" class="d-flex gap-2">
                        <select class="form-select" name="range" onchange="this.form.submit()">
                            <option value="7" <?php echo $date_range === '7' ? 'selected' : ''; ?>>Last 7 days</option>
                            <option value="30" <?php echo $date_range === '30' ? 'selected' : ''; ?>>Last 30 days</option>
                            <option value="90" <?php echo $date_range === '90' ? 'selected' : ''; ?>>Last 90 days</option>
                            <option value="365" <?php echo $date_range === '365' ? 'selected' : ''; ?>>Last year</option>
                        </select>
                        <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                            <i class="ri-refresh-line"></i>
                        </button>
                    </form>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php
            $missing_tables = [];
            if (mysqli_num_rows($customer_accounts_exists) == 0) $missing_tables[] = 'customer_accounts';
            if (mysqli_num_rows($customer_payments_exists) == 0) $missing_tables[] = 'customer_payments';
            if (mysqli_num_rows($payment_methods_exists) == 0) $missing_tables[] = 'payment_methods';
            if (mysqli_num_rows($ad_packages_exists) == 0) $missing_tables[] = 'ad_packages';

            if (!empty($missing_tables)): ?>
                <div class="alert alert-warning alert-dismissible fade show">
                    <i class="ri-database-2-line me-2"></i>
                    <strong>Database Setup Required!</strong> Some tables are missing: <?php echo implode(', ', $missing_tables); ?>.
                    Analytics data may be incomplete.
                    <a href="run_migration.php" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="ri-tools-line me-1"></i>Run Database Migration
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Overview Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-money-dollar-circle-line text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3>৳<?php echo number_format($revenue_stats['total_revenue'] ?? 0, 2); ?></h3>
                            <p>Total Revenue</p>
                            <small><?php echo $revenue_stats['total_payments'] ?? 0; ?> payments</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-user-line text-primary"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $customer_stats['total_customers'] ?? 0; ?></h3>
                            <p>Total Customers</p>
                            <small><?php echo $customer_stats['new_customers'] ?? 0; ?> new</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-advertisement-line text-info"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $ad_stats['total_ads'] ?? 0; ?></h3>
                            <p>Total Ads</p>
                            <small><?php echo $ad_stats['approved_ads'] ?? 0; ?> approved</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-line-chart-line text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3>৳<?php echo number_format($revenue_stats['avg_payment'] ?? 0, 2); ?></h3>
                            <p>Avg Payment</p>
                            <small>Per transaction</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Daily Revenue Trend</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Payment Methods</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="paymentMethodChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Performance -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Package Performance</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($package_stats)): ?>
                                <div class="text-center py-4">
                                    <i class="ri-package-line text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2">No package data available</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Package</th>
                                                <th>Purchases</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($package_stats as $package): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($package['package_name']); ?></td>
                                                    <td><?php echo $package['purchase_count']; ?></td>
                                                    <td>৳<?php echo number_format($package['revenue'], 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Top Customers</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_customers)): ?>
                                <div class="text-center py-4">
                                    <i class="ri-user-star-line text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2">No customer data available</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Payments</th>
                                                <th>Total Spent</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_customers as $customer): ?>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($customer['customer_name']); ?></strong>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($customer['whatsapp_number']); ?></small>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $customer['total_payments']; ?></td>
                                                    <td><strong>৳<?php echo number_format($customer['total_spent'], 2); ?></strong></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueData = {
    labels: [
        <?php
        foreach ($daily_revenue as $day) {
            echo "'" . date('M j', strtotime($day['date'])) . "',";
        }
        ?>
    ],
    datasets: [{
        label: 'Revenue (৳)',
        data: [
            <?php
            foreach ($daily_revenue as $day) {
                echo $day['revenue'] . ",";
            }
            ?>
        ],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
    }]
};

new Chart(revenueCtx, {
    type: 'line',
    data: revenueData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '৳' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Payment Method Chart
const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
const paymentMethodData = {
    labels: [
        <?php
        foreach ($payment_method_stats as $method) {
            echo "'" . htmlspecialchars($method['display_name']) . "',";
        }
        ?>
    ],
    datasets: [{
        data: [
            <?php
            foreach ($payment_method_stats as $method) {
                echo $method['revenue'] . ",";
            }
            ?>
        ],
        backgroundColor: [
            '#3b82f6',
            '#10b981',
            '#f59e0b',
            '#ef4444',
            '#8b5cf6',
            '#06b6d4'
        ]
    }]
};

new Chart(paymentMethodCtx, {
    type: 'doughnut',
    data: paymentMethodData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<style>
/* Stat cards styling */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon .text-success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stat-icon .text-primary {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-icon .text-info {
    background: rgba(6, 182, 212, 0.1);
    color: #06b6d4;
}

.stat-icon .text-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #111827;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

.stat-content small {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* Dashboard card styling */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

/* Chart containers */
#revenueChart {
    height: 300px !important;
}

#paymentMethodChart {
    height: 250px !important;
}

/* Table improvements */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

/* Form improvements */
.form-select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
    }

    .header-actions .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    #revenueChart, #paymentMethodChart {
        height: 200px !important;
    }
}

/* Empty state styling */
.text-center i {
    opacity: 0.5;
}

.text-muted {
    color: #6b7280 !important;
}
</style>

<?php include 'includes/footer.php'; ?>
