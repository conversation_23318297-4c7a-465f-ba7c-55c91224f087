<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activity.ServersActivity">

    <include
        android:id="@+id/toolbar"
        android:visibility="gone"
        layout="@layout/toolbar" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/ad_container_servers"
        android:layout_below="@id/toolbar" />

    <LinearLayout
        android:id="@+id/ad_container_servers"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="3dp"
        android:orientation="vertical" />

</RelativeLayout>