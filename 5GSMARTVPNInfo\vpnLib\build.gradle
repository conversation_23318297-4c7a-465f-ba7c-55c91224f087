apply plugin: 'com.android.library'
android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        dex {
            useLegacyPackaging true
        }
        jniLibs {
            useLegacyPackaging true
        }
        packagingOptions {
            exclude "jniLibs/**"
        }
    }



    buildFeatures {
        aidl true
    }
    namespace 'de.blinkt.openvpn'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.localbroadcastmanager
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation libs.appcompat
    implementation libs.work.runtime
}
