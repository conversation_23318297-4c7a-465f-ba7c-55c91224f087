package com.official.fivegfastvpn.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import java.util.regex.Pattern;

/**
 * Utility class for smart URL detection and action button generation
 * Supports Google Play Store, Apple App Store, and website URLs
 */
public class UrlDetectionUtils {
    private static final String TAG = "UrlDetectionUtils";
    
    // URL patterns for different types
    private static final Pattern PLAY_STORE_PATTERN = Pattern.compile(
        ".*play\\.google\\.com/store/apps/details\\?id=.*", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern APP_STORE_PATTERN = Pattern.compile(
        ".*(apps\\.apple\\.com|itunes\\.apple\\.com)/.*", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern WEBSITE_PATTERN = Pattern.compile(
        "^https?://.*", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * URL types enum
     */
    public enum UrlType {
        PLAYSTORE("playstore", "Install"),
        APPSTORE("appstore", "Install"),
        WEBSITE("website", "Visit"),
        OTHER("other", "Open");
        
        private final String type;
        private final String defaultButtonText;
        
        UrlType(String type, String defaultButtonText) {
            this.type = type;
            this.defaultButtonText = defaultButtonText;
        }
        
        public String getType() {
            return type;
        }
        
        public String getDefaultButtonText() {
            return defaultButtonText;
        }
    }
    
    /**
     * Detect URL type from URL string
     * @param url The URL to analyze
     * @return UrlType enum value
     */
    public static UrlType detectUrlType(String url) {
        if (url == null || url.trim().isEmpty()) {
            return UrlType.OTHER;
        }
        
        String cleanUrl = url.trim().toLowerCase();
        
        if (PLAY_STORE_PATTERN.matcher(cleanUrl).matches()) {
            return UrlType.PLAYSTORE;
        } else if (APP_STORE_PATTERN.matcher(cleanUrl).matches()) {
            return UrlType.APPSTORE;
        } else if (WEBSITE_PATTERN.matcher(cleanUrl).matches()) {
            return UrlType.WEBSITE;
        } else {
            return UrlType.OTHER;
        }
    }
    
    /**
     * Get appropriate button text based on URL type
     * @param url The URL to analyze
     * @param customButtonText Custom button text from API (optional)
     * @return Button text string
     */
    public static String getButtonText(String url, String customButtonText) {
        // Use custom button text if provided
        if (customButtonText != null && !customButtonText.trim().isEmpty()) {
            return customButtonText.trim();
        }
        
        // Auto-detect based on URL
        UrlType urlType = detectUrlType(url);
        return urlType.getDefaultButtonText();
    }
    
    /**
     * Get appropriate button text based on URL type from API
     * @param urlType URL type from API response
     * @param customButtonText Custom button text from API (optional)
     * @return Button text string
     */
    public static String getButtonTextFromType(String urlType, String customButtonText) {
        // Use custom button text if provided
        if (customButtonText != null && !customButtonText.trim().isEmpty()) {
            return customButtonText.trim();
        }
        
        // Use type-based default
        if (urlType != null) {
            switch (urlType.toLowerCase()) {
                case "playstore":
                case "appstore":
                    return "Install";
                case "website":
                    return "Visit";
                default:
                    return "Open";
            }
        }
        
        return "Open";
    }
    
    /**
     * Open URL with appropriate action
     * @param context Android context
     * @param url URL to open
     * @param urlType URL type (optional, will auto-detect if null)
     * @return true if successfully opened, false otherwise
     */
    public static boolean openUrl(Context context, String url, String urlType) {
        if (context == null || url == null || url.trim().isEmpty()) {
            Log.e(TAG, "Invalid context or URL");
            return false;
        }
        
        try {
            String cleanUrl = url.trim();
            
            // Ensure URL has protocol
            if (!cleanUrl.startsWith("http://") && !cleanUrl.startsWith("https://")) {
                cleanUrl = "https://" + cleanUrl;
            }
            
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(cleanUrl));
            
            // For Play Store URLs, try to open in Play Store app first
            if (urlType != null && urlType.equals("playstore")) {
                String packageName = extractPlayStorePackageName(cleanUrl);
                if (packageName != null) {
                    try {
                        Intent playStoreIntent = new Intent(Intent.ACTION_VIEW, 
                            Uri.parse("market://details?id=" + packageName));
                        playStoreIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(playStoreIntent);
                        return true;
                    } catch (Exception e) {
                        Log.d(TAG, "Play Store app not available, opening in browser");
                    }
                }
            }
            
            // Open in browser or default app
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error opening URL: " + url, e);
            return false;
        }
    }
    
    /**
     * Extract package name from Play Store URL
     * @param url Play Store URL
     * @return Package name or null if not found
     */
    private static String extractPlayStorePackageName(String url) {
        try {
            Uri uri = Uri.parse(url);
            return uri.getQueryParameter("id");
        } catch (Exception e) {
            Log.e(TAG, "Error extracting package name from URL: " + url, e);
            return null;
        }
    }
    
    /**
     * Check if URL is valid
     * @param url URL to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String cleanUrl = url.trim();
        
        // Check if it's a valid HTTP/HTTPS URL
        if (WEBSITE_PATTERN.matcher(cleanUrl).matches()) {
            return true;
        }
        
        // Check if it looks like a URL without protocol
        return cleanUrl.contains(".") && !cleanUrl.contains(" ");
    }
    
    /**
     * Get icon resource ID based on URL type
     * @param urlType URL type
     * @return Resource ID for icon
     */
    public static int getIconResource(String urlType) {
        if (urlType != null) {
            switch (urlType.toLowerCase()) {
                case "playstore":
                    return android.R.drawable.ic_menu_add; // Replace with actual Play Store icon
                case "appstore":
                    return android.R.drawable.ic_menu_add; // Replace with actual App Store icon
                case "website":
                    return android.R.drawable.ic_menu_view; // Replace with actual web icon
                default:
                    return android.R.drawable.ic_menu_view;
            }
        }
        return android.R.drawable.ic_menu_view;
    }
    
    /**
     * Format URL for display (remove protocol, truncate if too long)
     * @param url Original URL
     * @param maxLength Maximum length for display
     * @return Formatted URL string
     */
    public static String formatUrlForDisplay(String url, int maxLength) {
        if (url == null || url.trim().isEmpty()) {
            return "";
        }
        
        String displayUrl = url.trim()
            .replaceFirst("^https?://", "")
            .replaceFirst("^www\\.", "");
        
        if (displayUrl.length() > maxLength) {
            displayUrl = displayUrl.substring(0, maxLength - 3) + "...";
        }
        
        return displayUrl;
    }
}
