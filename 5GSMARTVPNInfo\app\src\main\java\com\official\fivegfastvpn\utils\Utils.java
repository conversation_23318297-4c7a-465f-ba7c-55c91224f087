package com.official.fivegfastvpn.utils;

//Developer :--<PERSON><PERSON><PERSON>


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.browser.customtabs.CustomTabsIntent;

import com.android.billingclient.BuildConfig;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.api.Const;

import java.text.SimpleDateFormat;
import java.util.Date;

public class Utils {


    public static void openLink(String url, Context context) {

        if (url.contains("play.google.com/store/apps") || url.contains("market://")) {
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                // If it's a web URL, convert it to market URL
                if (url.contains("play.google.com")) {
                    String packageName = url.substring(url.indexOf("id=") + 3);
                    if (packageName.contains("&")) {
                        packageName = packageName.substring(0, packageName.indexOf("&"));
                    }
                    intent.setData(Uri.parse("market://details?id=" + packageName));
                } else {
                    intent.setData(Uri.parse(url));
                }
                intent.setPackage("com.android.vending");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                return;
            } catch (Exception e) {
                // If Play Store app is not installed, open in browser
                Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                browserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(browserIntent);
                return;
            }
        }

        if (url.contains("youtube.com") || url.contains("youtu.be")) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setPackage("com.google.android.youtube");

            try {
                context.startActivity(intent);

            } catch (Exception e) {

                Intent defaultBrowser = Intent.makeMainSelectorActivity(Intent.ACTION_MAIN, Intent.CATEGORY_APP_BROWSER);
                defaultBrowser.setData(Uri.parse(url));
                try {
                    context.startActivity(defaultBrowser);
                } catch (Exception ex) {
                    Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    try {
                        context.startActivity(browserIntent);
                    } catch (Exception ee) {
                        Toast.makeText(context, "Not Available", Toast.LENGTH_SHORT).show();
                    }
                }
            }

        } else {

            try {
                CustomTabsIntent.Builder builder = new CustomTabsIntent.Builder();
                CustomTabsIntent customTabsIntent = builder.build();
                customTabsIntent.intent.setPackage("com.android.chrome");
                builder.setToolbarColor(context.getResources().getColor(R.color.colorPrimary));
                builder.setSecondaryToolbarColor(context.getResources().getColor(R.color.white));
                builder.setShowTitle(true);
                builder.addDefaultShareMenuItem();
                builder.build().launchUrl(context, Uri.parse(url));

            } catch (Exception e) {

                Intent defaultBrowser = Intent.makeMainSelectorActivity(Intent.ACTION_MAIN, Intent.CATEGORY_APP_BROWSER);
                defaultBrowser.setData(Uri.parse(url));
                try {
                    context.startActivity(defaultBrowser);
                } catch (Exception eee) {
                    Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    try {
                        context.startActivity(browserIntent);
                    } catch (Exception ee) {
                        Toast.makeText(context, "Not Available", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        }
    }


    public static String getImgURL(int resourceId) {

        return Uri.parse("android.resource://" + BuildConfig.APPLICATION_ID + "/" + resourceId).toString();
    }

    public static String imgUrl(String path, String url) {

        String image;
        if (url.startsWith("http") || url.contains("https://") || url.contains("http://")) {
            image = url;
        } else {
            image = Const.base + path + url;
        }
        return image;
    }

    public static void setTools(String title, Activity context) {
        TextView ttl = context.findViewById(R.id.tool_title);
        ttl.setText(title);

        ImageView back = context.findViewById(R.id.tool_back);
        back.setOnClickListener(v -> {
            context.finish();
        });
    }


    public static void startAnim(RelativeLayout rotate, Context activity) {
        Animation a = AnimationUtils.loadAnimation(activity, R.anim.rotate);
        a.setDuration(1000);
        rotate.startAnimation(a);
        a.setInterpolator(input -> {
            int frameCount = 8;
            return (float) Math.floor(input * frameCount) / frameCount;
        });
    }

    public static boolean isDateInRange(String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            Date now = new Date();
            
            // Add one day to end date to make it inclusive
            end.setTime(end.getTime() + 24*60*60*1000);
            
            return !now.before(start) && !now.after(end);
        } catch (Exception e) {
            e.printStackTrace();
            // If there's any error parsing dates, return true to show the ad
            return true;
        }
    }

}
