# Database Issues Fix for Custom Ads System

## Issues Fixed

This fix resolves the following database-related errors in the custom ads system:

### 1. customer-accounts.php (Line 123)
**Error:** `Unknown column 'ca.created_at' in 'order clause'`
**Cause:** The `custom_ads` table was missing the `created_at` column
**Fix:** Added column existence check and fallback query

### 2. custom-ads-analytics.php (Line 62)
**Error:** `Unknown column 'ca.created_at' in 'field list'`
**Cause:** Missing `created_at` and `is_approved` columns in `custom_ads` table
**Fix:** Added dynamic column checking and conditional queries

### 3. payment-methods.php (Line 213)
**Error:** `Undefined array key "account_number"` and `htmlspecialchars(): Passing null to parameter`
**Cause:** Null values in database fields
**Fix:** Added null coalescing operators (`??`) to handle null values

## Files Modified

1. **customer-accounts.php** - Added column existence checks for safer queries
2. **custom-ads-analytics.php** - Added dynamic column detection and fallback queries
3. **payment-methods.php** - Added null value handling for account information
4. **database_migration_fix.sql** - Complete database migration script
5. **run_migration.php** - Web interface to run the migration safely

## How to Fix

### Option 1: Run Database Migration (Recommended)

1. Open your browser and go to: `http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/run_migration.php`
2. Click "Run Database Migration" button
3. The script will safely add all missing tables and columns

### Option 2: Manual SQL Execution

1. Open phpMyAdmin or your MySQL client
2. Select your database
3. Run the SQL script: `database_migration_fix.sql`

## What the Migration Does

### Creates Missing Tables:
- `customer_accounts` - For managing customer information
- `customer_payments` - For tracking payments and transactions
- `payment_methods` - For configuring payment options
- `ad_packages` - For defining ad packages and pricing

### Adds Missing Columns to `custom_ads`:
- `customer_id` - Links ads to customers
- `created_at` - Timestamp for ad creation
- `updated_at` - Timestamp for last update
- `is_approved` - Approval status for ads
- `package_id` - Links ads to packages
- `payment_id` - Links ads to payments

### Inserts Default Data:
- Default payment methods (bKash, Nagad, Rocket, Google Pay, PayPal)
- Default ad packages (Basic, Premium, Extended)

## Safety Features

- **Safe to run multiple times** - Uses `IF NOT EXISTS` and column existence checks
- **No data loss** - Only adds missing structures, doesn't modify existing data
- **Backward compatible** - Code works both before and after migration
- **Error handling** - Graceful fallbacks for missing columns

## Verification

After running the migration, verify the fixes by visiting:

1. `http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/customer-accounts.php`
2. `http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/custom-ads-analytics.php`
3. `http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/payment-methods.php`

All pages should now load without errors.

## Technical Details

### Column Existence Checking
The code now checks if columns exist before using them:
```php
$columns_check = mysqli_query($conn, "SHOW COLUMNS FROM custom_ads LIKE 'created_at'");
$has_created_at = mysqli_num_rows($columns_check) > 0;
```

### Null Value Handling
Added null coalescing operators for safe value access:
```php
echo htmlspecialchars($method['account_number'] ?? '');
```

### Conditional Queries
Different queries based on available columns:
```php
if ($has_created_at && $has_is_approved) {
    // Full query with all columns
} else {
    // Fallback query with default values
}
```

## Support

If you encounter any issues after running the migration:

1. Check the migration results in the web interface
2. Verify database structure in phpMyAdmin
3. Check PHP error logs for any remaining issues
4. Ensure all files have been updated with the latest code

The system is now more robust and will handle missing database structures gracefully.
