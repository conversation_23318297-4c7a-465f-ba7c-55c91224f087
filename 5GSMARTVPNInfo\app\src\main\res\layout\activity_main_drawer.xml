<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">


    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/drawer_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:openDrawer="start">


        <include
            layout="@layout/activity_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


        <com.google.android.material.navigation.NavigationView
            android:id="@+id/nav_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:background="@color/FF5722"
            android:fitsSystemWindows="true"
            app:headerLayout="@layout/nav_header"
            app:itemIconTint="@color/white"
            app:itemTextColor="@color/white"
            app:menu="@menu/main_menu" />

    </androidx.drawerlayout.widget.DrawerLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>