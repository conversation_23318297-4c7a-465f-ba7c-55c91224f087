<?php
/**
 * Debug Firebase Notifications System
 * Comprehensive testing and debugging for Firebase v1 API
 */

session_start();
require_once 'includes/config.php';

echo "<h2>Firebase Notifications Debug System</h2>";

try {
    // Check database connection
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // 1. Check Firebase Configuration Files
    echo "<h3>1. Firebase Configuration Check</h3>";
    
    $service_account_file = __DIR__ . '/notifications/service-account-file.json';
    $firebase_config_file = __DIR__ . '/notifications/firebase-config.json';
    
    echo "<h4>1.1 Service Account File</h4>";
    if (file_exists($service_account_file)) {
        echo "<p style='color: green;'>✓ Service account file exists: " . htmlspecialchars($service_account_file) . "</p>";
        
        $service_account_content = file_get_contents($service_account_file);
        $service_account_data = json_decode($service_account_content, true);
        
        if ($service_account_data) {
            echo "<p style='color: green;'>✓ Service account file is valid JSON</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            $safe_fields = ['type', 'project_id', 'client_email', 'client_id', 'auth_uri', 'token_uri'];
            foreach ($safe_fields as $field) {
                if (isset($service_account_data[$field])) {
                    echo "<tr><td><strong>$field</strong></td><td>" . htmlspecialchars($service_account_data[$field]) . "</td></tr>";
                }
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ Service account file contains invalid JSON</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Service account file missing: " . htmlspecialchars($service_account_file) . "</p>";
        echo "<p><strong>Solution:</strong> Download the service account key from Firebase Console and place it at the above path.</p>";
    }

    // 2. Check Notifications Table
    echo "<h3>2. Database Schema Check</h3>";
    
    $table_check = $conn->query("SHOW TABLES LIKE 'notifications'");
    if ($table_check && $table_check->num_rows > 0) {
        echo "<p style='color: green;'>✓ Notifications table exists</p>";
        
        // Check table structure
        $columns_result = $conn->query("DESCRIBE notifications");
        if ($columns_result) {
            echo "<h4>2.1 Table Structure</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($col = $columns_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Default'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check recent notifications
        echo "<h4>2.2 Recent Notifications</h4>";
        $recent_notifications = $conn->query("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 10");
        if ($recent_notifications && $recent_notifications->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr><th>ID</th><th>Title</th><th>Status</th><th>Response</th><th>Created</th></tr>";
            while ($row = $recent_notifications->fetch_assoc()) {
                $status_color = $row['status'] === 'sent' ? 'green' : ($row['status'] === 'error' ? 'red' : 'orange');
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['title']) . "</td>";
                echo "<td style='color: $status_color;'>" . htmlspecialchars($row['status']) . "</td>";
                echo "<td>" . htmlspecialchars(substr($row['response'] ?? '', 0, 100)) . "...</td>";
                echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>No notifications found in database</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Notifications table missing</p>";
    }

    // 3. Test Firebase Functions
    echo "<h3>3. Firebase Functions Test</h3>";
    
    if (file_exists(__DIR__ . '/notifications/includes/notification_functions.php')) {
        echo "<p style='color: green;'>✓ Notification functions file exists</p>";
        
        require_once __DIR__ . '/notifications/includes/notification_functions.php';
        
        if (function_exists('sendFCMNotification')) {
            echo "<p style='color: green;'>✓ sendFCMNotification function available</p>";
        } else {
            echo "<p style='color: red;'>✗ sendFCMNotification function not found</p>";
        }
        
        if (function_exists('getAccessToken')) {
            echo "<p style='color: green;'>✓ getAccessToken function available</p>";
        } else {
            echo "<p style='color: red;'>✗ getAccessToken function not found</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Notification functions file missing</p>";
    }

    // 4. Test Firebase v1 Class
    echo "<h3>4. Firebase v1 Class Test</h3>";
    
    if (file_exists(__DIR__ . '/notifications/includes/firebase_v1.php')) {
        echo "<p style='color: green;'>✓ Firebase v1 class file exists</p>";
        
        require_once __DIR__ . '/notifications/includes/firebase_v1.php';
        
        if (class_exists('FirebaseV1')) {
            echo "<p style='color: green;'>✓ FirebaseV1 class available</p>";
            
            if (file_exists($service_account_file)) {
                try {
                    $firebase = new FirebaseV1($service_account_data['project_id'] ?? 'test-project', $service_account_file);
                    echo "<p style='color: green;'>✓ FirebaseV1 instance created successfully</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ Failed to create FirebaseV1 instance: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ FirebaseV1 class not found</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Firebase v1 class file missing</p>";
    }

    // 5. Test Network Connectivity
    echo "<h3>5. Network Connectivity Test</h3>";
    
    $test_urls = [
        'Firebase FCM' => 'https://fcm.googleapis.com',
        'Google OAuth' => 'https://oauth2.googleapis.com',
        'Google APIs' => 'https://www.googleapis.com'
    ];
    
    foreach ($test_urls as $name => $url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code >= 200 && $http_code < 400) {
            echo "<p style='color: green;'>✓ $name connectivity: OK (HTTP $http_code)</p>";
        } else {
            echo "<p style='color: red;'>✗ $name connectivity: Failed (HTTP $http_code)</p>";
        }
    }

    // 6. Quick Test Actions
    echo "<h3>6. Quick Test Actions</h3>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='test_notification' style='background: blue; color: white; padding: 10px; border: none; border-radius: 5px;'>Send Test Notification</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='test_access_token' style='background: green; color: white; padding: 10px; border: none; border-radius: 5px;'>Test Access Token</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='create_test_table' style='background: orange; color: white; padding: 10px; border: none; border-radius: 5px;'>Create/Fix Notifications Table</button>";
    echo "</form>";

    // Handle test actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['test_notification'])) {
            echo "<h4>Test Notification Result:</h4>";
            
            if (function_exists('sendFCMNotification')) {
                $test_title = "Test Notification - " . date('H:i:s');
                $test_message = "This is a test notification sent at " . date('Y-m-d H:i:s');
                
                $result = sendFCMNotification($test_title, $test_message, 'all', 'immediate', null, null, [
                    'test' => true,
                    'debug' => true,
                    'timestamp' => time()
                ]);
                
                echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
                echo json_encode($result, JSON_PRETTY_PRINT);
                echo "</pre>";
            } else {
                echo "<p style='color: red;'>✗ sendFCMNotification function not available</p>";
            }
        }
        
        if (isset($_POST['test_access_token'])) {
            echo "<h4>Access Token Test Result:</h4>";
            
            if (function_exists('getAccessToken') && file_exists($service_account_file)) {
                try {
                    $token = getAccessToken($service_account_file);
                    if ($token) {
                        echo "<p style='color: green;'>✓ Access token obtained successfully</p>";
                        echo "<p><strong>Token (first 50 chars):</strong> " . htmlspecialchars(substr($token, 0, 50)) . "...</p>";
                    } else {
                        echo "<p style='color: red;'>✗ Failed to obtain access token</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ Error getting access token: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ getAccessToken function or service account file not available</p>";
            }
        }
        
        if (isset($_POST['create_test_table'])) {
            echo "<h4>Table Creation Result:</h4>";
            
            $create_table_sql = "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('pending', 'sent', 'error', 'scheduled') DEFAULT 'pending',
                response TEXT,
                sent_to VARCHAR(100) DEFAULT 'all',
                notification_type VARCHAR(50) DEFAULT 'general',
                schedule_type VARCHAR(20) DEFAULT 'immediate',
                scheduled_time DATETIME DEFAULT NULL,
                recurring_interval VARCHAR(20) DEFAULT NULL,
                next_run_time DATETIME DEFAULT NULL,
                last_run_time DATETIME DEFAULT NULL,
                data JSON DEFAULT NULL,
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                category VARCHAR(50) DEFAULT 'general',
                target_audience VARCHAR(100) DEFAULT 'all_users',
                delivery_count INT DEFAULT 0,
                success_count INT DEFAULT 0,
                failure_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if ($conn->query($create_table_sql)) {
                echo "<p style='color: green;'>✓ Notifications table created/updated successfully</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create notifications table: " . $conn->error . "</p>";
            }
        }
        
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Troubleshooting Links</h3>";
echo "<ul>";
echo "<li><a href='notifications.php'>Notifications Management</a></li>";
echo "<li><a href='debug_custom_ads.php'>Debug Custom Ads</a></li>";
echo "<li><a href='test_api_with_auth.php'>Test API with Auth</a></li>";
echo "</ul>";

$conn->close();
?>
