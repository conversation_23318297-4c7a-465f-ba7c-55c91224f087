<?php
/**
 * Admin Panel - Payment Verification
 * Verify and manage customer payments for custom ads
 */

session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$page_title = "Payment Verification";
$success = '';
$error = '';

// Handle payment verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['verify_payment'])) {
        $payment_id = (int)$_POST['payment_id'];
        $action = $_POST['action']; // 'approve' or 'reject'
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if ($action === 'approve') {
            // Start transaction
            mysqli_begin_transaction($conn);
            
            try {
                // Update payment status
                $stmt = $conn->prepare("UPDATE customer_payments SET payment_status = 'verified', admin_notes = ?, verified_by = ?, verified_at = NOW() WHERE id = ?");
                $stmt->bind_param("sii", $admin_notes, $_SESSION['admin_id'], $payment_id);
                $stmt->execute();
                
                // Get payment details
                $stmt = $conn->prepare("
                    SELECT cp.*, ap.duration_days, ap.max_ads, ca.whatsapp_number 
                    FROM customer_payments cp 
                    JOIN ad_packages ap ON cp.package_id = ap.id 
                    JOIN customer_accounts ca ON cp.customer_id = ca.id 
                    WHERE cp.id = ?
                ");
                $stmt->bind_param("i", $payment_id);
                $stmt->execute();
                $payment = $stmt->get_result()->fetch_assoc();
                
                if ($payment) {
                    // Update customer total spent
                    $stmt = $conn->prepare("UPDATE customer_accounts SET total_spent = total_spent + ?, total_orders = total_orders + 1 WHERE id = ?");
                    $stmt->bind_param("di", $payment['amount'], $payment['customer_id']);
                    $stmt->execute();
                    
                    // Update provider total spent if exists
                    $stmt = $conn->prepare("UPDATE custom_ads_providers SET total_spent = total_spent + ? WHERE whatsapp_number = ?");
                    $stmt->bind_param("ds", $payment['amount'], $payment['whatsapp_number']);
                    $stmt->execute();
                    
                    // Auto-approve ads if setting is enabled
                    $auto_approve = getSetting($conn, 'auto_approve_ads', false);
                    if ($auto_approve) {
                        // This would be implemented when creating ads
                        // For now, just log the approval
                    }
                }
                
                mysqli_commit($conn);
                $success = "Payment approved successfully!";
                
            } catch (Exception $e) {
                mysqli_rollback($conn);
                $error = "Failed to approve payment: " . $e->getMessage();
            }
            
        } elseif ($action === 'reject') {
            $stmt = $conn->prepare("UPDATE customer_payments SET payment_status = 'rejected', admin_notes = ?, verified_by = ?, verified_at = NOW() WHERE id = ?");
            $stmt->bind_param("sii", $admin_notes, $_SESSION['admin_id'], $payment_id);
            
            if ($stmt->execute()) {
                $success = "Payment rejected successfully!";
            } else {
                $error = "Failed to reject payment.";
            }
        }
    }
}

// Check if required tables exist
$customer_payments_exists = mysqli_query($conn, "SHOW TABLES LIKE 'customer_payments'");
$customer_accounts_exists = mysqli_query($conn, "SHOW TABLES LIKE 'customer_accounts'");
$ad_packages_exists = mysqli_query($conn, "SHOW TABLES LIKE 'ad_packages'");
$payment_methods_exists = mysqli_query($conn, "SHOW TABLES LIKE 'payment_methods'");

// Get pending payments
$pending_payments = [];
if (mysqli_num_rows($customer_payments_exists) > 0 &&
    mysqli_num_rows($customer_accounts_exists) > 0 &&
    mysqli_num_rows($ad_packages_exists) > 0) {

    try {
        $stmt = $conn->prepare("
            SELECT
                cp.*,
                ca.whatsapp_number,
                ca.customer_name,
                ap.package_name,
                ap.duration_days,
                pm.display_name as payment_method_name
            FROM customer_payments cp
            JOIN customer_accounts ca ON cp.customer_id = ca.id
            JOIN ad_packages ap ON cp.package_id = ap.id
            LEFT JOIN payment_methods pm ON cp.payment_method = pm.method_code
            WHERE cp.payment_status = 'pending'
            ORDER BY cp.created_at DESC
        ");
        $stmt->execute();
        $pending_payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    } catch (mysqli_sql_exception $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Get recent verified payments
$recent_payments = [];
if (mysqli_num_rows($customer_payments_exists) > 0 &&
    mysqli_num_rows($customer_accounts_exists) > 0 &&
    mysqli_num_rows($ad_packages_exists) > 0) {

    try {
        $stmt = $conn->prepare("
            SELECT
                cp.*,
                ca.whatsapp_number,
                ca.customer_name,
                ap.package_name,
                pm.display_name as payment_method_name
            FROM customer_payments cp
            JOIN customer_accounts ca ON cp.customer_id = ca.id
            JOIN ad_packages ap ON cp.package_id = ap.id
            LEFT JOIN payment_methods pm ON cp.payment_method = pm.method_code
            WHERE cp.payment_status IN ('verified', 'rejected')
            ORDER BY cp.verified_at DESC
            LIMIT 20
        ");
        $stmt->execute();
        $recent_payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    } catch (mysqli_sql_exception $e) {
        // Silently handle error for recent payments
    }
}

// Get payment statistics
$stats = [];
$stats_result = mysqli_query($conn, "
    SELECT 
        payment_status,
        COUNT(*) as count,
        SUM(amount) as total_amount
    FROM customer_payments 
    GROUP BY payment_status
");
while ($row = mysqli_fetch_assoc($stats_result)) {
    $stats[$row['payment_status']] = $row;
}

// Helper function to get setting
function getSetting($conn, $key, $default = null) {
    $stmt = $conn->prepare("SELECT setting_value FROM custom_ads_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    return $result ? $result['setting_value'] : $default;
}

// Add Bootstrap CSS and JS for modals
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
];

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Payment Verification</h1>
                <p class="page-subtitle">Review and verify customer payments</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="ri-refresh-line me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php
            $missing_tables = [];
            if (mysqli_num_rows($customer_payments_exists) == 0) $missing_tables[] = 'customer_payments';
            if (mysqli_num_rows($customer_accounts_exists) == 0) $missing_tables[] = 'customer_accounts';
            if (mysqli_num_rows($ad_packages_exists) == 0) $missing_tables[] = 'ad_packages';
            if (mysqli_num_rows($payment_methods_exists) == 0) $missing_tables[] = 'payment_methods';

            if (!empty($missing_tables)): ?>
                <div class="alert alert-warning alert-dismissible fade show">
                    <i class="ri-database-2-line me-2"></i>
                    <strong>Database Setup Required!</strong> Some tables are missing: <?php echo implode(', ', $missing_tables); ?>.
                    Payment verification may not work properly.
                    <a href="run_migration.php" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="ri-tools-line me-1"></i>Run Database Migration
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-time-line text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['pending']['count'] ?? 0; ?></h3>
                            <p>Pending Payments</p>
                            <small>৳<?php echo number_format($stats['pending']['total_amount'] ?? 0, 2); ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-check-line text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['verified']['count'] ?? 0; ?></h3>
                            <p>Verified Payments</p>
                            <small>৳<?php echo number_format($stats['verified']['total_amount'] ?? 0, 2); ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-close-line text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['rejected']['count'] ?? 0; ?></h3>
                            <p>Rejected Payments</p>
                            <small>৳<?php echo number_format($stats['rejected']['total_amount'] ?? 0, 2); ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-refund-line text-info"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['refunded']['count'] ?? 0; ?></h3>
                            <p>Refunded Payments</p>
                            <small>৳<?php echo number_format($stats['refunded']['total_amount'] ?? 0, 2); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="ri-time-line me-2"></i>Pending Payments
                        <span class="badge bg-warning ms-2"><?php echo count($pending_payments); ?></span>
                    </h3>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_payments)): ?>
                        <div class="text-center py-4">
                            <i class="ri-check-double-line text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Pending Payments</h5>
                            <p class="text-muted">All payments have been processed.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Package</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Transaction ID</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_payments as $payment): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($payment['customer_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($payment['whatsapp_number']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($payment['package_name']); ?><br>
                                                <small class="text-muted"><?php echo $payment['duration_days']; ?> days</small>
                                            </td>
                                            <td>
                                                <strong><?php echo $payment['currency']; ?> <?php echo number_format($payment['amount'], 2); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($payment['payment_method_name'] ?? $payment['payment_method']); ?></span>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                                <?php if ($payment['payment_proof']): ?>
                                                    <br><small class="text-muted" title="<?php echo htmlspecialchars($payment['payment_proof']); ?>">
                                                        <i class="ri-file-text-line"></i> Has proof
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?><br>
                                                <small class="text-muted"><?php echo time_elapsed_string($payment['created_at']); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-success" onclick="verifyPayment(<?php echo $payment['id']; ?>, 'approve', '<?php echo htmlspecialchars($payment['customer_name']); ?>')">
                                                        <i class="ri-check-line"></i> Approve
                                                    </button>
                                                    <button class="btn btn-danger" onclick="verifyPayment(<?php echo $payment['id']; ?>, 'reject', '<?php echo htmlspecialchars($payment['customer_name']); ?>')">
                                                        <i class="ri-close-line"></i> Reject
                                                    </button>
                                                    <button class="btn btn-info" onclick="viewPaymentDetails(<?php echo htmlspecialchars(json_encode($payment)); ?>)">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="ri-history-line me-2"></i>Recent Processed Payments
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Package</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Processed</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_payments as $payment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($payment['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($payment['package_name']); ?></td>
                                        <td><?php echo $payment['currency']; ?> <?php echo number_format($payment['amount'], 2); ?></td>
                                        <td>
                                            <?php if ($payment['payment_status'] === 'verified'): ?>
                                                <span class="badge bg-success">Verified</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M j, g:i A', strtotime($payment['verified_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Payment Verification Modal -->
<div class="modal fade" id="verificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verificationModalTitle">Verify Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="verificationForm">
                <input type="hidden" name="payment_id" id="verification_payment_id">
                <input type="hidden" name="action" id="verification_action">
                <div class="modal-body">
                    <div id="verification_customer_info"></div>
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="admin_notes" rows="3" placeholder="Add notes about this verification..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="verify_payment" class="btn" id="verification_submit_btn">Verify</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function verifyPayment(paymentId, action, customerName) {
    document.getElementById('verification_payment_id').value = paymentId;
    document.getElementById('verification_action').value = action;
    
    const modal = document.getElementById('verificationModal');
    const title = document.getElementById('verificationModalTitle');
    const submitBtn = document.getElementById('verification_submit_btn');
    const customerInfo = document.getElementById('verification_customer_info');
    
    if (action === 'approve') {
        title.textContent = 'Approve Payment';
        submitBtn.textContent = 'Approve Payment';
        submitBtn.className = 'btn btn-success';
        customerInfo.innerHTML = '<div class="alert alert-success">Approving payment for: <strong>' + customerName + '</strong></div>';
    } else {
        title.textContent = 'Reject Payment';
        submitBtn.textContent = 'Reject Payment';
        submitBtn.className = 'btn btn-danger';
        customerInfo.innerHTML = '<div class="alert alert-danger">Rejecting payment for: <strong>' + customerName + '</strong></div>';
    }
    
    new bootstrap.Modal(modal).show();
}

function viewPaymentDetails(payment) {
    // Implementation for viewing payment details
    alert('Payment Details:\n' + JSON.stringify(payment, null, 2));
}

function time_elapsed_string(datetime) {
    // Simple time elapsed function - you can implement this in PHP instead
    return 'recently';
}
</script>

<style>
/* Stat cards styling */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(59, 130, 246, 0.1);
}

.stat-icon .text-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.stat-icon .text-success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stat-icon .text-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.stat-icon .text-info {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #111827;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

.stat-content small {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* Dashboard card styling */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Table improvements */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Alert improvements */
.alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 1.5rem;
}

/* Badge improvements */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}
</style>

<?php
// Simple time elapsed function
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    // Calculate weeks manually to avoid deprecated property warning
    $weeks = floor($diff->d / 7);
    $days = $diff->d - ($weeks * 7);

    $string = array();

    if ($diff->y) $string['y'] = $diff->y . ' year' . ($diff->y > 1 ? 's' : '');
    if ($diff->m) $string['m'] = $diff->m . ' month' . ($diff->m > 1 ? 's' : '');
    if ($weeks) $string['w'] = $weeks . ' week' . ($weeks > 1 ? 's' : '');
    if ($days) $string['d'] = $days . ' day' . ($days > 1 ? 's' : '');
    if ($diff->h) $string['h'] = $diff->h . ' hour' . ($diff->h > 1 ? 's' : '');
    if ($diff->i) $string['i'] = $diff->i . ' minute' . ($diff->i > 1 ? 's' : '');
    if ($diff->s) $string['s'] = $diff->s . ' second' . ($diff->s > 1 ? 's' : '');

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' ago' : 'just now';
}

include 'includes/footer.php'; 
?>
