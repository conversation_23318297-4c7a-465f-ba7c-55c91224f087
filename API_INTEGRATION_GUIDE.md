# 5G Smart VPN - PHP API Integration Guide

## Overview
This document provides a comprehensive guide for integrating the PHP API located at `5GsmartvpnAdminPanel/admin_new/api/` into the Android application.

## Base URL
```
http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/
```

## Authentication
All API endpoints (except `ip.php` and `status.php`) require HMAC-SHA256 authentication:

### Required Parameters:
- `timestamp`: Current timestamp in seconds
- `signature`: HMAC-SHA256 hash of timestamp using API secret key

### API Secret Key:
Update `API_SECRET_KEY` in `Const.java` to match the server configuration:
```java
public static final String API_SECRET_KEY = "your-secret-api-key-here";
```

## API Endpoints

### 1. Configuration API (`config.php`)
**Purpose**: Get complete app configuration including ads and servers
**Method**: GET
**Authentication**: Required
**Parameters**: 
- `pkg`: App package name

**Response Structure**:
```json
[{
  "admob_id": "ca-app-pub-...",
  "admob_banner": "ca-app-pub-...",
  "admob_interstitial": "ca-app-pub-...",
  "admob_native": "ca-app-pub-...",
  "admob_rewarded": "ca-app-pub-...",
  "admob_openad": "ca-app-pub-...",
  "facebook_id": "",
  "facebook_banner": "IMG_16_9_APP_INSTALL#...",
  "facebook_interstitial": "IMG_16_9_APP_INSTALL#...",
  "facebook_native": "IMG_16_9_APP_INSTALL#...",
  "facebook_rewarded": "IMG_16_9_APP_INSTALL#...",
  "ads_status": 1,
  "banner_type": "admob",
  "interstitial_type": "admob",
  "native_type": "admob",
  "rewarded_type": "admob",
  "banner_enabled": 1,
  "interstitial_enabled": 1,
  "rewarded_enabled": 0,
  "native_enabled": 0,
  "openad_enabled": 1,
  "reward_time": 30,
  "click_limit": 5,
  "show_frequency": 3,
  "test_mode": 1,
  "servers": [...],
  "api_version": "3.0",
  "timestamp": **********,
  "source": "modern_admin_panel",
  "endpoint": "config"
}]
```

### 2. Servers API (`servers.php`)
**Purpose**: Get VPN servers list
**Method**: GET
**Authentication**: Required
**Parameters**:
- `status`: Server status (1=active, 0=inactive)
- `limit`: Maximum number of servers to return
- `order`: Order by field (id, name, pos, status)
- `dir`: Order direction (ASC, DESC)

**Response Structure**:
```json
{
  "success": true,
  "servers": [{
    "id": 2,
    "name": "Singapore",
    "username": "...",
    "password": "...",
    "configFile": "...",
    "flagURL": "singapore.png",
    "type": 0,
    "pos": 0,
    "status": 1
  }],
  "metadata": {
    "total_servers": 1,
    "returned_servers": 1,
    "status_filter": 1,
    "order_by": "pos",
    "order_direction": "DESC",
    "limit": null,
    "api_version": "3.0",
    "timestamp": **********,
    "source": "modern_admin_panel",
    "endpoint": "servers"
  }
}
```

### 3. Custom Ads API (`custom_ads.php`)
**Purpose**: Get random active custom advertisement
**Method**: GET
**Authentication**: Required

**Response Structure**:
```json
[{
  "id": 1,
  "title": "Testing notification",
  "image": "images/bannerBg_1741973854.png",
  "text": "tee",
  "url": "https://google.com",
  "date_start": "2025-04-26",
  "date_end": "2025-04-28",
  "on": 1,
  "view_count": 6,
  "click_count": 2,
  "api_version": "3.0",
  "timestamp": **********,
  "source": "modern_admin_panel",
  "endpoint": "custom_ads",
  "total_active_ads": 2
}]
```

### 4. Ad Tracking API (`track_ad.php`)
**Purpose**: Track ad views and clicks
**Method**: POST
**Authentication**: Not required
**Content-Type**: application/json

**Request Body**:
```json
{
  "ad_id": 1,
  "type": "view", // or "click"
  "ad_type": "custom", // "custom", "admob", "facebook"
  "user_id": "optional_user_id",
  "device_id": "optional_device_id"
}
```

**Response Structure**:
```json
{
  "success": true,
  "message": "Custom ad view count updated successfully",
  "timestamp": **********,
  "api_version": "3.0",
  "source": "modern_admin_panel",
  "endpoint": "track_ad",
  "data": {
    "ad_id": 1,
    "ad_type": "custom",
    "event_type": "view",
    "user_id": null,
    "device_id": "android_device_123"
  }
}
```

### 5. IP Detection API (`ip.php`)
**Purpose**: Get client IP address
**Method**: GET
**Authentication**: Not required
**Parameters**:
- `format`: Response format ("json" or "plain")
- `debug`: Include debug info (1 or 0)

**Response Structure**:
```json
{
  "success": true,
  "ip": "*************",
  "type": "IPv4",
  "is_private": true,
  "timestamp": **********,
  "api_version": "3.0",
  "source": "modern_admin_panel",
  "endpoint": "ip"
}
```

### 6. Settings API (`settings.php`)
**Purpose**: Get application settings
**Method**: GET
**Authentication**: Required
**Parameters**:
- `category`: Settings category ("all", "app", "ads", "system")
- `keys`: Comma-separated list of specific keys

### 7. Status API (`status.php`)
**Purpose**: Check API health and status
**Method**: GET
**Authentication**: Not required

### 8. Legacy API (`legacy.php`)
**Purpose**: Backward compatibility with old API format
**Method**: GET
**Authentication**: Required

## Android Integration

### 1. API Service Usage
```java
// Get app configuration
VpnApiService apiService = VpnApiService.getInstance(context);
apiService.getAppConfig(packageName, new VpnApiService.ApiCallback<AppConfig>() {
    @Override
    public void onSuccess(AppConfig config) {
        // Handle successful response
    }
    
    @Override
    public void onError(String error, int errorCode) {
        // Handle error
    }
});

// Get servers
apiService.getServers(new VpnApiService.ApiCallback<List<Server>>() {
    @Override
    public void onSuccess(List<Server> servers) {
        // Handle servers list
    }
    
    @Override
    public void onError(String error, int errorCode) {
        // Handle error
    }
});

// Track ad
apiService.trackAd(adId, "view", "custom", userId, deviceId, callback);
```

### 2. Ad Tracking Usage
```java
// Track custom ad
AdTracker.trackCustomAdView(context, adId);
AdTracker.trackCustomAdClick(context, adId);

// Track AdMob ads
AdTracker.trackAdMobView(context, adUnitId);
AdTracker.trackAdMobClick(context, adUnitId);

// Track Facebook ads
AdTracker.trackFacebookView(context, placementId);
AdTracker.trackFacebookClick(context, placementId);
```

## Error Handling

### Common Error Codes:
- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Missing or invalid authentication
- `404`: Not Found - Endpoint not found
- `500`: Internal Server Error - Server-side error
- `503`: Service Unavailable - API unhealthy

### Error Response Format:
```json
{
  "error": "Unauthorized",
  "message": "Missing authentication parameters",
  "code": 401,
  "timestamp": **********
}
```

## Configuration Steps

### 1. Update API Secret Key
Update the API secret key in both server and client:
- Server: `5GsmartvpnAdminPanel/admin_new/db.php` line 22
- Client: `Const.java` line 37

### 2. Test API Connection
Use the status endpoint to verify API connectivity:
```java
apiService.checkApiStatus(new VpnApiService.ApiCallback<JSONObject>() {
    @Override
    public void onSuccess(JSONObject status) {
        Log.d("API", "API Status: " + status.toString());
    }
    
    @Override
    public void onError(String error, int errorCode) {
        Log.e("API", "API Status Check Failed: " + error);
    }
});
```

### 3. Database Schema
Ensure the database contains the required tables:
- `settings`: App configuration
- `servers`: VPN servers
- `custom_ads`: Custom advertisements
- `ad_tracking`: Ad tracking data (auto-created)

## Security Considerations

1. **API Key Security**: Store API keys securely and rotate them regularly
2. **HTTPS**: Use HTTPS in production environments
3. **Rate Limiting**: Implement rate limiting on the server side
4. **Input Validation**: Validate all input parameters
5. **Error Handling**: Don't expose sensitive information in error messages

## Testing

### 1. Unit Tests
Create unit tests for API service methods:
```java
@Test
public void testGetAppConfig() {
    // Test app config retrieval
}

@Test
public void testTrackAd() {
    // Test ad tracking
}
```

### 2. Integration Tests
Test the complete API integration flow:
1. Authentication
2. Data retrieval
3. Error handling
4. Network failures

## Troubleshooting

### Common Issues:
1. **Authentication Failures**: Check API key and timestamp generation
2. **Network Errors**: Verify base URL and network connectivity
3. **Parsing Errors**: Check response format and model classes
4. **Server Errors**: Check server logs and database connectivity

### Debug Mode:
Enable debug logging to troubleshoot issues:
```java
Log.d("API", "Request URL: " + url);
Log.d("API", "Response: " + response);
```
