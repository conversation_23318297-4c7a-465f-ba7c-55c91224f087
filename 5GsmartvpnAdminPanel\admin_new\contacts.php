<?php
/**
 * 5G Smart VPN Admin Panel - Contact Messages
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Contact Messages';
$success_message = '';
$error_message = '';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'mark_read':
            $message_id = (int)$_POST['message_id'];
            $query = "UPDATE contact_messages SET status = 'read', read_at = NOW() WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $message_id);
                if (mysqli_stmt_execute($stmt)) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to mark as read']);
                }
                mysqli_stmt_close($stmt);
            } else {
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
            exit();
            
        case 'delete_message':
            $message_id = (int)$_POST['message_id'];
            $query = "DELETE FROM contact_messages WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $message_id);
                if (mysqli_stmt_execute($stmt)) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to delete message']);
                }
                mysqli_stmt_close($stmt);
            } else {
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
            exit();
            
        case 'reply_message':
            $message_id = (int)$_POST['message_id'];
            $reply_text = trim($_POST['reply_text'] ?? '');
            
            if (empty($reply_text)) {
                echo json_encode(['success' => false, 'message' => 'Reply text is required']);
                exit();
            }
            
            // Update message with reply
            $query = "UPDATE contact_messages SET reply_text = ?, replied_at = NOW(), status = 'replied' WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'si', $reply_text, $message_id);
                if (mysqli_stmt_execute($stmt)) {
                    echo json_encode(['success' => true, 'message' => 'Reply sent successfully']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to send reply']);
                }
                mysqli_stmt_close($stmt);
            } else {
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
            exit();
    }
}

// Get messages with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$status_filter = $_GET['status'] ?? 'all';

// Build query based on filter
$where_clause = '';
$params = [];
$param_types = '';

if ($status_filter !== 'all') {
    $where_clause = 'WHERE status = ?';
    $params[] = $status_filter;
    $param_types .= 's';
}

// Count total messages
$count_query = "SELECT COUNT(*) as total FROM contact_messages $where_clause";
$count_stmt = mysqli_prepare($conn, $count_query);
if ($count_stmt && !empty($params)) {
    mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
}
if ($count_stmt) {
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total_messages = $count_result ? mysqli_fetch_assoc($count_result)['total'] : 0;
    mysqli_stmt_close($count_stmt);
} else {
    $total_messages = 0;
}

$total_pages = ceil($total_messages / $limit);

// Get messages
$query = "SELECT * FROM contact_messages $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$param_types .= 'ii';

$stmt = mysqli_prepare($conn, $query);
$messages = [];
if ($stmt) {
    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $param_types, ...$params);
    }
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $messages[] = $row;
        }
    }
    mysqli_stmt_close($stmt);
}

// Get stats
$stats = [
    'total_messages' => 0,
    'unread_messages' => 0,
    'replied_messages' => 0,
    'pending_messages' => 0
];

if ($conn) {
    $stats_query = "SELECT 
        COUNT(*) as total_messages,
        SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread_messages,
        SUM(CASE WHEN status = 'replied' THEN 1 ELSE 0 END) as replied_messages,
        SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as pending_messages
        FROM contact_messages";
    
    $stats_result = mysqli_query($conn, $stats_query);
    if ($stats_result) {
        $stats_data = mysqli_fetch_assoc($stats_result);
        $stats = array_merge($stats, $stats_data);
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Contact Messages</h1>
                <p class="page-subtitle">Manage customer inquiries and support requests</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <select class="form-control form-select" onchange="filterMessages(this.value)" style="width: auto;">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Messages</option>
                        <option value="unread" <?php echo $status_filter === 'unread' ? 'selected' : ''; ?>>Unread</option>
                        <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Read</option>
                        <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                    </select>
                    <button class="btn btn-secondary" onclick="refreshPage()">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Refresh</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-mail-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_messages']; ?></h3>
                        <p class="stat-label">Total Messages</p>
                        <span class="stat-change neutral">
                            <i class="ri-mail-line"></i>
                            All time
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-mail-unread-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['unread_messages']; ?></h3>
                        <p class="stat-label">Unread Messages</p>
                        <span class="stat-change <?php echo $stats['unread_messages'] > 0 ? 'negative' : 'positive'; ?>">
                            <i class="ri-notification-line"></i>
                            Needs attention
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-reply-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['replied_messages']; ?></h3>
                        <p class="stat-label">Replied Messages</p>
                        <span class="stat-change positive">
                            <i class="ri-check-line"></i>
                            Completed
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-time-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['pending_messages']; ?></h3>
                        <p class="stat-label">Pending Reply</p>
                        <span class="stat-change warning">
                            <i class="ri-clock-line"></i>
                            In progress
                        </span>
                    </div>
                </div>
            </div>

            <!-- Messages List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Messages</h3>
                    <div class="card-actions">
                        <div class="search-box">
                            <input type="text" id="messageSearch" placeholder="Search messages..." class="form-control">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="messages-list">
                        <?php foreach ($messages as $message): ?>
                        <div class="message-item <?php echo $message['status']; ?>" data-message-id="<?php echo $message['id']; ?>">
                            <div class="message-header">
                                <div class="message-sender">
                                    <div class="sender-avatar">
                                        <i class="ri-user-line"></i>
                                    </div>
                                    <div class="sender-info">
                                        <h4 class="sender-name"><?php echo htmlspecialchars($message['name']); ?></h4>
                                        <p class="sender-email"><?php echo htmlspecialchars($message['email']); ?></p>
                                    </div>
                                </div>
                                <div class="message-meta">
                                    <span class="message-date"><?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?></span>
                                    <span class="message-status badge <?php echo $message['status']; ?>">
                                        <?php echo ucfirst($message['status']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="message-content">
                                <h5 class="message-subject"><?php echo htmlspecialchars($message['subject']); ?></h5>
                                <p class="message-text"><?php echo nl2br(htmlspecialchars($message['message'])); ?></p>
                            </div>
                            
                            <?php if (!empty($message['reply_text'])): ?>
                            <div class="message-reply">
                                <h6 class="reply-header">
                                    <i class="ri-reply-line"></i>
                                    Your Reply (<?php echo date('M j, Y g:i A', strtotime($message['replied_at'])); ?>)
                                </h6>
                                <p class="reply-text"><?php echo nl2br(htmlspecialchars($message['reply_text'])); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="message-actions">
                                <?php if ($message['status'] === 'unread'): ?>
                                <button class="btn btn-sm btn-secondary" onclick="markAsRead(<?php echo $message['id']; ?>)">
                                    <i class="ri-mail-open-line"></i>
                                    Mark as Read
                                </button>
                                <?php endif; ?>
                                
                                <?php if ($message['status'] !== 'replied'): ?>
                                <button class="btn btn-sm btn-primary" onclick="showReplyModal(<?php echo $message['id']; ?>, '<?php echo htmlspecialchars($message['email']); ?>', '<?php echo htmlspecialchars($message['subject']); ?>')">
                                    <i class="ri-reply-line"></i>
                                    Reply
                                </button>
                                <?php endif; ?>
                                
                                <button class="btn btn-sm btn-danger" onclick="deleteMessage(<?php echo $message['id']; ?>)">
                                    <i class="ri-delete-bin-line"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($messages)): ?>
                        <div class="empty-state">
                            <i class="ri-mail-line empty-state-icon"></i>
                            <h3>No Messages Found</h3>
                            <p>No contact messages match your current filter.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>" 
                               class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<!-- Reply Modal -->
<div id="replyModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Reply to Message</h3>
            <button class="modal-close" onclick="closeReplyModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="replyForm">
                <input type="hidden" id="replyMessageId" name="message_id">
                <div class="form-group">
                    <label class="form-label">To:</label>
                    <input type="email" id="replyEmail" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">Subject:</label>
                    <input type="text" id="replySubject" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label for="replyText" class="form-label">Your Reply:</label>
                    <textarea id="replyText" name="reply_text" class="form-control form-textarea" 
                              placeholder="Type your reply here..." required></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeReplyModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="sendReply()">Send Reply</button>
        </div>
    </div>
</div>

<script>
// Filter messages
function filterMessages(status) {
    window.location.href = `?status=${status}`;
}

// Refresh page
function refreshPage() {
    location.reload();
}

// Mark message as read
function markAsRead(messageId) {
    fetch('contacts.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=mark_read&message_id=${messageId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark message as read');
        }
    })
    .catch(error => {
        alert('An error occurred');
    });
}

// Delete message
function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        fetch('contacts.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete_message&message_id=${messageId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelector(`[data-message-id="${messageId}"]`).remove();
            } else {
                alert('Failed to delete message');
            }
        })
        .catch(error => {
            alert('An error occurred');
        });
    }
}

// Show reply modal
function showReplyModal(messageId, email, subject) {
    document.getElementById('replyMessageId').value = messageId;
    document.getElementById('replyEmail').value = email;
    document.getElementById('replySubject').value = 'Re: ' + subject;
    document.getElementById('replyText').value = '';
    document.getElementById('replyModal').style.display = 'flex';
}

// Close reply modal
function closeReplyModal() {
    document.getElementById('replyModal').style.display = 'none';
}

// Send reply
function sendReply() {
    const messageId = document.getElementById('replyMessageId').value;
    const replyText = document.getElementById('replyText').value.trim();
    
    if (!replyText) {
        alert('Please enter a reply message');
        return;
    }
    
    fetch('contacts.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=reply_message&message_id=${messageId}&reply_text=${encodeURIComponent(replyText)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeReplyModal();
            location.reload();
        } else {
            alert(data.message || 'Failed to send reply');
        }
    })
    .catch(error => {
        alert('An error occurred');
    });
}

// Search functionality
document.getElementById('messageSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const messages = document.querySelectorAll('.message-item');
    
    messages.forEach(message => {
        const name = message.querySelector('.sender-name').textContent.toLowerCase();
        const email = message.querySelector('.sender-email').textContent.toLowerCase();
        const subject = message.querySelector('.message-subject').textContent.toLowerCase();
        const text = message.querySelector('.message-text').textContent.toLowerCase();
        
        const matches = name.includes(searchTerm) || 
                       email.includes(searchTerm) || 
                       subject.includes(searchTerm) || 
                       text.includes(searchTerm);
        
        message.style.display = matches ? '' : 'none';
    });
});

// Close modal when clicking outside
document.getElementById('replyModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReplyModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
