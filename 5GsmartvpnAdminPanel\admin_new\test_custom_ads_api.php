<?php
/**
 * Test Custom Ads API - Simple test without authentication
 */

require_once 'includes/config.php';

echo "<h2>Custom Ads API Test</h2>";

try {
    // Get current date for filtering active ads
    $today = date("Y-m-d");

    // Query to get active custom ads (same as API)
    $stmt = $conn->prepare("SELECT *,
        CASE
            WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 0
            ELSE 1
        END as is_active_now
        FROM custom_ads
        WHERE `on` = 1
        AND date_start <= ?
        AND date_end >= ?
        AND (is_approved = 1 OR is_approved IS NULL)
        AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY 
            CASE 
                WHEN priority >= 3 THEN RAND() * 0.1
                WHEN priority = 2 THEN RAND() * 0.3  
                WHEN priority = 1 THEN RAND() * 0.6
                ELSE RAND()
            END,
            priority DESC, 
            RAND()");
    $stmt->bind_param("ss", $today, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    $custom_ads_list = [];
    while ($row = $result->fetch_assoc()) {
        // Auto-detect URL type if not set
        if (empty($row['url_type']) || $row['url_type'] === 'other') {
            $url = strtolower($row['url']);
            if (strpos($url, 'play.google.com') !== false) {
                $row['url_type'] = 'playstore';
                $row['button_text'] = 'Install';
            } elseif (strpos($url, 'apps.apple.com') !== false || strpos($url, 'itunes.apple.com') !== false) {
                $row['url_type'] = 'appstore';
                $row['button_text'] = 'Install';
            } else {
                $row['url_type'] = 'website';
                $row['button_text'] = 'Visit';
            }
        }

        // Ensure button_text is set
        if (empty($row['button_text'])) {
            $row['button_text'] = $row['url_type'] === 'playstore' || $row['url_type'] === 'appstore' ? 'Install' : 'Visit';
        }

        $custom_ads_list[] = $row;
    }

    echo "<h3>Query Results</h3>";
    echo "<p><strong>Total active ads found:</strong> " . count($custom_ads_list) . "</p>";
    echo "<p><strong>Query date:</strong> $today</p>";

    if (!empty($custom_ads_list)) {
        // Select random ad (same logic as API)
        $random_ad = $custom_ads_list[array_rand($custom_ads_list)];

        echo "<h3>Selected Random Ad (API Response)</h3>";
        
        // Normalize field names for consistency with Android app
        $normalized_ad = [
            'id' => $random_ad['id'],
            'title' => $random_ad['title'],
            'image' => $random_ad['image'], // Database column is 'image'
            'text' => $random_ad['text'],   // Database column is 'text'
            'url' => $random_ad['url'],
            'url_type' => $random_ad['url_type'], // New field for URL type detection
            'button_text' => $random_ad['button_text'], // New field for button text
            'date_start' => $random_ad['date_start'],
            'date_end' => $random_ad['date_end'],
            'expires_at' => $random_ad['expires_at'],
            'priority' => (int)$random_ad['priority'],
            'on' => $random_ad['on'],
            'view_count' => $random_ad['view_count'],
            'click_count' => $random_ad['click_count'],
            'is_approved' => (bool)$random_ad['is_approved'],
            // Add metadata to the ad
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => count($custom_ads_list),
            // Enhanced metadata
            'smart_url_detection' => true,
            'supports_app_store_detection' => true
        ];

        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
        echo json_encode([$normalized_ad], JSON_PRETTY_PRINT);
        echo "</pre>";

        echo "<h3>Ad Details</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($normalized_ad as $key => $value) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($key) . "</strong></td>";
            echo "<td>" . htmlspecialchars(is_bool($value) ? ($value ? 'true' : 'false') : $value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Show image if exists
        if (!empty($random_ad['image'])) {
            echo "<h3>Ad Image</h3>";
            echo "<img src='../" . htmlspecialchars($random_ad['image']) . "' alt='Ad Image' style='max-width: 300px; border: 1px solid #ddd; border-radius: 5px;'>";
        }

    } else {
        echo "<h3>No Active Ads Found</h3>";
        echo "<p style='color: red;'>The API would return an empty response.</p>";
        
        echo "<h4>Troubleshooting:</h4>";
        echo "<ul>";
        echo "<li>Check if ads are enabled (`on` = 1)</li>";
        echo "<li>Check if ads are approved (is_approved = 1 or NULL)</li>";
        echo "<li>Check if ads are within date range (date_start <= today <= date_end)</li>";
        echo "<li>Check if ads haven't expired (expires_at > now or NULL)</li>";
        echo "</ul>";
    }

    // Show all ads for debugging
    echo "<h3>All Ads in Database (Debug)</h3>";
    $all_ads_result = $conn->query("SELECT id, title, `on`, is_approved, date_start, date_end, expires_at, priority FROM custom_ads ORDER BY id DESC");
    if ($all_ads_result && $all_ads_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>On</th><th>Approved</th><th>Start</th><th>End</th><th>Expires</th><th>Priority</th><th>Status</th></tr>";
        while ($row = $all_ads_result->fetch_assoc()) {
            $status = "INACTIVE";
            if ($row['on'] == 1) {
                if ($row['is_approved'] == 1 || $row['is_approved'] === null) {
                    if ($row['date_start'] <= $today && $row['date_end'] >= $today) {
                        if ($row['expires_at'] === null || $row['expires_at'] > date('Y-m-d H:i:s')) {
                            $status = "ACTIVE";
                        } else {
                            $status = "EXPIRED";
                        }
                    } else {
                        $status = "OUT_OF_DATE_RANGE";
                    }
                } else {
                    $status = "NOT_APPROVED";
                }
            }
            
            $status_color = $status === 'ACTIVE' ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td>" . ($row['on'] ? 'YES' : 'NO') . "</td>";
            echo "<td>" . ($row['is_approved'] === null ? 'PENDING' : ($row['is_approved'] ? 'YES' : 'NO')) . "</td>";
            echo "<td>" . htmlspecialchars($row['date_start']) . "</td>";
            echo "<td>" . htmlspecialchars($row['date_end']) . "</td>";
            echo "<td>" . htmlspecialchars($row['expires_at'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['priority'] ?? '0') . "</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
