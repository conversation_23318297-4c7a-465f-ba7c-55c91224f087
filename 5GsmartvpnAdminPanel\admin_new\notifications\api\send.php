<?php
/**
 * 5G Smart VPN Admin Panel - Send Notification API
 * RESTful API endpoint for sending notifications
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../includes/notification_functions.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
$required_fields = ['title', 'message'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    http_response_code(400);
    echo json_encode([
        'error' => 'Missing required fields',
        'missing_fields' => $missing_fields
    ]);
    exit();
}

// Extract and sanitize input data
$title = trim($input['title']);
$message = trim($input['message']);
$topic = $input['topic'] ?? 'all';
$scheduleType = $input['schedule_type'] ?? 'immediate';
$scheduledTime = $input['scheduled_time'] ?? null;
$recurringInterval = $input['recurring_interval'] ?? null;
$priority = $input['priority'] ?? 'normal';
$category = $input['category'] ?? 'general';
$targetAudience = $input['target_audience'] ?? 'all_users';
$data = $input['data'] ?? [];

// Validate priority
$valid_priorities = ['low', 'normal', 'high', 'urgent'];
if (!in_array($priority, $valid_priorities)) {
    $priority = 'normal';
}

// Validate schedule type
$valid_schedule_types = ['immediate', 'scheduled', 'recurring'];
if (!in_array($scheduleType, $valid_schedule_types)) {
    $scheduleType = 'immediate';
}

// Validate scheduled time for non-immediate notifications
if ($scheduleType !== 'immediate') {
    if (empty($scheduledTime)) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Scheduled time is required for non-immediate notifications'
        ]);
        exit();
    }
    
    // Validate date format
    $scheduledDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $scheduledTime);
    if (!$scheduledDateTime) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Invalid scheduled time format. Use Y-m-d H:i:s'
        ]);
        exit();
    }
    
    // Check if scheduled time is in the future
    if ($scheduledDateTime <= new DateTime()) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Scheduled time must be in the future'
        ]);
        exit();
    }
}

// Validate recurring interval for recurring notifications
if ($scheduleType === 'recurring') {
    $valid_intervals = ['hourly', 'daily', 'weekly', 'monthly'];
    if (empty($recurringInterval) || !in_array($recurringInterval, $valid_intervals)) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Valid recurring interval is required for recurring notifications',
            'valid_intervals' => $valid_intervals
        ]);
        exit();
    }
}

// Add metadata to data
$data['priority'] = $priority;
$data['category'] = $category;
$data['target_audience'] = $targetAudience;
$data['sent_at'] = date('Y-m-d H:i:s');

try {
    // Send the notification
    $result = sendFCMNotification(
        $title,
        $message,
        $topic,
        $scheduleType,
        $scheduledTime,
        $recurringInterval,
        $data
    );
    
    if ($result['success']) {
        // Log the successful action
        if (isset($result['notification_id'])) {
            logNotificationAction($result['notification_id'], 'sent', 'Notification sent successfully via API');
        }
        
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $scheduleType === 'immediate' ? 'Notification sent successfully' : 'Notification scheduled successfully',
            'notification_id' => $result['notification_id'] ?? null,
            'status' => $result['status'] ?? 'sent',
            'schedule_type' => $scheduleType,
            'scheduled_time' => $scheduledTime,
            'recurring_interval' => $recurringInterval
        ]);
    } else {
        // Log the failed action
        if (isset($result['notification_id'])) {
            logNotificationAction($result['notification_id'], 'failed', $result['error'] ?? 'Unknown error');
        }
        
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $result['error'] ?? 'Failed to send notification',
            'notification_id' => $result['notification_id'] ?? null
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}

/**
 * Log notification action
 */
function logNotificationAction($notificationId, $action, $details) {
    global $conn;
    
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    
    $sql = "INSERT INTO notification_logs (notification_id, action, details, user_agent, ip_address) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("issss", $notificationId, $action, $details, $userAgent, $ipAddress);
    $stmt->execute();
}
?>
