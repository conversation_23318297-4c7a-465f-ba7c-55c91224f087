<?php
/**
 * Simple Analytics API Endpoint
 * Provides basic analytics data using mysqli
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../../config.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Verify API authentication
$timestamp = $_GET['timestamp'] ?? '';
$signature = $_GET['signature'] ?? '';

if (empty($timestamp) || empty($signature)) {
    http_response_code(401);
    echo json_encode(['error' => 'Missing authentication parameters']);
    exit;
}

$expected_signature = hash_hmac('sha256', $timestamp, $API_KEY);
if (!hash_equals($expected_signature, $signature)) {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid signature']);
    exit;
}

// Check timestamp (allow 5 minute window)
$current_time = time();
if (abs($current_time - $timestamp) > 300) {
    http_response_code(401);
    echo json_encode(['error' => 'Request timestamp expired']);
    exit;
}

try {
    // Get date range from parameters
    $start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
    $end_date = $_GET['end_date'] ?? date('Y-m-d');
    $action = $_GET['action'] ?? 'summary';
    
    $response = [
        'success' => true,
        'timestamp' => time(),
        'date_range' => [
            'start' => $start_date,
            'end' => $end_date
        ]
    ];
    
    switch ($action) {
        case 'summary':
            $response['data'] = getAnalyticsSummary($conn, $start_date, $end_date);
            break;
            
        case 'custom_ads':
            $response['data'] = getCustomAdsAnalytics($conn, $start_date, $end_date);
            break;
            
        case 'real_time':
            $response['data'] = getRealTimeAnalytics($conn);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action parameter']);
            exit;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}

function getAnalyticsSummary($conn, $start_date, $end_date) {
    $summary = [
        'total_impressions' => 0,
        'total_clicks' => 0,
        'total_revenue' => 0,
        'ctr' => 0,
        'custom_ads' => [
            'total_ads' => 0,
            'views' => 0,
            'clicks' => 0
        ]
    ];
    
    // Get custom ads data
    $custom_query = "SELECT 
        COUNT(*) as total_ads,
        SUM(view_count) as total_views,
        SUM(click_count) as total_clicks
        FROM custom_ads 
        WHERE date_start <= ? AND date_end >= ?";
    
    $stmt = mysqli_prepare($conn, $custom_query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ss', $end_date, $start_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        if ($row = mysqli_fetch_assoc($result)) {
            $summary['custom_ads']['total_ads'] = (int)($row['total_ads'] ?? 0);
            $summary['custom_ads']['views'] = (int)($row['total_views'] ?? 0);
            $summary['custom_ads']['clicks'] = (int)($row['total_clicks'] ?? 0);
        }
        mysqli_stmt_close($stmt);
    }
    
    // Get tracking data if table exists
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'ad_tracking'");
    if (mysqli_num_rows($table_check) > 0) {
        $tracking_query = "SELECT 
            event_type,
            COUNT(*) as event_count
            FROM ad_tracking 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY event_type";
        
        $tracking_stmt = mysqli_prepare($conn, $tracking_query);
        if ($tracking_stmt) {
            mysqli_stmt_bind_param($tracking_stmt, 'ss', $start_date, $end_date);
            mysqli_stmt_execute($tracking_stmt);
            $tracking_result = mysqli_stmt_get_result($tracking_stmt);
            
            while ($row = mysqli_fetch_assoc($tracking_result)) {
                if ($row['event_type'] === 'view') {
                    $summary['total_impressions'] += (int)$row['event_count'];
                } elseif ($row['event_type'] === 'click') {
                    $summary['total_clicks'] += (int)$row['event_count'];
                }
            }
            mysqli_stmt_close($tracking_stmt);
        }
    }
    
    // Add custom ads data to totals
    $summary['total_impressions'] += $summary['custom_ads']['views'];
    $summary['total_clicks'] += $summary['custom_ads']['clicks'];
    
    // Calculate metrics
    if ($summary['total_impressions'] > 0) {
        $summary['ctr'] = round(($summary['total_clicks'] / $summary['total_impressions']) * 100, 2);
    }
    
    $summary['total_revenue'] = $summary['total_clicks'] * 0.05; // Simulate revenue
    
    return $summary;
}

function getCustomAdsAnalytics($conn, $start_date, $end_date) {
    $custom_query = "SELECT 
        id,
        title,
        view_count,
        click_count,
        date_start,
        date_end,
        `on` as active
        FROM custom_ads 
        WHERE date_start <= ? AND date_end >= ?
        ORDER BY view_count DESC";
    
    $stmt = mysqli_prepare($conn, $custom_query);
    $custom_ads = [];
    
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ss', $end_date, $start_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        while ($row = mysqli_fetch_assoc($result)) {
            $total_views = (int)$row['view_count'];
            $total_clicks = (int)$row['click_count'];
            
            $row['total_views'] = $total_views;
            $row['total_clicks'] = $total_clicks;
            
            if ($total_views > 0) {
                $row['ctr'] = round(($total_clicks / $total_views) * 100, 2);
            } else {
                $row['ctr'] = 0;
            }
            
            $custom_ads[] = $row;
        }
        mysqli_stmt_close($stmt);
    }
    
    return $custom_ads;
}

function getRealTimeAnalytics($conn) {
    $real_time_data = [
        'hourly_stats' => [],
        'last_updated' => date('Y-m-d H:i:s'),
        'total_impressions_24h' => 0,
        'total_clicks_24h' => 0
    ];
    
    // Initialize hourly stats
    for ($i = 0; $i < 24; $i++) {
        $real_time_data['hourly_stats'][$i] = [
            'hour' => $i,
            'impressions' => 0,
            'clicks' => 0
        ];
    }
    
    // Get tracking data if table exists
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'ad_tracking'");
    if (mysqli_num_rows($table_check) > 0) {
        $real_time_query = "SELECT 
            event_type,
            COUNT(*) as event_count,
            HOUR(created_at) as hour
            FROM ad_tracking 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY event_type, HOUR(created_at)
            ORDER BY hour ASC";
        
        $result = mysqli_query($conn, $real_time_query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $hour = (int)$row['hour'];
                $event_type = $row['event_type'];
                $count = (int)$row['event_count'];
                
                if ($event_type === 'view') {
                    $real_time_data['hourly_stats'][$hour]['impressions'] += $count;
                    $real_time_data['total_impressions_24h'] += $count;
                } elseif ($event_type === 'click') {
                    $real_time_data['hourly_stats'][$hour]['clicks'] += $count;
                    $real_time_data['total_clicks_24h'] += $count;
                }
            }
        }
    }
    
    // Convert to indexed array
    $real_time_data['hourly_stats'] = array_values($real_time_data['hourly_stats']);
    
    return $real_time_data;
}
?>
