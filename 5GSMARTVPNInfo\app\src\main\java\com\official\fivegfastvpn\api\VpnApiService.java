package com.official.fivegfastvpn.api;

import android.content.Context;
import android.util.Log;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonArrayRequest;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.official.fivegfastvpn.model.ApiResponse;
import com.official.fivegfastvpn.model.AppConfig;
import com.official.fivegfastvpn.model.CustomAd;
import com.official.fivegfastvpn.model.Server;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * VPN API Service for 5G Smart VPN
 * Handles all API communication with the modern admin panel
 */
public class VpnApiService {
    private static final String TAG = "VpnApiService";
    private static VpnApiService instance;
    private RequestQueue requestQueue;
    private Context context;
    
    private VpnApiService(Context context) {
        this.context = context.getApplicationContext();
        this.requestQueue = Volley.newRequestQueue(this.context);
    }
    
    public static synchronized VpnApiService getInstance(Context context) {
        if (instance == null) {
            instance = new VpnApiService(context);
        }
        return instance;
    }
    
    /**
     * Interface for API callbacks
     */
    public interface ApiCallback<T> {
        void onSuccess(T result);
        void onError(String error, int errorCode);
    }
    
    /**
     * Get app configuration (ads + servers)
     */
    public void getAppConfig(String packageName, ApiCallback<AppConfig> callback) {
        if (!ApiAuthenticator.isApiKeyConfigured()) {
            callback.onError("API key not configured", 401);
            return;
        }
        
        String url = ApiAuthenticator.buildAuthenticatedUrl(Const.CONFIG_API, packageName);
        if (url == null) {
            callback.onError("Failed to build authenticated URL", 500);
            return;
        }
        
        Log.d(TAG, "Fetching app config from: " + url);
        
        JsonArrayRequest request = new JsonArrayRequest(Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.length() > 0) {
                        JSONObject configJson = response.getJSONObject(0);
                        AppConfig config = parseAppConfig(configJson);
                        callback.onSuccess(config);
                    } else {
                        callback.onError("Empty response from server", 204);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing config response", e);
                    callback.onError("Failed to parse server response", 500);
                }
            },
            error -> {
                Log.e(TAG, "Error fetching app config", error);
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    /**
     * Get VPN servers list
     */
    public void getServers(ApiCallback<List<Server>> callback) {
        getServers(1, null, null, null, callback); // Default: active servers
    }
    
    /**
     * Get VPN servers with filters
     */
    public void getServers(int status, Integer limit, String orderBy, String orderDir, 
                          ApiCallback<List<Server>> callback) {
        if (!ApiAuthenticator.isApiKeyConfigured()) {
            callback.onError("API key not configured", 401);
            return;
        }
        
        StringBuilder urlBuilder = new StringBuilder(Const.SERVERS_API);
        urlBuilder.append("?status=").append(status);
        
        if (limit != null && limit > 0) {
            urlBuilder.append("&limit=").append(limit);
        }
        if (orderBy != null && !orderBy.isEmpty()) {
            urlBuilder.append("&order=").append(orderBy);
        }
        if (orderDir != null && !orderDir.isEmpty()) {
            urlBuilder.append("&dir=").append(orderDir);
        }
        
        String url = ApiAuthenticator.addAuthParams(urlBuilder.toString());
        Log.d(TAG, "Fetching servers from: " + url);
        
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.getBoolean("success")) {
                        JSONArray serversArray = response.getJSONArray("servers");
                        List<Server> servers = parseServersList(serversArray);
                        callback.onSuccess(servers);
                    } else {
                        String error = response.optString("message", "Unknown error");
                        callback.onError(error, response.optInt("code", 500));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing servers response", e);
                    callback.onError("Failed to parse server response", 500);
                }
            },
            error -> {
                Log.e(TAG, "Error fetching servers", error);
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    /**
     * Get custom ads
     */
    public void getCustomAds(ApiCallback<CustomAd> callback) {
        if (!ApiAuthenticator.isApiKeyConfigured()) {
            callback.onError("API key not configured", 401);
            return;
        }
        
        String url = ApiAuthenticator.buildAuthenticatedUrl(Const.CUSTOM_ADS_API);
        if (url == null) {
            callback.onError("Failed to build authenticated URL", 500);
            return;
        }
        
        Log.d(TAG, "Fetching custom ads from: " + url);
        Log.d(TAG, "Custom ads API endpoint: " + Const.CUSTOM_ADS_API);

        JsonArrayRequest request = new JsonArrayRequest(Request.Method.GET, url, null,
            response -> {
                try {
                    Log.d(TAG, "Custom ads response received, length: " + response.length());
                    Log.d(TAG, "Custom ads raw response: " + response.toString());

                    if (response.length() > 0) {
                        JSONObject adJson = response.getJSONObject(0);
                        Log.d(TAG, "Parsing custom ad JSON: " + adJson.toString());

                        CustomAd customAd = parseCustomAd(adJson);
                        Log.d(TAG, "Custom ad parsed successfully: " + customAd.toString());
                        callback.onSuccess(customAd);
                    } else {
                        Log.w(TAG, "No custom ads found in response");
                        callback.onError("No active ads found", 204);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing custom ads response", e);
                    Log.e(TAG, "Raw response that failed to parse: " + response.toString());
                    callback.onError("Failed to parse ads response: " + e.getMessage(), 500);
                }
            },
            error -> {
                Log.e(TAG, "Error fetching custom ads", error);
                if (error.networkResponse != null) {
                    Log.e(TAG, "Network response code: " + error.networkResponse.statusCode);
                    if (error.networkResponse.data != null) {
                        String errorBody = new String(error.networkResponse.data);
                        Log.e(TAG, "Error response body: " + errorBody);
                    }
                }
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    /**
     * Track ad view or click
     */
    public void trackAd(int adId, String eventType, String adType, String userId, String deviceId,
                       ApiCallback<String> callback) {
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("ad_id", adId);
            requestBody.put("type", eventType); // "view" or "click"
            requestBody.put("ad_type", adType); // "custom", "admob", "facebook"
            if (userId != null) requestBody.put("user_id", userId);
            if (deviceId != null) requestBody.put("device_id", deviceId);
        } catch (JSONException e) {
            callback.onError("Failed to create request body", 400);
            return;
        }
        
        Log.d(TAG, "Tracking ad: " + requestBody.toString());
        
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.POST, Const.TRACK_AD_API, 
            requestBody,
            response -> {
                try {
                    if (response.getBoolean("success")) {
                        String message = response.optString("message", "Ad tracked successfully");
                        callback.onSuccess(message);
                    } else {
                        String error = response.optString("message", "Failed to track ad");
                        callback.onError(error, response.optInt("code", 400));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing track ad response", e);
                    callback.onError("Failed to parse response", 500);
                }
            },
            error -> {
                Log.e(TAG, "Error tracking ad", error);
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    /**
     * Get client IP address
     */
    public void getClientIP(ApiCallback<String> callback) {
        StringRequest request = new StringRequest(Request.Method.GET, Const.IP_API,
            response -> {
                try {
                    JSONObject jsonResponse = new JSONObject(response);
                    if (jsonResponse.getBoolean("success")) {
                        String ip = jsonResponse.getString("ip");
                        callback.onSuccess(ip);
                    } else {
                        String error = jsonResponse.optString("message", "Failed to get IP");
                        callback.onError(error, jsonResponse.optInt("code", 500));
                    }
                } catch (JSONException e) {
                    // Fallback: try to parse as plain text (backward compatibility)
                    if (response != null && !response.isEmpty()) {
                        callback.onSuccess(response.trim());
                    } else {
                        callback.onError("Invalid IP response", 500);
                    }
                }
            },
            error -> {
                Log.e(TAG, "Error getting client IP", error);
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    /**
     * Check API status
     */
    public void checkApiStatus(ApiCallback<JSONObject> callback) {
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET, Const.STATUS_API, null,
            response -> callback.onSuccess(response),
            error -> {
                Log.e(TAG, "Error checking API status", error);
                handleVolleyError(error, callback);
            }
        );
        
        setRequestPolicy(request);
        requestQueue.add(request);
    }
    
    // Helper methods
    private AppConfig parseAppConfig(JSONObject json) throws JSONException {
        AppConfig config = new AppConfig();
        
        // AdMob Configuration
        config.setAdmobId(json.optString("admob_id", ""));
        config.setAdmobBanner(json.optString("admob_banner", ""));
        config.setAdmobInterstitial(json.optString("admob_interstitial", ""));
        config.setAdmobNative(json.optString("admob_native", ""));
        config.setAdmobRewarded(json.optString("admob_rewarded", ""));
        config.setAdmobOpenad(json.optString("admob_openad", ""));
        
        // Facebook Ads Configuration
        config.setFacebookId(json.optString("facebook_id", ""));
        config.setFacebookBanner(json.optString("facebook_banner", ""));
        config.setFacebookInterstitial(json.optString("facebook_interstitial", ""));
        config.setFacebookNative(json.optString("facebook_native", ""));
        config.setFacebookRewarded(json.optString("facebook_rewarded", ""));
        
        // Ad Status and Types
        config.setAdsStatus(json.optInt("ads_status", 0));
        config.setBannerType(json.optString("banner_type", "admob"));
        config.setInterstitialType(json.optString("interstitial_type", "admob"));
        config.setNativeType(json.optString("native_type", "admob"));
        config.setRewardedType(json.optString("rewarded_type", "admob"));
        
        // Individual Ad Type Status
        config.setBannerEnabled(json.optInt("banner_enabled", 1));
        config.setInterstitialEnabled(json.optInt("interstitial_enabled", 1));
        config.setRewardedEnabled(json.optInt("rewarded_enabled", 0));
        config.setNativeEnabled(json.optInt("native_enabled", 0));
        config.setOpenadEnabled(json.optInt("openad_enabled", 1));
        
        // Additional Settings
        config.setRewardTime(json.optInt("reward_time", 30));
        config.setClickLimit(json.optInt("click_limit", 5));
        config.setShowFrequency(json.optInt("show_frequency", 3));
        config.setTestMode(json.optInt("test_mode", 1));
        
        // Legacy compatibility fields
        config.setAppId(json.optString("app_id", ""));
        config.setBanner(json.optString("banner", ""));
        config.setInterstitial(json.optString("interstitial", ""));
        config.setNativeAd(json.optString("native", ""));
        config.setRewarded(json.optString("rewarded", ""));
        config.setOpenad(json.optString("openad", ""));
        config.setActive(json.optInt("active", 0));
        
        // Servers
        if (json.has("servers")) {
            JSONArray serversArray = json.getJSONArray("servers");
            config.setServers(parseServersList(serversArray));
        }
        
        // Metadata
        config.setApiVersion(json.optString("api_version", ""));
        config.setTimestamp(json.optLong("timestamp", 0));
        config.setSource(json.optString("source", ""));
        config.setEndpoint(json.optString("endpoint", ""));
        
        return config;
    }
    
    private List<Server> parseServersList(JSONArray serversArray) throws JSONException {
        List<Server> servers = new ArrayList<>();
        for (int i = 0; i < serversArray.length(); i++) {
            JSONObject serverJson = serversArray.getJSONObject(i);
            Server server = new Server();
            
            server.setId(serverJson.optInt("id", 0));
            server.setName(serverJson.optString("name", ""));
            server.setUsername(serverJson.optString("username", ""));
            server.setPassword(serverJson.optString("password", ""));
            server.setConfigFile(serverJson.optString("configFile", ""));
            server.setFlagURL(serverJson.optString("flagURL", ""));
            server.setType(String.valueOf(serverJson.optInt("type", 1))); // Convert int to String
            server.setPos(serverJson.optInt("pos", 0));
            server.setStatus(serverJson.optInt("status", 1));
            
            servers.add(server);
        }
        return servers;
    }
    
    private CustomAd parseCustomAd(JSONObject json) throws JSONException {
        CustomAd ad = new CustomAd();
        
        ad.setId(json.optInt("id", 0));
        ad.setTitle(json.optString("title", ""));
        ad.setImage(json.optString("image", ""));
        ad.setText(json.optString("text", ""));
        ad.setUrl(json.optString("url", ""));
        ad.setDateStart(json.optString("date_start", ""));
        ad.setDateEnd(json.optString("date_end", ""));
        ad.setOn(json.optInt("on", 0));
        ad.setViewCount(json.optInt("view_count", 0));
        ad.setClickCount(json.optInt("click_count", 0));
        
        // Metadata
        ad.setApiVersion(json.optString("api_version", ""));
        ad.setTimestamp(json.optLong("timestamp", 0));
        ad.setSource(json.optString("source", ""));
        ad.setEndpoint(json.optString("endpoint", ""));
        ad.setTotalActiveAds(json.optInt("total_active_ads", 0));
        
        return ad;
    }
    
    private void setRequestPolicy(Request<?> request) {
        request.setRetryPolicy(new DefaultRetryPolicy(
            Const.API_TIMEOUT_SECONDS * 1000,
            Const.API_RETRY_COUNT,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));
    }
    
    private void handleVolleyError(VolleyError error, ApiCallback<?> callback) {
        String errorMessage = "Network error occurred";
        int errorCode = 500;
        
        if (error.networkResponse != null) {
            errorCode = error.networkResponse.statusCode;
            try {
                String responseBody = new String(error.networkResponse.data, "utf-8");
                JSONObject errorJson = new JSONObject(responseBody);
                errorMessage = errorJson.optString("message", errorMessage);
            } catch (Exception e) {
                // Use default error message
            }
        } else if (error.getMessage() != null) {
            errorMessage = error.getMessage();
        }
        
        callback.onError(errorMessage, errorCode);
    }
}
