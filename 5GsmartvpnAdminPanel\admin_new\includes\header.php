<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo ADMIN_PANEL_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="assets/images/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/images/favicon.ico" type="image/x-icon">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Main CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">

    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Meta tags -->
    <meta name="description" content="5G Smart VPN Admin Panel - Manage your VPN servers, advertisements, and notifications">
    <meta name="author" content="5G Smart VPN">
    <meta name="robots" content="noindex, nofollow">

    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : bin2hex(random_bytes(32)); ?>">

    <!-- Theme Configuration -->
    <script>
        // Theme configuration
        const ADMIN_CONFIG = {
            baseUrl: '<?php echo ADMIN_PANEL_URL; ?>',
            version: '<?php echo ADMIN_PANEL_VERSION; ?>',
            csrfToken: '<?php echo isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : bin2hex(random_bytes(32)); ?>',
            user: {
                id: <?php echo $_SESSION['admin_id'] ?? 0; ?>,
                username: '<?php echo $_SESSION['admin_username'] ?? ''; ?>'
            }
        };

        // Set theme from localStorage or default to light
        const theme = localStorage.getItem('admin-theme') || 'light';
        document.documentElement.setAttribute('data-theme', theme);
    </script>
</head>
<body class="admin-body">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php
    $flash = null;
    if (isset($_SESSION['flash_message'])) {
        $flash = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
    }
    ?>
    <?php if ($flash): ?>
    <div id="flash-message" class="flash-message flash-<?php echo $flash['type']; ?>">
        <div class="flash-content">
            <i class="flash-icon ri-<?php echo $flash['type'] === 'success' ? 'check' : ($flash['type'] === 'error' ? 'error-warning' : 'information'); ?>-line"></i>
            <span class="flash-text"><?php echo htmlspecialchars($flash['text']); ?></span>
            <button class="flash-close" onclick="closeFlashMessage()">
                <i class="ri-close-line"></i>
            </button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Mobile Overlay -->
    <div id="mobile-overlay" class="mobile-overlay" onclick="closeMobileSidebar()"></div>

    <script>
        // Hide loading screen when page is loaded
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
        });

        // Flash message functions
        function closeFlashMessage() {
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                flashMessage.style.opacity = '0';
                flashMessage.style.transform = 'translateY(-100%)';
                setTimeout(() => {
                    flashMessage.remove();
                }, 300);
            }
        }

        // Auto-hide flash messages after 5 seconds
        setTimeout(() => {
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                closeFlashMessage();
            }
        }, 5000);

        // Mobile sidebar functions
        function toggleMobileSidebar() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.getElementById('mobile-overlay');
            const body = document.body;

            if (sidebar && overlay) {
                const isOpen = sidebar.classList.contains('mobile-open');

                if (isOpen) {
                    closeMobileSidebar();
                } else {
                    sidebar.classList.add('mobile-open');
                    overlay.classList.add('active');
                    body.classList.add('sidebar-open');
                }
            }
        }

        function closeMobileSidebar() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.getElementById('mobile-overlay');
            const body = document.body;

            if (sidebar && overlay) {
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('active');
                body.classList.remove('sidebar-open');
            }
        }

        // Close sidebar when clicking on main content on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.admin-sidebar');
                const mainContent = document.querySelector('.main-content');

                if (sidebar && mainContent &&
                    sidebar.classList.contains('mobile-open') &&
                    mainContent.contains(e.target)) {
                    closeMobileSidebar();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMobileSidebar();
            }
        });

        // Theme toggle function
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('admin-theme', newTheme);

            // Update theme toggle button icon
            const themeToggleIcons = document.querySelectorAll('.theme-toggle-icon');
            themeToggleIcons.forEach(icon => {
                icon.className = newTheme === 'dark' ? 'ri-sun-line theme-toggle-icon' : 'ri-moon-line theme-toggle-icon';
            });

            // Add smooth transition effect
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        }

        // Initialize theme toggle button
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggleIcons = document.querySelectorAll('.theme-toggle-icon');
            const currentTheme = document.documentElement.getAttribute('data-theme');

            themeToggleIcons.forEach(icon => {
                icon.className = currentTheme === 'dark' ? 'ri-sun-line theme-toggle-icon' : 'ri-moon-line theme-toggle-icon';
            });
        });

        // AJAX helper function
        function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': ADMIN_CONFIG.csrfToken
                }
            };

            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('Request failed:', error);
                    throw error;
                });
        }

        // Mobile-specific enhancements
        function initializeMobileEnhancements() {
            // Add touch feedback to buttons (non-interfering)
            document.querySelectorAll('.btn, button').forEach(btn => {
                btn.addEventListener('touchstart', function(e) {
                    // Don't prevent default - allow click events to work
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                btn.addEventListener('touchend', function(e) {
                    // Don't prevent default - allow click events to work
                    this.style.transform = '';
                }, { passive: true });

                // Ensure click events work on mobile
                btn.addEventListener('click', function(e) {
                    // Reset transform immediately on click
                    this.style.transform = '';
                }, { passive: false });
            });

            // Improve table scrolling on mobile (non-interfering)
            document.querySelectorAll('.table-responsive').forEach(table => {
                let isScrolling = false;
                let startX = 0;
                let startY = 0;

                table.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    isScrolling = false;
                }, { passive: true });

                table.addEventListener('touchmove', (e) => {
                    const currentX = e.touches[0].clientX;
                    const currentY = e.touches[0].clientY;
                    const diffX = Math.abs(currentX - startX);
                    const diffY = Math.abs(currentY - startY);

                    // Only consider it scrolling if horizontal movement is significant
                    if (diffX > diffY && diffX > 10) {
                        isScrolling = true;
                    }
                }, { passive: true });

                table.addEventListener('touchend', () => {
                    setTimeout(() => {
                        isScrolling = false;
                    }, 100);
                }, { passive: true });
            });

            // Add swipe to close sidebar
            let startX = 0;
            let currentX = 0;
            const sidebar = document.querySelector('.admin-sidebar');

            if (sidebar) {
                sidebar.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                });

                sidebar.addEventListener('touchmove', (e) => {
                    currentX = e.touches[0].clientX;
                });

                sidebar.addEventListener('touchend', () => {
                    const diffX = startX - currentX;
                    if (diffX > 50 && sidebar.classList.contains('mobile-open')) {
                        closeMobileSidebar();
                    }
                });
            }

            // Prevent zoom on form inputs (iOS)
            document.querySelectorAll('input, select, textarea').forEach(input => {
                input.addEventListener('focus', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content',
                            'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });

                input.addEventListener('blur', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content',
                            'width=device-width, initial-scale=1.0');
                    }
                });
            });

            // Ensure all interactive elements work on mobile
            enhanceMobileInteractions();
        }

        // Enhanced mobile interactions
        function enhanceMobileInteractions() {
            // Fix action buttons in tables
            document.querySelectorAll('.action-buttons a, .action-buttons button').forEach(element => {
                // Ensure proper cursor and touch behavior
                element.style.cursor = 'pointer';
                element.style.touchAction = 'manipulation';

                // Add mobile-specific click handling only if needed
                if (window.innerWidth <= 768) {
                    element.addEventListener('touchend', function(e) {
                        // Only handle if this is a simple tap (not a scroll)
                        const touch = e.changedTouches[0];
                        const element = document.elementFromPoint(touch.clientX, touch.clientY);

                        if (element === this || this.contains(element)) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Trigger click after a small delay to ensure touch is complete
                            setTimeout(() => {
                                if (this.href) {
                                    window.location.href = this.href;
                                } else if (this.onclick) {
                                    this.onclick();
                                } else {
                                    // Create and dispatch a proper click event
                                    const clickEvent = new MouseEvent('click', {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    });
                                    this.dispatchEvent(clickEvent);
                                }
                            }, 50);
                        }
                    }, { passive: false });
                }
            });

            // Fix switches and toggles
            document.querySelectorAll('.switch input, .status-toggle').forEach(toggle => {
                toggle.style.touchAction = 'manipulation';

                toggle.addEventListener('touchend', function(e) {
                    e.stopPropagation();
                    // Let the default behavior handle the toggle
                }, { passive: true });
            });

            // Fix dropdown triggers
            document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(dropdown => {
                dropdown.style.touchAction = 'manipulation';

                dropdown.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    setTimeout(() => {
                        this.click();
                    }, 50);
                }, { passive: false });
            });

            // Fix modal triggers
            document.querySelectorAll('[data-bs-toggle="modal"]').forEach(modal => {
                modal.style.touchAction = 'manipulation';

                modal.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    setTimeout(() => {
                        this.click();
                    }, 50);
                }, { passive: false });
            });
        }

        // Initialize mobile enhancements when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeMobileEnhancements);

        // Re-initialize mobile interactions for dynamically loaded content
        function reinitializeMobileInteractions() {
            enhanceMobileInteractions();
        }

        // Make function globally available
        window.reinitializeMobileInteractions = reinitializeMobileInteractions;

        // Observer for dynamically added content
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain interactive elements
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            const hasInteractiveElements = node.querySelector && (
                                node.querySelector('.btn') ||
                                node.querySelector('.action-buttons') ||
                                node.querySelector('.switch') ||
                                node.querySelector('[data-bs-toggle]')
                            );

                            if (hasInteractiveElements) {
                                setTimeout(reinitializeMobileInteractions, 100);
                            }
                        }
                    });
                }
            });
        });

        // Start observing
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>
