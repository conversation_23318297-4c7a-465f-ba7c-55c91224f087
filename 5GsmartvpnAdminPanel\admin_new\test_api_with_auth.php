<?php
/**
 * Test Custom Ads API with Authentication
 */

echo "<h2>Custom Ads API Test with Authentication</h2>";

// API configuration (same as Android app)
$api_base = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/";
$api_key = "5g_smart_vpn_2024_secure_key_v2";
$api_secret = "vpn_admin_secret_2024_enhanced";

function buildAuthenticatedUrl($endpoint, $api_key, $api_secret) {
    $timestamp = time();
    $data_to_sign = $endpoint . $timestamp . $api_key;
    $signature = hash_hmac('sha256', $data_to_sign, $api_secret);
    
    return $endpoint . "?timestamp=" . $timestamp . "&signature=" . $signature;
}

try {
    // Build authenticated URL
    $custom_ads_endpoint = $api_base . "custom_ads.php";
    $authenticated_url = buildAuthenticatedUrl($custom_ads_endpoint, $api_key, $api_secret);
    
    echo "<h3>API Request Details</h3>";
    echo "<p><strong>Endpoint:</strong> " . htmlspecialchars($custom_ads_endpoint) . "</p>";
    echo "<p><strong>Authenticated URL:</strong> " . htmlspecialchars($authenticated_url) . "</p>";
    
    // Make API request
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: 5G Smart VPN Test Client',
                'Accept: application/json'
            ]
        ]
    ]);
    
    echo "<h3>Making API Request...</h3>";
    $response = file_get_contents($authenticated_url, false, $context);
    
    if ($response === false) {
        throw new Exception("Failed to fetch data from API");
    }
    
    echo "<h3>API Response</h3>";
    echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";
    
    // Try to decode JSON
    $json_data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<p style='color: red;'>JSON Decode Error: " . json_last_error_msg() . "</p>";
        echo "<h4>Raw Response:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
    } else {
        echo "<p style='color: green;'>✓ JSON decoded successfully</p>";
        
        if (is_array($json_data) && !empty($json_data)) {
            echo "<h4>Formatted JSON Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
            echo json_encode($json_data, JSON_PRETTY_PRINT);
            echo "</pre>";
            
            // Check if it's an array of ads
            if (isset($json_data[0]) && is_array($json_data[0])) {
                $ad = $json_data[0];
                echo "<h4>Ad Details:</h4>";
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Field</th><th>Value</th></tr>";
                foreach ($ad as $key => $value) {
                    echo "<tr>";
                    echo "<td><strong>" . htmlspecialchars($key) . "</strong></td>";
                    echo "<td>" . htmlspecialchars(is_bool($value) ? ($value ? 'true' : 'false') : $value) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Show image if exists
                if (!empty($ad['image'])) {
                    echo "<h4>Ad Image:</h4>";
                    $image_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/" . $ad['image'];
                    echo "<img src='" . htmlspecialchars($image_url) . "' alt='Ad Image' style='max-width: 300px; border: 1px solid #ddd; border-radius: 5px;'>";
                }
                
                echo "<h4>Android App Integration:</h4>";
                echo "<p style='color: green;'>✓ This ad should be visible in the Android app</p>";
                echo "<p><strong>Title:</strong> " . htmlspecialchars($ad['title'] ?? '') . "</p>";
                echo "<p><strong>Text:</strong> " . htmlspecialchars($ad['text'] ?? '') . "</p>";
                echo "<p><strong>URL:</strong> " . htmlspecialchars($ad['url'] ?? '') . "</p>";
                echo "<p><strong>Button Text:</strong> " . htmlspecialchars($ad['button_text'] ?? '') . "</p>";
                echo "<p><strong>Priority:</strong> " . htmlspecialchars($ad['priority'] ?? '0') . "</p>";
                
            } else {
                echo "<h4>Response Analysis:</h4>";
                if (isset($json_data['message'])) {
                    echo "<p><strong>Message:</strong> " . htmlspecialchars($json_data['message']) . "</p>";
                }
                if (isset($json_data['total_active_ads'])) {
                    echo "<p><strong>Total Active Ads:</strong> " . htmlspecialchars($json_data['total_active_ads']) . "</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>API returned empty or non-array response</p>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
            echo json_encode($json_data, JSON_PRETTY_PRINT);
            echo "</pre>";
        }
    }
    
    // Test multiple requests to see randomization
    echo "<h3>Testing Priority-Based Randomization</h3>";
    echo "<p>Making 5 requests to test random ad selection...</p>";
    
    for ($i = 1; $i <= 5; $i++) {
        $test_url = buildAuthenticatedUrl($custom_ads_endpoint, $api_key, $api_secret);
        $test_response = file_get_contents($test_url, false, $context);
        
        if ($test_response !== false) {
            $test_data = json_decode($test_response, true);
            if (is_array($test_data) && !empty($test_data) && isset($test_data[0]['id'])) {
                echo "<p><strong>Request $i:</strong> Ad ID " . $test_data[0]['id'] . " - " . htmlspecialchars($test_data[0]['title'] ?? '') . " (Priority: " . ($test_data[0]['priority'] ?? '0') . ")</p>";
            } else {
                echo "<p><strong>Request $i:</strong> No ad returned</p>";
            }
        } else {
            echo "<p><strong>Request $i:</strong> Failed</p>";
        }
        
        // Small delay between requests
        usleep(100000); // 0.1 second
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Troubleshooting Links</h3>";
echo "<ul>";
echo "<li><a href='debug_custom_ads.php'>Debug Custom Ads Database</a></li>";
echo "<li><a href='test_custom_ads_api.php'>Test API Logic (No Auth)</a></li>";
echo "<li><a href='ad-approval.php'>Ad Approval Management</a></li>";
echo "<li><a href='custom-ads.php'>Custom Ads Management</a></li>";
echo "</ul>";
?>
