<?php
/**
 * 5G Smart VPN Admin Panel - Edit Custom Ad
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Edit Custom Ad';
$success_message = '';
$error_message = '';
$ad = null;

// Get ad ID
$ad_id = (int)($_GET['id'] ?? 0);
if ($ad_id <= 0) {
    header('Location: custom-ads.php');
    exit();
}

// Fetch ad data
$query = "SELECT * FROM custom_ads WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, 'i', $ad_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $ad = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
}

if (!$ad) {
    header('Location: custom-ads.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $text_snippet = trim($_POST['text_snippet'] ?? '');
    $action_url = trim($_POST['action_url'] ?? '');
    $date_start = trim($_POST['date_start'] ?? '');
    $date_end = trim($_POST['date_end'] ?? '');
    $status = isset($_POST['status']) ? 1 : 0;

    // Handle image upload
    $image_url = $ad['image']; // Keep existing image by default (note: column is 'image', not 'image_url')
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'images/'; // Relative to admin_new directory
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['png', 'jpg', 'jpeg', 'gif'];

        if (in_array($file_extension, $allowed_extensions)) {
            $filename = 'ad_' . $ad_id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $filename;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                // Delete old image if different
                if ($image_url && $image_url !== $upload_dir . $filename && file_exists($image_url)) {
                    unlink($image_url);
                }
                $image_url = $upload_dir . $filename;
            }
        }
    }

    // Validation
    $errors = [];
    if (empty($title)) $errors[] = 'Title is required';
    if (empty($text_snippet)) $errors[] = 'Text snippet is required';
    if (empty($action_url)) $errors[] = 'Action URL is required';
    if (empty($date_start)) $errors[] = 'Start date is required';
    if (empty($date_end)) $errors[] = 'End date is required';
    if ($date_start && $date_end && $date_start > $date_end) $errors[] = 'Start date must be before end date';

    if (empty($errors)) {
        // Update ad - using correct column names from database
        $query = "UPDATE custom_ads SET title=?, text=?, image=?, url=?, date_start=?, date_end=?, `on`=? WHERE id=?";

        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, 'ssssssii',
                $title, $text_snippet, $image_url, $action_url, $date_start, $date_end, $status, $ad_id);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = 'Ad updated successfully!';
                // Refresh ad data
                $ad['title'] = $title;
                $ad['text'] = $text_snippet;
                $ad['image'] = $image_url;
                $ad['url'] = $action_url;
                $ad['date_start'] = $date_start;
                $ad['date_end'] = $date_end;
                $ad['on'] = $status;
            } else {
                $error_message = 'Failed to update ad: ' . mysqli_error($conn);
            }
            mysqli_stmt_close($stmt);
        } else {
            $error_message = 'Database error: ' . mysqli_error($conn);
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Edit Custom Ad</h1>
                <p class="page-subtitle">Modify advertisement details for "<?php echo htmlspecialchars($ad['title']); ?>"</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="custom-ads.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        <span class="hide-mobile">Back to Ads</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="ri-check-circle-line"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="ri-error-warning-line"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Ad Configuration</h3>
                    <p class="card-subtitle">Update the advertisement details below</p>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" class="ad-form">
                        <div class="form-grid">
                            <!-- Basic Information -->
                            <div class="form-section">
                                <h4 class="section-title">Basic Information</h4>
                                
                                <div class="form-group">
                                    <label for="title" class="form-label">Ad Title *</label>
                                    <input type="text" id="title" name="title" class="form-control" 
                                           value="<?php echo htmlspecialchars($ad['title']); ?>" 
                                           placeholder="Enter ad title" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="text_snippet" class="form-label">Text Snippet *</label>
                                    <textarea id="text_snippet" name="text_snippet" class="form-control form-textarea"
                                              placeholder="Enter ad description or call-to-action text" required><?php echo htmlspecialchars($ad['text']); ?></textarea>
                                    <small class="form-text">Brief description that will be shown with the ad</small>
                                </div>

                                <div class="form-group">
                                    <label for="action_url" class="form-label">Action URL *</label>
                                    <input type="url" id="action_url" name="action_url" class="form-control"
                                           value="<?php echo htmlspecialchars($ad['url']); ?>"
                                           placeholder="https://example.com" required>
                                    <small class="form-text">URL to redirect when ad is clicked</small>
                                </div>
                            </div>
                            
                            <!-- Image Upload -->
                            <div class="form-section">
                                <h4 class="section-title">Ad Image</h4>
                                
                                <?php if ($ad['image']): ?>
                                <div class="current-image" style="margin-bottom: 1rem;">
                                    <img src="<?php echo $ad['image']; ?>"
                                         alt="Current ad image"
                                         style="max-width: 300px; max-height: 200px; border-radius: 8px; border: 1px solid var(--gray-200);">
                                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: var(--gray-600);">Current image</p>
                                </div>
                                <?php endif; ?>
                                
                                <div class="form-group">
                                    <label for="image" class="form-label">Upload New Image</label>
                                    <div class="form-file">
                                        <input type="file" id="image" name="image" accept="image/*">
                                        <label for="image" class="form-file-label">
                                            <i class="ri-upload-cloud-line" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                                            <div>Click to upload new image</div>
                                            <small>PNG, JPG, GIF up to 5MB (optional)</small>
                                        </label>
                                    </div>
                                    <small class="form-text">Leave empty to keep current image. Recommended size: 300x200px</small>
                                </div>
                            </div>
                            
                            <!-- Schedule -->
                            <div class="form-section">
                                <h4 class="section-title">Schedule</h4>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="date_start" class="form-label">Start Date *</label>
                                        <input type="date" id="date_start" name="date_start" class="form-control" 
                                               value="<?php echo $ad['date_start']; ?>" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="date_end" class="form-label">End Date *</label>
                                        <input type="date" id="date_end" name="date_end" class="form-control" 
                                               value="<?php echo $ad['date_end']; ?>" required>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Ad Status</label>
                                    <div class="form-check">
                                        <label class="switch">
                                            <input type="checkbox" name="status" 
                                                   <?php echo $ad['on'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="form-check-label">Enable this ad</span>
                                    </div>
                                    <small class="form-text">Ad will only be shown if enabled and within the date range</small>
                                </div>
                            </div>
                            
                            <!-- Performance Stats -->
                            <div class="form-section">
                                <h4 class="section-title">Performance Statistics</h4>
                                
                                <div class="stats-row">
                                    <div class="stat-item">
                                        <h4 class="stat-value"><?php echo number_format($ad['view_count']); ?></h4>
                                        <p class="stat-label">Total Views</p>
                                    </div>
                                    <div class="stat-item">
                                        <h4 class="stat-value"><?php echo number_format($ad['click_count']); ?></h4>
                                        <p class="stat-label">Total Clicks</p>
                                    </div>
                                    <div class="stat-item">
                                        <h4 class="stat-value">
                                            <?php echo $ad['view_count'] > 0 ? round(($ad['click_count'] / $ad['view_count']) * 100, 2) : 0; ?>%
                                        </h4>
                                        <p class="stat-label">Click Rate</p>
                                    </div>
                                </div>
                                
                                <div class="performance-info">
                                    <p><strong>Ad ID:</strong> <?php echo $ad['id']; ?></p>
                                    <p><strong>Status:</strong> <?php echo $ad['on'] ? 'Active' : 'Inactive'; ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i>
                                Update Ad
                            </button>
                            <a href="custom-ads.php" class="btn btn-secondary">
                                <i class="ri-close-line"></i>
                                Cancel
                            </a>
                            <button type="button" class="btn btn-warning" onclick="previewAd()">
                                <i class="ri-eye-line"></i>
                                Preview
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Preview Modal -->
<div id="previewModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Ad Preview</h3>
            <button class="modal-close" onclick="closePreviewModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="ad-preview">
                <div class="ad-preview-container">
                    <div class="ad-preview-image">
                        <?php if ($ad['image']): ?>
                        <img src="<?php echo $ad['image']; ?>" alt="Ad preview">
                        <?php else: ?>
                        <div class="ad-preview-placeholder">
                            <i class="ri-image-line"></i>
                            <span>No image</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="ad-preview-content">
                        <h4 class="ad-preview-title"><?php echo htmlspecialchars($ad['title']); ?></h4>
                        <p class="ad-preview-text"><?php echo htmlspecialchars($ad['text']); ?></p>
                        <a href="#" class="ad-preview-button">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closePreviewModal()">Close</button>
        </div>
    </div>
</div>

<script>
// File upload preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const label = document.querySelector('.form-file-label');
    
    if (file) {
        label.classList.add('has-file');
        label.innerHTML = `
            <i class="ri-image-line" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
            <div>${file.name}</div>
            <small>New file selected</small>
        `;
    } else {
        label.classList.remove('has-file');
        label.innerHTML = `
            <i class="ri-upload-cloud-line" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
            <div>Click to upload new image</div>
            <small>PNG, JPG, GIF up to 5MB (optional)</small>
        `;
    }
});

// Form validation
document.querySelector('.ad-form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    // Validate date range
    const startDate = document.getElementById('date_start').value;
    const endDate = document.getElementById('date_end').value;
    
    if (startDate && endDate && startDate > endDate) {
        document.getElementById('date_end').classList.add('error');
        isValid = false;
        alert('End date must be after start date.');
    }
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields correctly.');
    }
});

// Preview ad
function previewAd() {
    document.getElementById('previewModal').style.display = 'flex';
}

// Close preview modal
function closePreviewModal() {
    document.getElementById('previewModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('previewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePreviewModal();
    }
});
</script>

<style>
.stats-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-600);
    margin: 0 0 0.25rem 0;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.performance-info {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.performance-info p {
    margin: 0.5rem 0;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.ad-preview-container {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    background: white;
}

.ad-preview-image {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    background: var(--gray-100);
}

.ad-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ad-preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

.ad-preview-content {
    flex: 1;
}

.ad-preview-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.ad-preview-text {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.ad-preview-button {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--primary-500);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

@media (max-width: 768px) {
    .stats-row {
        grid-template-columns: 1fr;
    }
    
    .ad-preview-container {
        flex-direction: column;
    }
    
    .ad-preview-image {
        width: 100%;
        height: 120px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
