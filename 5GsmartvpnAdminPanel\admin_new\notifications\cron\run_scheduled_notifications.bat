@echo off
REM 5G Smart VPN Admin Panel - Scheduled Notifications Runner
REM This batch file runs the scheduled notifications processor

echo ========================================
echo 5G Smart VPN - Scheduled Notifications
echo ========================================
echo Starting at %date% %time%
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Log the start
echo [%date% %time%] Starting scheduled notifications processor >> cron_log.txt

REM Run the PHP script
"C:\xampp\php\php.exe" -f "process_scheduled_notifications.php" >> cron_log.txt 2>&1

REM Check if the script ran successfully
if %ERRORLEVEL% EQU 0 (
    echo [%date% %time%] Scheduled notifications processor completed successfully >> cron_log.txt
    echo ✅ Notifications processed successfully
) else (
    echo [%date% %time%] Scheduled notifications processor failed with error code %ERRORLEVEL% >> cron_log.txt
    echo ❌ Error processing notifications - Check cron_log.txt for details
)

echo.
echo Finished at %date% %time%
echo ========================================
echo.

REM Add separator to log file
echo. >> cron_log.txt

REM Pause only if running manually (not from task scheduler)
if "%1" NEQ "auto" (
    pause
)
