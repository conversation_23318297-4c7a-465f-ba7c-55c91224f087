R_DEF: Internal format may change without notice
local
array auth_retry_type
array crm_entries
array crm_values
array tls_directions_entries
array tls_directions_values
array vpn_types
attr? certificate
attr? fileTitle
attr? pstsDividerColor
attr? pstsDividerPadding
attr? pstsDividerWidth
attr? pstsIndicatorColor
attr? pstsIndicatorHeight
attr? pstsPaddingMiddle
attr? pstsScrollOffset
attr? pstsShouldExpand
attr? pstsTabBackground
attr? pstsTabPaddingLeftRight
attr? pstsTextAllCaps
attr? pstsTextAlpha
attr? pstsTextSelectedAlpha
attr? pstsTextSelectedStyle
attr? pstsTextStyle
attr? pstsUnderlineColor
attr? pstsUnderlineHeight
attr? showClear
bool logSildersAlwaysVisible
bool supportFileScheme
color accent
color background_tab_pressed
color dataIn
color dataOut
color gelb
color primary
color primary_dark
color rot
color switchbar
color tab_text
dimen add_button_margin
dimen add_button_margin_topfab
dimen diameter
dimen elevation_high
dimen elevation_low
dimen paddingItemsSidebarLog
dimen round_button_diameter
dimen stdpadding
dimen switchbar_pad
dimen vpn_setting_padding
drawable ic_menu_archive
drawable ic_menu_close_clear_cancel
drawable ic_menu_copy_holo_light
drawable ic_menu_log
drawable ic_menu_pause
drawable ic_menu_play
drawable ic_notification
drawable ic_quick
drawable ic_stat_vpn
drawable ic_stat_vpn_empty_halo
drawable ic_stat_vpn_offline
drawable ic_stat_vpn_outline
drawable vpn_item_settings
id as_servername
id check
id icon
id password
id prompt
id request_autologin
id save_password
id show_password
id username
id warning
layout api_confirm
layout import_as_config
layout launchvpn
layout userpass
mipmap banner_tv
mipmap ic_launcher
plurals days_left
plurals hours_left
plurals minutes_left
plurals months_left
string Search
string Use_no_proxy
string ab_kitkat_mss
string ab_kitkat_mss_title
string ab_kitkat_reconnect
string ab_kitkat_reconnect_title
string ab_lollipop_reinstall
string ab_lollipop_reinstall_title
string ab_not_route_to_vpn
string ab_not_route_to_vpn_title
string ab_only_cidr
string ab_only_cidr_title
string ab_persist_tun
string ab_persist_tun_title
string ab_proxy
string ab_proxy_title
string ab_secondary_users
string ab_secondary_users_title
string ab_tethering_44
string ab_vpn_reachability_44
string ab_vpn_reachability_44_title
string abi_mismatch
string about
string add
string add_new_vpn_hint
string add_profile
string add_profile_name_prompt
string add_remote
string address
string advanced
string advanced_settings
string all_app_prompt
string allow_vpn_changes
string allowed_apps
string allowed_vpn_apps_info
string app
string app_no_longer_exists
string appbehaviour
string apprest_name
string apprest_name_desc
string apprest_ovpn
string apprest_ovpn_desc
string apprest_uuid
string apprest_uuid_desc
string apprest_ver
string apprest_vpnconf
string apprest_vpnlist
string as_servername
string auth_dialog_message
string auth_dialog_title
string auth_failed_behaviour
string auth_pwquery
string auth_username
string avghour
string avgmin
string backup_dns
string basic
string baterry_consumption
string battery_consumption_title
string bits_per_second
string blocklocal_summary
string blocklocal_title
string bouncy_castle
string broken_image_cert
string broken_image_cert_title
string broken_images
string broken_images_faq
string building_configration
string built_by
string ca_title
string cancel
string cancel_connection
string cancel_connection_long
string cancel_connection_query
string cannotparsecert
string cant_read_folder
string change_sorting
string channel_description_background
string channel_description_status
string channel_description_userreq
string channel_name_background
string channel_name_status
string channel_name_userreq
string check_remote_tlscert
string check_remote_tlscert_title
string chipher_dialog_message
string cipher_dialog_title
string clear
string clear_external_apps
string clear_log
string clear_log_on_connect
string clearappsdialog
string client_behaviour
string client_certificate_title
string client_key_title
string client_no_certificate
string client_pkcs12_title
string complete_dn
string config_error_found
string configuration_changed
string configure
string configure_the_vpn
string connect_timeout
string connection_retries
string connectretrymaxmessage
string connectretrymaxtitle
string connectretrymessage
string connectretrywait
string converted_profile
string converted_profile_i
string copied_entry
string copy_of_profile
string copying_log_entries
string copyright_blinktgui
string copyright_bouncycastle
string copyright_file_dialog
string copyright_guicode
string copyright_logo
string copyright_openssl
string copyright_others
string crash_toast_text
string crashdump
string crl_file
string crl_title
string crtext_requested
string custom_config_summary
string custom_config_title
string custom_connection_options
string custom_connection_options_warng
string custom_option_warning
string custom_options_title
string custom_route_format_error
string custom_route_message
string custom_route_message_excluded
string custom_routes_title
string custom_routes_title_excluded
string data_in
string data_out
string debug_build
string default_cipherlist_test
string default_route_summary
string defaultport
string defaultserver
string defaultvpn
string defaultvpnsummary
string delete
string deprecated_tls_remote
string device_specific
string disallowed_vpn_apps_info
string dns
string dns1_summary
string dns_add_error
string dns_override_summary
string dns_server
string dns_server_info
string donatePlayStore
string downloaded_data
string duplicate_profile_name
string duplicate_profile_title
string duplicate_vpn
string edit_profile_title
string edit_vpn
string enabled_connection_entry
string enableproxyauth
string encryption
string encryption_cipher
string enter_tlscn_dialog
string enter_tlscn_title
string error
string error_extapp_sign
string error_importing_file
string error_orbot_and_proxy_options
string error_reading_config_file
string error_rsa_sign
string export_config_chooser_title
string export_config_title
string extauth_not_configured
string external_authenticator
string extracahint
string faq
string faq_android_clients
string faq_androids_clients_title
string faq_copying
string faq_duplicate_notification
string faq_duplicate_notification_title
string faq_hint
string faq_howto
string faq_howto_shortcut
string faq_howto_title
string faq_killswitch
string faq_killswitch_title
string faq_remote_api
string faq_remote_api_title
string faq_routing
string faq_routing_title
string faq_security
string faq_security_title
string faq_shortcut
string faq_system_dialog_xposed
string faq_system_dialogs
string faq_system_dialogs_title
string faq_tap_mode
string faq_tethering
string faq_vpndialog43
string faq_vpndialog43_title
string file_dialog
string file_explorer_tab
string file_icon
string file_nothing_selected
string file_select
string files_missing_hint
string float_summary
string float_title
string full_licenses
string gbits_per_second
string generalsettings
string generated_config
string generated_config_summary
string getproxy_error
string graph
string help_translate
string hwkeychain
string ics_openvpn_log_file
string ignore
string ignore_multicast_route
string ignore_routes_summary
string ignored_pushed_routes
string import_config
string import_config_error
string import_configuration_file
string import_content_resolve_error
string import_could_not_open
string import_done
string import_error_message
string import_from_as
string import_log
string import_vpn
string import_warning_custom_options
string imported_from_file
string importing_config
string importpkcs12fromconfig
string info_from_server
string inline_file_data
string inline_file_tab
string install_keychain
string internal_web_view
string ip_add_error
string ip_looks_like_subnet
string ip_not_cidr
string ipdns
string ipv4
string ipv4_address
string ipv4_dialog_title
string ipv4_format_error
string ipv6
string ipv6_address
string ipv6_dialog_tile
string jelly_keystore_alphanumeric_bug
string kbits_per_second
string keep
string keyChainAccessError
string keychain_access
string keychain_nocacert
string last5minutes
string last_openvpn_tun_config
string lastdumpdate
string loading
string local_ip_info
string location
string logCleared
string log_no_last_vpn
string log_verbosity_level
string logview_options
string lzo
string lzo_copyright
string make_selection_inline
string management_socket_closed
string mbits_per_second
string menu_add_profile
string menu_import
string menu_import_short
string menu_use_inline_data
string message_no_user_edit
string minidump_generated
string missing_ca_certificate
string missing_certificates
string missing_tlsauth
string mobile_info
string mssfix_checkbox
string mssfix_dialogtitle
string mssfix_invalid_value
string mssfix_value_dialog
string mtu_invalid_value
string netchange
string netchange_summary
string netstatus
string no_allowed_app
string no_bind
string no_ca_cert_selected
string no_certificate
string no_data
string no_default_vpn_set
string no_error_found
string no_external_app_allowed
string no_keystore_cert_selected
string no_orbotfound
string no_remote_defined
string no_vpn_profiles_defined
string no_vpn_support_image
string nobind_summary
string notenoughdata
string notifcation_title
string notifcation_title_notconnect
string nought_alwayson_warning
string novpn_selected
string obscure
string official_build
string onbootrestart
string onbootrestartsummary
string openssl
string openssl_cipher_name
string openssl_error
string opentun_no_ipaddr
string openurl_requested
string openvpn
string openvpn3_nostatickeys
string openvpn3_pkcs12
string openvpn3_socksproxy
string openvpn_is_no_free_vpn
string openvpn_log
string opevpn_copyright
string osslspeedtest
string override_dns
string owner_fix
string owner_fix_summary
string packet_auth
string password
string pauseVPN
string payload_options
string permission_description
string permission_icon_app
string permission_revoked
string persistent_tun_title
string persisttun_summary
string pkcs12_file_encryption_key
string pkcs12pwquery
string port
string private_key_password
string profilename
string prompt
string protocol
string proxy
string pull_off_summary
string pull_on_summary
string pushpeerinfo
string pushpeerinfosummary
string pw_query_hint
string pw_request_dialog_prompt
string pw_request_dialog_title
string qs_connect
string qs_disconnect
string qs_title
string query_delete_remote
string query_permissions_sdcard
string random_host_prefix
string random_host_summary
string rdn
string rdn_prefix
string reconnect
string reconnection_settings
string remote_no_server_selected
string remote_random
string remote_tlscn_check_summary
string remote_tlscn_check_title
string remote_trust
string remote_warning
string remotetlsnote
string remove_connection_entry
string remove_vpn
string remove_vpn_query
string request_autologin
string reread_log
string restart
string restart_vpn_after_change
string resumevpn
string route_not_cidr
string route_not_netip
string route_rejected
string routes_debug
string routes_info_excl
string routes_info_incl
string routing
string running_test
string samsung_broken
string samsung_broken_title
string save_password
string screen_nopersistenttun
string screenoff_pause
string screenoff_summary
string screenoff_title
string searchdomain
string secondary_dns_message
string select
string select_file
string send
string send_config
string send_logfile
string send_minidump
string send_minidump_summary
string server_list
string service_restarted
string session_ipv4string
string session_ipv6string
string setting_loadtun
string setting_loadtun_summary
string settings_auth
string shortcut_profile_notfound
string show_log
string show_log_summary
string show_log_window
string show_password
string sort
string sorted_az
string sorted_lru
string speed_waiting
string start_vpn_ticker
string start_vpn_title
string state_add_routes
string state_assign_ip
string state_auth
string state_auth_failed
string state_auth_pending
string state_connected
string state_connecting
string state_disconnected
string state_exiting
string state_get_config
string state_nonetwork
string state_noprocess
string state_reconnecting
string state_resolve
string state_screenoff
string state_tcp_connect
string state_user_vpn_password
string state_user_vpn_password_cancelled
string state_user_vpn_permission
string state_user_vpn_permission_cancelled
string state_userpause
string state_wait
string state_waitconnectretry
string state_waitorbot
string static_keys_info
string statusline_bytecount
string summary_block_address_families
string tap_faq2
string tap_faq3
string tap_mode
string test_algoirhtms
string thanks_for_donation
string timestamp_iso
string timestamp_short
string timestamps
string timestamps_none
string title_activity_open_sslspeed
string title_block_address_families
string title_cancel
string tls_auth_file
string tls_authentication
string tls_cipher_alert
string tls_cipher_alert_title
string tls_direction
string tls_key_auth
string tls_remote_deprecated
string tls_settings
string tor_orbot
string translationby
string tun_error_helpful
string tun_open_error
string unhandled_exception
string unhandled_exception_context
string unknown_state
string uploaded_data
string useLZO
string useTLSAuth
string use_default_title
string use_logarithmic_scale
string use_pull
string use_system_proxy
string use_system_proxy_summary
string userpw_file
string using_proxy
string version_and_later
string version_info
string version_upto
string volume_byte
string volume_gbyte
string volume_kbyte
string volume_mbyte
string vpn_allow_bypass
string vpn_allow_radio
string vpn_allowed_apps
string vpn_disallow_radio
string vpn_import_hint
string vpn_launch_title
string vpn_list_title
string vpn_shortcut
string vpn_status
string vpn_tethering_title
string vpn_type
string vpnbehaviour
string vpnselected
string warn_no_dns
string weakmd
string weakmd_title
style blinkt
style blinkt.baseTheme
style blinkt.dialog
styleable FileSelectLayout fileTitle certificate showClear
styleable PagerSlidingTabStrip pstsIndicatorColor pstsUnderlineColor pstsDividerColor pstsDividerWidth pstsIndicatorHeight pstsUnderlineHeight pstsDividerPadding pstsTabPaddingLeftRight pstsScrollOffset pstsTabBackground pstsShouldExpand pstsTextAllCaps pstsPaddingMiddle pstsTextStyle pstsTextSelectedStyle pstsTextAlpha pstsTextSelectedAlpha
xml app_restrictions
