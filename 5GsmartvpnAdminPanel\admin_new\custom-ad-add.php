<?php
/**
 * 5G Smart VPN Admin Panel - Add Custom Ad
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Add Custom Ad';
$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_ad'])) {
    $title = trim($_POST['title']);
    $text_snippet = trim($_POST['text_snippet']);
    $action_url = trim($_POST['action_url'] ?? '');
    $url_type = $_POST['url_type'] ?? 'website';
    $button_text = trim($_POST['button_text'] ?? '');
    $date_start = $_POST['date_start'];
    $date_end = $_POST['date_end'];
    $status = isset($_POST['status']) ? 1 : 0;
    
    // Validate required fields
    if (empty($title) || empty($text_snippet) || empty($date_start) || empty($date_end)) {
        $error_message = 'Please fill in all required fields.';
    } elseif (strtotime($date_end) < strtotime($date_start)) {
        $error_message = 'End date must be after start date.';
    } else {
        // Handle image upload
        $image_url = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/ads/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $filename = 'ad_' . time() . '_' . uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $image_url = 'uploads/ads/' . $filename;
                } else {
                    $error_message = 'Failed to upload image.';
                }
            } else {
                $error_message = 'Invalid image format. Please use JPG, PNG, GIF, or WebP.';
            }
        }
        
        if (empty($error_message)) {
            // Auto-detect URL type and button text if not provided
            if (!empty($action_url) && empty($button_text)) {
                $url_lower = strtolower($action_url);
                if (strpos($url_lower, 'play.google.com') !== false) {
                    $url_type = 'playstore';
                    $button_text = 'Install';
                } elseif (strpos($url_lower, 'apps.apple.com') !== false || strpos($url_lower, 'itunes.apple.com') !== false) {
                    $url_type = 'appstore';
                    $button_text = 'Install';
                } else {
                    $url_type = 'website';
                    $button_text = 'Visit';
                }
            }

            // Insert into database using prepared statement
            $stmt = $conn->prepare("INSERT INTO custom_ads (title, text, image, url, url_type, button_text, date_start, date_end, `on`, view_count, click_count, is_approved, priority) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0, 1, 1)");
            $stmt->bind_param("ssssssssi", $title, $text_snippet, $image_url, $action_url, $url_type, $button_text, $date_start, $date_end, $status);

            if ($stmt->execute()) {
                $success_message = 'Custom ad created successfully!';
                // Clear form data
                $title = $text_snippet = $action_url = $button_text = $date_start = $date_end = '';
                $url_type = 'website';
                $status = 0;
            } else {
                $error_message = 'Failed to create ad: ' . $stmt->error;
            }
            $stmt->close();
        }
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Add Custom Ad</h1>
                <p class="page-subtitle">Create a new custom advertisement</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="custom-ads.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        <span class="hide-mobile">Back to Ads</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Add Ad Form -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Ad Information</h3>
                        </div>
                        <div class="card-body">
                            <!-- Error/Success Messages -->
                            <?php if ($error_message): ?>
                                <div class="alert alert-error">
                                    <i class="ri-error-warning-line"></i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success">
                                    <i class="ri-check-line"></i>
                                    <?php echo htmlspecialchars($success_message); ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="" enctype="multipart/form-data" class="form-modern">
                                <div class="form-group">
                                    <label for="title" class="form-label">Ad Title *</label>
                                    <input type="text" 
                                           id="title" 
                                           name="title" 
                                           class="form-control" 
                                           placeholder="Enter ad title"
                                           value="<?php echo isset($title) ? htmlspecialchars($title) : ''; ?>"
                                           required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="text_snippet" class="form-label">Ad Description *</label>
                                    <textarea id="text_snippet" 
                                              name="text_snippet" 
                                              class="form-control" 
                                              rows="4"
                                              placeholder="Enter ad description or text snippet"
                                              required><?php echo isset($text_snippet) ? htmlspecialchars($text_snippet) : ''; ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="image" class="form-label">Ad Image</label>
                                    <input type="file"
                                           id="image"
                                           name="image"
                                           class="form-control"
                                           accept="image/*">
                                    <small class="form-text">Supported formats: JPG, PNG, GIF, WebP. Max size: 5MB</small>
                                </div>

                                <div class="form-group">
                                    <label for="action_url" class="form-label">Action URL</label>
                                    <input type="url"
                                           id="action_url"
                                           name="action_url"
                                           class="form-control"
                                           placeholder="https://example.com"
                                           value="<?php echo isset($action_url) ? htmlspecialchars($action_url) : ''; ?>">
                                    <small class="form-text">URL to open when user clicks the ad button</small>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url_type" class="form-label">URL Type</label>
                                            <select id="url_type" name="url_type" class="form-control">
                                                <option value="website" <?php echo (isset($url_type) && $url_type === 'website') ? 'selected' : ''; ?>>Website</option>
                                                <option value="playstore" <?php echo (isset($url_type) && $url_type === 'playstore') ? 'selected' : ''; ?>>Play Store</option>
                                                <option value="appstore" <?php echo (isset($url_type) && $url_type === 'appstore') ? 'selected' : ''; ?>>App Store</option>
                                                <option value="other" <?php echo (isset($url_type) && $url_type === 'other') ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                            <small class="form-text">Type of URL for smart button detection</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="button_text" class="form-label">Button Text</label>
                                            <input type="text"
                                                   id="button_text"
                                                   name="button_text"
                                                   class="form-control"
                                                   placeholder="Visit, Install, Download, etc."
                                                   value="<?php echo isset($button_text) ? htmlspecialchars($button_text) : ''; ?>">
                                            <small class="form-text">Custom text for the action button (leave empty for auto-detection)</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_start" class="form-label">Start Date *</label>
                                            <input type="date" 
                                                   id="date_start" 
                                                   name="date_start" 
                                                   class="form-control"
                                                   value="<?php echo isset($date_start) ? $date_start : date('Y-m-d'); ?>"
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_end" class="form-label">End Date *</label>
                                            <input type="date" 
                                                   id="date_end" 
                                                   name="date_end" 
                                                   class="form-control"
                                                   value="<?php echo isset($date_end) ? $date_end : date('Y-m-d', strtotime('+30 days')); ?>"
                                                   required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               id="status" 
                                               name="status" 
                                               class="form-check-input"
                                               <?php echo (isset($status) && $status) ? 'checked' : ''; ?>>
                                        <label for="status" class="form-check-label">
                                            Activate ad immediately
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" name="add_ad" class="btn btn-primary">
                                        <i class="ri-save-line"></i>
                                        Create Ad
                                    </button>
                                    <a href="custom-ads.php" class="btn btn-secondary">
                                        <i class="ri-close-line"></i>
                                        Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Ad Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Ad Preview</h3>
                        </div>
                        <div class="card-body">
                            <div class="ad-preview" id="adPreview">
                                <div class="preview-image" id="previewImage">
                                    <div class="placeholder-image">
                                        <i class="ri-image-line"></i>
                                        <p>No image selected</p>
                                    </div>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title" id="previewTitle">Ad Title</h4>
                                    <p class="preview-description" id="previewDescription">Ad description will appear here...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Tips for Better Ads</h3>
                        </div>
                        <div class="card-body">
                            <ul class="tips-list">
                                <li><i class="ri-check-line"></i> Use clear, compelling titles</li>
                                <li><i class="ri-check-line"></i> Keep descriptions concise</li>
                                <li><i class="ri-check-line"></i> Use high-quality images</li>
                                <li><i class="ri-check-line"></i> Set appropriate date ranges</li>
                                <li><i class="ri-check-line"></i> Test different variations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.row {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.col-lg-8 {
    flex: 0 0 calc(66.666% - var(--spacing-lg));
}

.col-lg-4 {
    flex: 0 0 calc(33.333% - var(--spacing-lg));
}

.col-md-6 {
    flex: 0 0 calc(50% - var(--spacing-sm));
}

.form-modern {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-check-input {
    width: 18px;
    height: 18px;
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.form-text {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.ad-preview {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.preview-image {
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-image {
    text-align: center;
    color: var(--gray-400);
}

.placeholder-image i {
    font-size: 3rem;
    margin-bottom: var(--spacing-sm);
    display: block;
}

.preview-content {
    padding: var(--spacing-md);
}

.preview-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-sm);
}

.preview-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.tips-list i {
    color: var(--success-600);
    font-size: 1rem;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-error {
    background: var(--error-50);
    color: var(--error-700);
    border: 1px solid var(--error-200);
}

.alert-success {
    background: var(--success-50);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .col-lg-8,
    .col-lg-4,
    .col-md-6 {
        flex: 1 1 100%;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('text_snippet');
    const imageInput = document.getElementById('image');
    
    const previewTitle = document.getElementById('previewTitle');
    const previewDescription = document.getElementById('previewDescription');
    const previewImage = document.getElementById('previewImage');
    
    // Update title preview
    titleInput.addEventListener('input', function() {
        previewTitle.textContent = this.value || 'Ad Title';
    });
    
    // Update description preview
    descriptionInput.addEventListener('input', function() {
        previewDescription.textContent = this.value || 'Ad description will appear here...';
    });
    
    // Update image preview
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        } else {
            previewImage.innerHTML = `
                <div class="placeholder-image">
                    <i class="ri-image-line"></i>
                    <p>No image selected</p>
                </div>
            `;
        }
    });
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date_start').min = today;
    document.getElementById('date_end').min = today;
    
    // Update end date minimum when start date changes
    document.getElementById('date_start').addEventListener('change', function() {
        document.getElementById('date_end').min = this.value;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
