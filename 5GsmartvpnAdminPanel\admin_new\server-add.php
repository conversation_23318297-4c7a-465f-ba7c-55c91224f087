<?php
/**
 * 5G Smart VPN Admin Panel - Add Server
 * Based on original admin/server_add.php with Premium/Free options
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Add New Server';
$success_message = '';
$error_message = '';

// Get available flags
function getAvailableFlags() {
    $flagDir = __DIR__ . '/flag/';
    $flags = [];

    if (is_dir($flagDir)) {
        $files = scandir($flagDir);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'png') {
                $flagName = pathinfo($file, PATHINFO_FILENAME);
                // Convert filename to readable country name
                $countryName = ucwords(str_replace(['-', '_'], ' ', $flagName));
                $flags[$flagName] = [
                    'name' => $countryName,
                    'file' => $file,
                    'path' => 'flag/' . $file
                ];
            }
        }
    }

    // Sort by country name
    uasort($flags, function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });

    return $flags;
}

$availableFlags = getAvailableFlags();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['insert'])) {
    $name = trim($_POST['name'] ?? '');
    $username = trim($_POST['user'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $selectedFlag = trim($_POST['flag_select'] ?? '');
    $flagURL = !empty($selectedFlag) ? 'flag/' . $selectedFlag . '.png' : '';
    $configFile = mysqli_real_escape_string($conn, trim($_POST['config'] ?? ''));
    $type = (int)($_POST['type'] ?? 1);

    // Validation
    $errors = [];
    if (empty($name)) $errors[] = 'Country name is required';
    if (empty($username)) $errors[] = 'Username is required';
    if (empty($password)) $errors[] = 'Password is required';
    if (empty($configFile)) $errors[] = 'Config file is required';

    if (empty($errors)) {
        // Insert server using the original structure
        $insert = "INSERT INTO `servers`(`name`, `username`, `password`, `configFile`, `flagURL`, `type`)
                   VALUES ('$name','$username','$password','$configFile','$flagURL','$type')";

        $query = mysqli_query($conn, $insert);
        if ($query) {
            $success_message = 'Server added successfully!';
            // Clear form data
            $_POST = [];
        } else {
            $error_message = 'Failed to add server. ' . mysqli_error($conn);
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Add New Server</h1>
                <p class="page-subtitle">Configure a new VPN server for your network</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="servers.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        <span class="hide-mobile">Back to Servers</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="ri-check-circle-line"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="ri-error-warning-line"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Server Configuration</h3>
                    <p class="card-subtitle">Enter the server details below</p>
                </div>
                <div class="card-body">
                    <form method="POST" class="server-form">
                        <div class="form-grid">
                            <!-- Basic Information -->
                            <div class="form-section">
                                <h4 class="section-title">Basic Information</h4>

                                <div class="form-group">
                                    <label for="name" class="form-label">Country Name *</label>
                                    <input type="text" id="name" name="name" class="form-control"
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="e.g., United States" required>
                                </div>

                                <div class="form-group">
                                    <label for="user" class="form-label">User Name *</label>
                                    <input type="text" id="user" name="user" class="form-control"
                                           value="<?php echo htmlspecialchars($_POST['user'] ?? ''); ?>"
                                           placeholder="VPN username" required>
                                </div>

                                <div class="form-group">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="text" id="password" name="password" class="form-control"
                                           value="<?php echo htmlspecialchars($_POST['password'] ?? ''); ?>"
                                           placeholder="VPN password" required>
                                </div>

                                <div class="form-group">
                                    <label for="flag_select" class="form-label">Country Flag</label>
                                    <div class="flag-selection-container">
                                        <select id="flag_select" name="flag_select" class="form-control form-select">
                                            <option value="">Select a country flag...</option>
                                            <?php foreach ($availableFlags as $flagKey => $flagData): ?>
                                                <option value="<?php echo htmlspecialchars($flagKey); ?>"
                                                        <?php echo ($_POST['flag_select'] ?? '') === $flagKey ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($flagData['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="flag-preview">
                                            <img id="flag_preview" src="" alt="Flag preview" style="display: none; width: 32px; height: 24px; margin-left: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                    </div>
                                    <small class="form-text">Choose the country flag for this server</small>
                                </div>
                            </div>

                            <!-- Configuration -->
                            <div class="form-section">
                                <h4 class="section-title">Configuration</h4>

                                <div class="form-group">
                                    <label for="config" class="form-label">Config File *</label>
                                    <textarea id="config" name="config" class="form-control" rows="8"
                                              placeholder="Paste your VPN configuration here..." required><?php echo htmlspecialchars($_POST['config'] ?? ''); ?></textarea>
                                    <small class="form-text">Paste the complete VPN configuration file content</small>
                                </div>

                                <div class="form-group">
                                    <label for="type" class="form-label">Server Type *</label>
                                    <select id="type" name="type" class="form-control form-select">
                                        <option value="1" <?php echo ($_POST['type'] ?? 1) == 1 ? 'selected' : ''; ?>>Free Server</option>
                                        <option value="0" <?php echo ($_POST['type'] ?? 1) == 0 ? 'selected' : ''; ?>>Premium Server</option>
                                    </select>
                                    <small class="form-text">Choose whether this server is free or premium for users</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" name="insert" class="btn btn-primary">
                                <i class="ri-save-line"></i>
                                Add Server
                            </button>
                            <a href="servers.php" class="btn btn-secondary">
                                <i class="ri-close-line"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
// Flag data for JavaScript
const flagData = <?php echo json_encode($availableFlags); ?>;

// Form validation
document.querySelector('.server-form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields.');
    }
});

// Auto-resize textarea
document.getElementById('config').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Flag selection and preview
document.getElementById('flag_select').addEventListener('change', function() {
    const selectedFlag = this.value;
    const flagPreview = document.getElementById('flag_preview');

    if (selectedFlag && flagData[selectedFlag]) {
        flagPreview.src = flagData[selectedFlag].path;
        flagPreview.style.display = 'inline-block';
        flagPreview.alt = flagData[selectedFlag].name + ' flag';
    } else {
        flagPreview.style.display = 'none';
        flagPreview.src = '';
    }
});

// Initialize flag preview if there's a selected value
document.addEventListener('DOMContentLoaded', function() {
    const flagSelect = document.getElementById('flag_select');
    if (flagSelect.value) {
        flagSelect.dispatchEvent(new Event('change'));
    }
});
</script>

<?php include 'includes/footer.php'; ?>
