-- Add a test custom ad with current dates for testing
-- This script adds an active custom ad that should be visible in the app

INSERT INTO `custom_ads` (`title`, `image`, `text`, `url`, `date_start`, `date_end`, `on`, `view_count`, `click_count`) 
VALUES (
    'Welcome to 5G Smart VPN!', 
    'images/test_ad.png', 
    'Experience lightning-fast and secure VPN connections. Protect your privacy and access content from anywhere in the world!', 
    'https://example.com/welcome', 
    CURDATE(), 
    DATE_ADD(CURDATE(), INTERVAL 30 DAY), 
    1, 
    0, 
    0
);

-- Update existing ads to have current dates and new fields for testing
UPDATE `custom_ads`
SET
    `date_start` = CURDATE(),
    `date_end` = DATE_ADD(CURDATE(), INTERVAL 7 DAY),
    `on` = 1,
    `url_type` = CASE
        WHEN url LIKE '%play.google.com%' THEN 'playstore'
        WHEN url LIKE '%apps.apple.com%' THEN 'appstore'
        ELSE 'website'
    END,
    `button_text` = CASE
        WHEN url LIKE '%play.google.com%' THEN 'Install'
        WHEN url LIKE '%apps.apple.com%' THEN 'Install'
        ELSE 'Visit'
    END,
    `is_approved` = 1,
    `priority` = 1
WHERE `id` IN (1, 2);

-- Verify the ads are active
SELECT 
    id, 
    title, 
    image, 
    text, 
    url, 
    date_start, 
    date_end, 
    `on` as active,
    view_count,
    click_count
FROM custom_ads 
WHERE `on` = 1 
AND date_start <= CURDATE() 
AND date_end >= CURDATE()
ORDER BY id;
