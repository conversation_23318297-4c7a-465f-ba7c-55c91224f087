<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2012-2016 <PERSON><PERSON>
  ~ Distributed under the GNU GPL v2 with additional terms. For full terms see the file doc/LICENSE.txt
  -->

<resources>
     <declare-styleable name="FileSelectLayout">
       <attr name="fileTitle" format="string|reference" />
      <attr name="certificate" format="boolean" />
<!--     <attr name="taskid" format="integer" /> -->
       <attr name="showClear" format="boolean" />
   </declare-styleable>

    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsDividerWidth" format="dimension" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerPadding" format="dimension" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsTextAllCaps" format="boolean" />
        <attr name="pstsPaddingMiddle" format="boolean" />
        <attr name="pstsTextStyle">
            <flag name="normal" value="0x0" />
            <flag name="bold" value="0x1" />
            <flag name="italic" value="0x2" />
        </attr>
        <attr name="pstsTextSelectedStyle">
            <flag name="normal" value="0x0" />
            <flag name="bold" value="0x1" />
            <flag name="italic" value="0x2" />
        </attr>
        <attr name="pstsTextAlpha" format="float" />
        <attr name="pstsTextSelectedAlpha" format="float" />
    </declare-styleable>
</resources>
