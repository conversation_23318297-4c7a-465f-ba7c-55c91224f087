<?php
/**
 * 5G Smart VPN Admin Panel - Activity Logs
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Activity Logs';

// Get filter parameters
$log_level = $_GET['level'] ?? 'all';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$search = $_GET['search'] ?? '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 50;
$offset = ($page - 1) * $limit;

// Get real log data from database
$logs = [];
$total_logs = 0;

// Build WHERE clause for filtering
$where_conditions = [];
$params = [];
$types = "";

// Date filtering
if ($date_from && $date_to) {
    $where_conditions[] = "DATE(created_at) BETWEEN ? AND ?";
    $params[] = $date_from;
    $params[] = $date_to;
    $types .= "ss";
}

// Search filtering
if ($search) {
    $where_conditions[] = "(action LIKE ? OR description LIKE ? OR admin_username LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "sss";
}

// Level filtering (map to actions since admin_logs doesn't have level field)
if ($log_level !== 'all') {
    $level_actions = [
        'info' => ['login', 'logout', 'dashboard_access', 'view'],
        'warning' => ['update', 'change', 'modify'],
        'error' => ['delete', 'clear', 'remove'],
        'debug' => ['debug', 'test']
    ];

    if (isset($level_actions[$log_level])) {
        $placeholders = str_repeat('?,', count($level_actions[$log_level]) - 1) . '?';
        $where_conditions[] = "action IN ($placeholders)";
        $params = array_merge($params, $level_actions[$log_level]);
        $types .= str_repeat('s', count($level_actions[$log_level]));
    }
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(' AND ', $where_conditions) : "";

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM admin_logs $where_clause";
if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_logs = $count_result->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $count_result = $conn->query($count_sql);
    $total_logs = $count_result->fetch_assoc()['total'];
}

$total_pages = ceil($total_logs / $limit);

// Get paginated logs
$logs_sql = "SELECT id, admin_id, admin_username, action, description, ip_address, user_agent, created_at
             FROM admin_logs $where_clause
             ORDER BY created_at DESC
             LIMIT ? OFFSET ?";

$final_params = array_merge($params, [$limit, $offset]);
$final_types = $types . "ii";

if (!empty($params) || true) {
    $logs_stmt = $conn->prepare($logs_sql);
    $logs_stmt->bind_param($final_types, ...$final_params);
    $logs_stmt->execute();
    $logs_result = $logs_stmt->get_result();

    while ($row = $logs_result->fetch_assoc()) {
        // Map action to log level for display
        $level = 'info'; // default
        if (in_array($row['action'], ['delete', 'clear', 'remove'])) {
            $level = 'error';
        } elseif (in_array($row['action'], ['update', 'change', 'modify'])) {
            $level = 'warning';
        } elseif (in_array($row['action'], ['debug', 'test'])) {
            $level = 'debug';
        }

        $logs[] = [
            'id' => $row['id'],
            'timestamp' => $row['created_at'],
            'level' => $level,
            'action' => $row['action'],
            'message' => $row['description'],
            'ip_address' => $row['ip_address'],
            'user_agent' => $row['user_agent'],
            'admin_username' => $row['admin_username']
        ];
    }
    $logs_stmt->close();
}

// Get log statistics
$stats_sql = "SELECT
    COUNT(*) as total_logs,
    SUM(CASE WHEN action IN ('login', 'logout', 'dashboard_access', 'view') THEN 1 ELSE 0 END) as info_logs,
    SUM(CASE WHEN action IN ('update', 'change', 'modify') THEN 1 ELSE 0 END) as warning_logs,
    SUM(CASE WHEN action IN ('delete', 'clear', 'remove') THEN 1 ELSE 0 END) as error_logs,
    SUM(CASE WHEN action IN ('debug', 'test') THEN 1 ELSE 0 END) as debug_logs
    FROM admin_logs";

$stats_result = $conn->query($stats_sql);
$stats = $stats_result->fetch_assoc();

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Activity Logs</h1>
                <p class="page-subtitle">Monitor system activities and troubleshoot issues</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportLogs()">
                        <i class="ri-download-line"></i>
                        <span class="hide-mobile">Export Logs</span>
                    </button>
                    <button class="btn btn-warning" onclick="showClearLogsModal()">
                        <i class="ri-delete-bin-line"></i>
                        <span class="hide-mobile">Clear Logs</span>
                    </button>
                    <button class="btn btn-primary" onclick="refreshLogs()">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Refresh</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-file-list-3-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['total_logs']); ?></h3>
                        <p class="stat-label">Total Logs</p>
                        <span class="stat-change neutral">
                            <i class="ri-time-line"></i>
                            Last 30 days
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-information-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['info_logs']); ?></h3>
                        <p class="stat-label">Info Logs</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            Normal activity
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-alert-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['warning_logs']); ?></h3>
                        <p class="stat-label">Warnings</p>
                        <span class="stat-change warning">
                            <i class="ri-error-warning-line"></i>
                            Needs attention
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-error-warning-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['error_logs']); ?></h3>
                        <p class="stat-label">Errors</p>
                        <span class="stat-change negative">
                            <i class="ri-close-circle-line"></i>
                            Critical issues
                        </span>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Filter Logs</h3>
                </div>
                <div class="card-body">
                    <form method="GET" class="log-filters">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="level" class="form-label">Log Level</label>
                                <select id="level" name="level" class="form-control form-select">
                                    <option value="all" <?php echo $log_level === 'all' ? 'selected' : ''; ?>>All Levels</option>
                                    <option value="info" <?php echo $log_level === 'info' ? 'selected' : ''; ?>>Info</option>
                                    <option value="warning" <?php echo $log_level === 'warning' ? 'selected' : ''; ?>>Warning</option>
                                    <option value="error" <?php echo $log_level === 'error' ? 'selected' : ''; ?>>Error</option>
                                    <option value="debug" <?php echo $log_level === 'debug' ? 'selected' : ''; ?>>Debug</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>" class="form-control">
                            </div>

                            <div class="filter-group">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>" class="form-control">
                            </div>

                            <div class="filter-group">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search logs..." class="form-control">
                            </div>

                            <div class="filter-group">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-search-line"></i>
                                    Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Logs Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Activity Logs</h3>
                    <div class="card-actions">
                        <span class="text-muted">Showing <?php echo count($logs); ?> of <?php echo $total_logs; ?> logs</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table logs-table">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Level</th>
                                    <th>Action</th>
                                    <th>Message</th>
                                    <th>IP Address</th>
                                    <th>Admin</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                <tr class="log-row log-<?php echo $log['level']; ?>">
                                    <td class="log-timestamp">
                                        <?php echo date('M j, Y H:i:s', strtotime($log['timestamp'])); ?>
                                    </td>
                                    <td>
                                        <span class="log-level badge <?php echo $log['level']; ?>">
                                            <?php echo ucfirst($log['level']); ?>
                                        </span>
                                    </td>
                                    <td class="log-action">
                                        <?php echo htmlspecialchars($log['action']); ?>
                                    </td>
                                    <td class="log-message">
                                        <?php echo htmlspecialchars($log['message']); ?>
                                    </td>
                                    <td class="log-ip">
                                        <?php echo htmlspecialchars($log['ip_address']); ?>
                                    </td>
                                    <td class="log-admin">
                                        <?php echo htmlspecialchars($log['admin_username']); ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-secondary" onclick="viewLogDetails(<?php echo $log['id']; ?>)" title="View Details">
                                                <i class="ri-eye-line"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteLog(<?php echo $log['id']; ?>)" title="Delete Log">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>

                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="7" class="text-center" style="padding: 3rem;">
                                        <div class="empty-state">
                                            <i class="ri-file-list-3-line empty-state-icon"></i>
                                            <h3>No Logs Found</h3>
                                            <p>No logs match your current filter criteria.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <div class="pagination">
                        <?php for ($i = 1; $i <= min(10, $total_pages); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&level=<?php echo $log_level; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&search=<?php echo urlencode($search); ?>"
                               class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($total_pages > 10): ?>
                            <span class="pagination-dots">...</span>
                            <a href="?page=<?php echo $total_pages; ?>&level=<?php echo $log_level; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&search=<?php echo urlencode($search); ?>"
                               class="pagination-btn">
                                <?php echo $total_pages; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<!-- Log Details Modal -->
<div id="logModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Log Details</h3>
            <button class="modal-close" onclick="closeLogModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="logDetails">
                <!-- Log details will be loaded here -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeLogModal()">Close</button>
        </div>
    </div>
</div>

<!-- Clear Logs Modal -->
<div id="clearLogsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Clear Logs</h3>
            <button class="modal-close" onclick="closeClearLogsModal()">&times;</button>
        </div>
        <div class="modal-body">
            <p>Choose how you want to clear the logs:</p>
            <div class="clear-options">
                <div class="option-group">
                    <label>
                        <input type="radio" name="clear_type" value="older" checked>
                        Clear logs older than:
                    </label>
                    <select id="clear_days" class="form-control" style="margin-left: 20px; width: 150px; display: inline-block;">
                        <option value="7">7 days</option>
                        <option value="30" selected>30 days</option>
                        <option value="60">60 days</option>
                        <option value="90">90 days</option>
                    </select>
                </div>
                <div class="option-group">
                    <label>
                        <input type="radio" name="clear_type" value="level">
                        Clear logs by level:
                    </label>
                    <select id="clear_level" class="form-control" style="margin-left: 20px; width: 150px; display: inline-block;">
                        <option value="all">All levels</option>
                        <option value="info">Info only</option>
                        <option value="warning">Warning only</option>
                        <option value="error">Error only</option>
                        <option value="debug">Debug only</option>
                    </select>
                </div>
                <div class="option-group">
                    <label>
                        <input type="radio" name="clear_type" value="all">
                        Clear ALL logs (irreversible)
                    </label>
                </div>
            </div>
            <div class="alert alert-warning" style="margin-top: 1rem;">
                <i class="ri-alert-line"></i>
                <strong>Warning:</strong> This action cannot be undone. Please make sure you have exported any important logs before proceeding.
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeClearLogsModal()">Cancel</button>
            <button type="button" class="btn btn-danger" onclick="confirmClearLogs()">Clear Logs</button>
        </div>
    </div>
</div>

<script>
// Show loading state
function showLoading(message = 'Processing...') {
    // Create or update loading overlay
    let overlay = document.getElementById('loadingOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); display: flex; align-items: center;
            justify-content: center; z-index: 9999; color: white;
        `;
        document.body.appendChild(overlay);
    }
    overlay.innerHTML = `<div style="text-align: center;"><i class="ri-loader-4-line" style="font-size: 2rem; animation: spin 1s linear infinite;"></i><br>${message}</div>`;
    overlay.style.display = 'flex';
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) overlay.style.display = 'none';
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 10000;
        min-width: 300px; animation: slideIn 0.3s ease-out;
    `;
    notification.innerHTML = `
        <i class="ri-${type === 'success' ? 'check' : type === 'error' ? 'error-warning' : 'information'}-line"></i>
        ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem;">&times;</button>
    `;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => notification.remove(), 5000);
}

// Export logs
function exportLogs() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');

    // In real implementation, this would trigger a download
    showNotification('Export functionality would be implemented here. This would download logs as CSV/JSON based on current filters.', 'info');
}

// Show clear logs modal
function showClearLogsModal() {
    document.getElementById('clearLogsModal').style.display = 'flex';
}

// Close clear logs modal
function closeClearLogsModal() {
    document.getElementById('clearLogsModal').style.display = 'none';
}

// Confirm clear logs
function confirmClearLogs() {
    const clearType = document.querySelector('input[name="clear_type"]:checked').value;
    let action, data;

    if (clearType === 'all') {
        action = 'clear_all_logs';
        data = { action };
    } else if (clearType === 'older') {
        const days = document.getElementById('clear_days').value;
        action = 'clear_logs';
        data = { action, days: parseInt(days) };
    } else if (clearType === 'level') {
        const level = document.getElementById('clear_level').value;
        const days = 365; // Clear by level for last year
        action = 'clear_logs';
        data = { action, days, level };
    }

    closeClearLogsModal();
    showLoading('Clearing logs...');

    fetch('api/logs_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        hideLoading();
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(result.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showNotification('Error clearing logs: ' + error.message, 'error');
    });
}

// Refresh logs
function refreshLogs() {
    location.reload();
}

// View log details
function viewLogDetails(logId) {
    // Get log details from the current page data
    const logRows = document.querySelectorAll('.log-row');
    let logData = null;

    logRows.forEach(row => {
        const deleteBtn = row.querySelector('button[onclick*="deleteLog(' + logId + ')"]');
        if (deleteBtn) {
            const cells = row.querySelectorAll('td');
            logData = {
                id: logId,
                timestamp: cells[0].textContent.trim(),
                level: cells[1].textContent.trim(),
                action: cells[2].textContent.trim(),
                message: cells[3].textContent.trim(),
                ip: cells[4].textContent.trim(),
                admin: cells[5].textContent.trim()
            };
        }
    });

    if (logData) {
        const logDetails = `
            <div class="log-detail-item">
                <strong>Log ID:</strong> ${logData.id}
            </div>
            <div class="log-detail-item">
                <strong>Timestamp:</strong> ${logData.timestamp}
            </div>
            <div class="log-detail-item">
                <strong>Level:</strong> ${logData.level}
            </div>
            <div class="log-detail-item">
                <strong>Action:</strong> ${logData.action}
            </div>
            <div class="log-detail-item">
                <strong>Message:</strong> ${logData.message}
            </div>
            <div class="log-detail-item">
                <strong>IP Address:</strong> ${logData.ip}
            </div>
            <div class="log-detail-item">
                <strong>Admin User:</strong> ${logData.admin}
            </div>
        `;

        document.getElementById('logDetails').innerHTML = logDetails;
        document.getElementById('logModal').style.display = 'flex';
    }
}

// Close log modal
function closeLogModal() {
    document.getElementById('logModal').style.display = 'none';
}

// Delete log
function deleteLog(logId) {
    if (confirm('Are you sure you want to delete this log entry?')) {
        showLoading('Deleting log...');

        fetch('api/logs_actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_log',
                log_id: logId
            })
        })
        .then(response => response.json())
        .then(result => {
            hideLoading();
            if (result.success) {
                showNotification(result.message, 'success');
                // Remove the row from the table
                const deleteBtn = document.querySelector(`button[onclick*="deleteLog(${logId})"]`);
                if (deleteBtn) {
                    deleteBtn.closest('tr').remove();
                }
                // Update the count
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(result.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('Error deleting log: ' + error.message, 'error');
        });
    }
}

// Auto-refresh logs every 30 seconds
setInterval(function() {
    if (document.querySelector('input[name="auto_refresh"]:checked')) {
        location.reload();
    }
}, 30000);

// Close modal when clicking outside
document.getElementById('logModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLogModal();
    }
});

document.getElementById('clearLogsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeClearLogsModal();
    }
});

// Add CSS animation for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

<style>
.log-filters .filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.logs-table .log-row.log-error {
    background: var(--error-50);
}

.logs-table .log-row.log-warning {
    background: var(--warning-50);
}

.logs-table .log-row.log-debug {
    background: var(--gray-50);
}

.log-level.info {
    background: var(--primary-50);
    color: var(--primary-700);
}

.log-level.warning {
    background: var(--warning-50);
    color: var(--warning-700);
}

.log-level.error {
    background: var(--error-50);
    color: var(--error-700);
}

.log-level.debug {
    background: var(--gray-100);
    color: var(--gray-700);
}

.log-timestamp {
    font-family: monospace;
    font-size: var(--font-size-xs);
    white-space: nowrap;
}

.log-message {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.log-detail-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    gap: 1rem;
}

.log-detail-item:last-child {
    border-bottom: none;
}

.log-detail-item strong {
    min-width: 120px;
    color: var(--gray-600);
}

.pagination-dots {
    padding: 0.5rem 1rem;
    color: var(--gray-500);
}

.clear-options {
    margin: 1rem 0;
}

.option-group {
    margin: 1rem 0;
    padding: 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-group:hover {
    background: var(--gray-50);
}

.option-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    cursor: pointer;
    flex: 1;
}

.option-group input[type="radio"] {
    margin: 0;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-warning {
    background: var(--warning-50);
    border-color: var(--warning-200);
    color: var(--warning-800);
}

@media (max-width: 768px) {
    .log-filters .filter-row {
        grid-template-columns: 1fr;
    }

    .logs-table {
        font-size: var(--font-size-xs);
    }

    .log-message {
        max-width: 150px;
    }

    .option-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .option-group label {
        width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
