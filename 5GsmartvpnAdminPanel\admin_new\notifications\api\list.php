<?php
/**
 * 5G Smart VPN Admin Panel - List Notifications API
 * RESTful API endpoint for retrieving notifications
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../includes/notification_functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Get query parameters
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
    $status = $_GET['status'] ?? null;
    $category = $_GET['category'] ?? null;
    $priority = $_GET['priority'] ?? null;
    $search = $_GET['search'] ?? null;
    $date_from = $_GET['date_from'] ?? null;
    $date_to = $_GET['date_to'] ?? null;
    $sort_by = $_GET['sort_by'] ?? 'created_at';
    $sort_order = $_GET['sort_order'] ?? 'DESC';
    
    // Validate sort parameters
    $valid_sort_fields = ['id', 'title', 'status', 'priority', 'category', 'created_at', 'scheduled_time'];
    if (!in_array($sort_by, $valid_sort_fields)) {
        $sort_by = 'created_at';
    }
    
    $valid_sort_orders = ['ASC', 'DESC'];
    if (!in_array(strtoupper($sort_order), $valid_sort_orders)) {
        $sort_order = 'DESC';
    }
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    $param_types = '';
    
    if ($status) {
        $where_conditions[] = "status = ?";
        $params[] = $status;
        $param_types .= 's';
    }
    
    if ($category) {
        $where_conditions[] = "category = ?";
        $params[] = $category;
        $param_types .= 's';
    }
    
    if ($priority) {
        $where_conditions[] = "priority = ?";
        $params[] = $priority;
        $param_types .= 's';
    }
    
    if ($search) {
        $where_conditions[] = "(title LIKE ? OR message LIKE ?)";
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $param_types .= 'ss';
    }
    
    if ($date_from) {
        $where_conditions[] = "created_at >= ?";
        $params[] = $date_from . ' 00:00:00';
        $param_types .= 's';
    }
    
    if ($date_to) {
        $where_conditions[] = "created_at <= ?";
        $params[] = $date_to . ' 23:59:59';
        $param_types .= 's';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM notifications $where_clause";
    if (!empty($params)) {
        $count_stmt = $conn->prepare($count_sql);
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
    } else {
        $count_result = $conn->query($count_sql);
    }
    
    $total_count = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_count / $limit);
    
    // Get notifications
    $offset = ($page - 1) * $limit;
    $sql = "SELECT 
                id, title, message, status, sent_to, notification_type, 
                schedule_type, scheduled_time, recurring_interval, 
                priority, category, target_audience, delivery_count, 
                success_count, failure_count, created_at, updated_at
            FROM notifications 
            $where_clause 
            ORDER BY $sort_by $sort_order 
            LIMIT ? OFFSET ?";
    
    // Add limit and offset to parameters
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= 'ii';
    
    if (!empty($where_conditions) || true) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $conn->query($sql);
    }
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        // Format dates
        $row['created_at'] = date('Y-m-d H:i:s', strtotime($row['created_at']));
        $row['updated_at'] = date('Y-m-d H:i:s', strtotime($row['updated_at']));
        
        if ($row['scheduled_time']) {
            $row['scheduled_time'] = date('Y-m-d H:i:s', strtotime($row['scheduled_time']));
        }
        
        // Add formatted message preview
        $row['message_preview'] = strlen($row['message']) > 100 ? 
            substr($row['message'], 0, 100) . '...' : $row['message'];
        
        $notifications[] = $row;
    }
    
    // Get statistics
    $stats = getNotificationStats();
    
    // Response
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => [
            'notifications' => $notifications,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_count' => $total_count,
                'limit' => $limit,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ],
            'filters' => [
                'status' => $status,
                'category' => $category,
                'priority' => $priority,
                'search' => $search,
                'date_from' => $date_from,
                'date_to' => $date_to,
                'sort_by' => $sort_by,
                'sort_order' => $sort_order
            ],
            'statistics' => $stats
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
