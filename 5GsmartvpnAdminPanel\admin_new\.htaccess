# # 5G Smart VPN Admin Panel - Security Configuration
# Options -Indexes +FollowSymLinks

# # Protect against unauthorized access
# <IfModule mod_authz_core.c>
#     Require all granted
# </IfModule>

# # Enable rewrite engine
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteBase /
# </IfModule>

# # Protect sensitive files
# <FilesMatch "^\.">
#     Require all denied
# </FilesMatch>

# # Protect against script injections
# <IfModule mod_headers.c>
#     Header set X-Content-Type-Options "nosniff"
#     Header set X-XSS-Protection "1; mode=block"
#     Header set X-Frame-Options "SAMEORIGIN"
#     Header set Referrer-Policy "strict-origin-when-cross-origin"
# </IfModule>

# # PHP handler configuration
# <IfModule mod_php.c>
#     php_value display_errors Off
#     php_value log_errors On
#     php_value error_log logs/error.log
#     php_value max_execution_time 300
#     php_value memory_limit 256M
#     php_value upload_max_filesize 10M
#     php_value post_max_size 10M
# </IfModule>

# # Allow access to specific file types
# <FilesMatch "\.(jpg|jpeg|png|gif|css|js|ico|pdf|txt|woff|woff2|ttf|eot|svg)$">
#     Require all granted
#     <IfModule mod_expires.c>
#         ExpiresActive On
#         ExpiresByType image/jpg "access plus 1 month"
#         ExpiresByType image/jpeg "access plus 1 month"
#         ExpiresByType image/gif "access plus 1 month"
#         ExpiresByType image/png "access plus 1 month"
#         ExpiresByType text/css "access plus 1 month"
#         ExpiresByType application/pdf "access plus 1 month"
#         ExpiresByType text/javascript "access plus 1 month"
#         ExpiresByType application/javascript "access plus 1 month"
#     </IfModule>
# </FilesMatch>

# # Disable directory browsing
# IndexIgnore *

# # Set default character set
# AddDefaultCharset UTF-8

# # Compress files for better performance
# <IfModule mod_deflate.c>
#     AddOutputFilterByType DEFLATE text/plain
#     AddOutputFilterByType DEFLATE text/html
#     AddOutputFilterByType DEFLATE text/xml
#     AddOutputFilterByType DEFLATE text/css
#     AddOutputFilterByType DEFLATE application/xml
#     AddOutputFilterByType DEFLATE application/xhtml+xml
#     AddOutputFilterByType DEFLATE application/rss+xml
#     AddOutputFilterByType DEFLATE application/javascript
#     AddOutputFilterByType DEFLATE application/x-javascript
# </IfModule>

# # Security headers
# <IfModule mod_headers.c>
#     Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
#     Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self'"
# </IfModule>
