<?php
/**
 * Detailed Firebase Notification Testing
 * Comprehensive testing to identify why notifications aren't reaching Android app
 */

session_start();
require_once 'includes/config.php';

echo "<h2>Detailed Firebase Notification Testing</h2>";

try {
    // 1. Check Firebase Configuration
    echo "<h3>1. Firebase Configuration Check</h3>";
    
    $service_account_file = __DIR__ . '/notifications/service-account-file.json';
    $firebase_config_file = __DIR__ . '/notifications/firebase-config.json';
    
    if (file_exists($service_account_file)) {
        echo "<p style='color: green;'>✓ Service account file exists</p>";
        
        $service_content = file_get_contents($service_account_file);
        $service_data = json_decode($service_content, true);
        
        if ($service_data && isset($service_data['project_id'])) {
            echo "<p style='color: green;'>✓ Project ID: " . htmlspecialchars($service_data['project_id']) . "</p>";
            echo "<p style='color: green;'>✓ Client Email: " . htmlspecialchars($service_data['client_email']) . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Invalid service account file</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Service account file missing</p>";
        echo "<p><strong>Expected location:</strong> " . htmlspecialchars($service_account_file) . "</p>";
    }

    // 2. Test Access Token Generation
    echo "<h3>2. Access Token Generation Test</h3>";
    
    if (file_exists($service_account_file)) {
        require_once __DIR__ . '/notifications/includes/notification_functions.php';
        
        if (function_exists('getAccessToken')) {
            try {
                $access_token = getAccessToken($service_account_file);
                if ($access_token) {
                    echo "<p style='color: green;'>✓ Access token generated successfully</p>";
                    echo "<p><strong>Token (first 50 chars):</strong> " . htmlspecialchars(substr($access_token, 0, 50)) . "...</p>";
                    echo "<p><strong>Token length:</strong> " . strlen($access_token) . " characters</p>";
                } else {
                    echo "<p style='color: red;'>✗ Failed to generate access token</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Access token error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ getAccessToken function not available</p>";
        }
    }

    // 3. Test FCM API Connectivity
    echo "<h3>3. FCM API Connectivity Test</h3>";
    
    if (isset($service_data['project_id']) && isset($access_token)) {
        $fcm_url = "https://fcm.googleapis.com/v1/projects/{$service_data['project_id']}/messages:send";
        
        // Test with a minimal payload to check connectivity
        $test_payload = [
            'message' => [
                'topic' => 'test_connectivity',
                'notification' => [
                    'title' => 'Connectivity Test',
                    'body' => 'Testing FCM API connectivity'
                ]
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fcm_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_payload));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        echo "<p><strong>FCM URL:</strong> " . htmlspecialchars($fcm_url) . "</p>";
        echo "<p><strong>HTTP Code:</strong> $http_code</p>";
        
        if ($curl_error) {
            echo "<p style='color: red;'><strong>cURL Error:</strong> " . htmlspecialchars($curl_error) . "</p>";
        }
        
        if ($response) {
            $response_data = json_decode($response, true);
            echo "<p><strong>Response:</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
            echo json_encode($response_data, JSON_PRETTY_PRINT);
            echo "</pre>";
            
            if ($http_code === 200) {
                echo "<p style='color: green;'>✓ FCM API is accessible and responding</p>";
            } else {
                echo "<p style='color: red;'>✗ FCM API returned error code: $http_code</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ No response from FCM API</p>";
        }
    }

    // 4. Test Notification Function
    echo "<h3>4. Notification Function Test</h3>";
    
    if (function_exists('sendFCMNotification')) {
        echo "<p style='color: green;'>✓ sendFCMNotification function available</p>";
        
        // Test with actual notification
        $test_title = "Test Notification " . date('H:i:s');
        $test_message = "This is a detailed test notification sent at " . date('Y-m-d H:i:s');
        
        echo "<p><strong>Sending test notification...</strong></p>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($test_title) . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($test_message) . "</p>";
        echo "<p><strong>Topic:</strong> all</p>";
        
        $result = sendFCMNotification(
            $test_title,
            $test_message,
            'all',
            'immediate',
            null,
            null,
            [
                'test' => true,
                'detailed_test' => true,
                'timestamp' => time(),
                'source' => 'detailed_firebase_test'
            ]
        );
        
        echo "<p><strong>Function Result:</strong></p>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo json_encode($result, JSON_PRETTY_PRINT);
        echo "</pre>";
        
        if ($result['success']) {
            echo "<p style='color: green;'>✓ Notification function executed successfully</p>";
            
            // Check if notification was saved to database
            if (isset($result['notification_id'])) {
                echo "<p style='color: green;'>✓ Notification saved to database (ID: " . $result['notification_id'] . ")</p>";
                
                // Get the saved notification details
                $saved_notification = $conn->query("SELECT * FROM notifications WHERE id = " . (int)$result['notification_id']);
                if ($saved_notification && $saved_notification->num_rows > 0) {
                    $notification_data = $saved_notification->fetch_assoc();
                    echo "<p><strong>Database Record:</strong></p>";
                    echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
                    echo json_encode($notification_data, JSON_PRETTY_PRINT);
                    echo "</pre>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Notification function failed</p>";
            if (isset($result['error'])) {
                echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($result['error']) . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>✗ sendFCMNotification function not available</p>";
    }

    // 5. Check Recent Notifications in Database
    echo "<h3>5. Recent Notifications Analysis</h3>";
    
    $recent_notifications = $conn->query("
        SELECT * FROM notifications 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    if ($recent_notifications && $recent_notifications->num_rows > 0) {
        echo "<p style='color: green;'>✓ Found " . $recent_notifications->num_rows . " notifications in the last hour</p>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Status</th><th>Response</th><th>Created</th></tr>";
        
        while ($row = $recent_notifications->fetch_assoc()) {
            $status_color = $row['status'] === 'sent' ? 'green' : ($row['status'] === 'error' ? 'red' : 'orange');
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td style='color: $status_color;'>" . htmlspecialchars($row['status']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['response'] ?? '', 0, 100)) . "...</td>";
            echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ No notifications found in the last hour</p>";
    }

    // 6. Android App Integration Check
    echo "<h3>6. Android App Integration Analysis</h3>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h5>Potential Issues with Android App:</h5>";
    echo "<ul>";
    echo "<li><strong>Topic Subscription:</strong> Ensure the Android app subscribes to the 'all' topic</li>";
    echo "<li><strong>Firebase Project:</strong> Verify the Android app uses the same Firebase project</li>";
    echo "<li><strong>google-services.json:</strong> Check if the Android app has the correct configuration file</li>";
    echo "<li><strong>FCM Token:</strong> Verify the app generates and registers FCM tokens</li>";
    echo "<li><strong>Background Processing:</strong> Check if the app handles background notifications</li>";
    echo "<li><strong>Notification Permissions:</strong> Ensure the app has notification permissions</li>";
    echo "</ul>";
    echo "</div>";

    // 7. Quick Actions
    echo "<h3>7. Quick Test Actions</h3>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='send_high_priority' style='background: #dc3545; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Send High Priority Notification</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='send_data_only' style='background: #6f42c1; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Send Data-Only Notification</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='test_multiple_topics' style='background: #20c997; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Test Multiple Topics</button>";
    echo "</form>";

    // Handle test actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<h4>Test Action Results:</h4>";
        
        if (isset($_POST['send_high_priority']) && function_exists('sendFCMNotification')) {
            $result = sendFCMNotification(
                "HIGH PRIORITY TEST",
                "This is a high priority test notification",
                'all',
                'immediate',
                null,
                null,
                [
                    'priority' => 'high',
                    'test_type' => 'high_priority',
                    'timestamp' => time()
                ]
            );
            
            echo "<p><strong>High Priority Test Result:</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
            echo json_encode($result, JSON_PRETTY_PRINT);
            echo "</pre>";
        }
        
        if (isset($_POST['send_data_only']) && function_exists('sendFCMNotification')) {
            // Send data-only notification (no notification payload)
            $result = sendFCMNotification(
                "",
                "",
                'all',
                'immediate',
                null,
                null,
                [
                    'data_only' => 'true',
                    'action' => 'test_data_notification',
                    'timestamp' => time(),
                    'message' => 'This is a data-only notification'
                ]
            );
            
            echo "<p><strong>Data-Only Test Result:</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
            echo json_encode($result, JSON_PRETTY_PRINT);
            echo "</pre>";
        }
        
        if (isset($_POST['test_multiple_topics']) && function_exists('sendFCMNotification')) {
            $topics = ['all', 'android', 'test', 'vpn_users'];
            
            foreach ($topics as $topic) {
                $result = sendFCMNotification(
                    "Topic Test: $topic",
                    "Testing notification delivery to topic: $topic",
                    $topic,
                    'immediate',
                    null,
                    null,
                    [
                        'topic_test' => true,
                        'target_topic' => $topic,
                        'timestamp' => time()
                    ]
                );
                
                echo "<p><strong>Topic '$topic' Result:</strong> " . ($result['success'] ? 'Success' : 'Failed') . "</p>";
            }
        }
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Critical Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Useful Links</h3>";
echo "<ul>";
echo "<li><a href='notifications.php'>Notifications Management</a></li>";
echo "<li><a href='debug_firebase_notifications.php'>Basic Firebase Debug</a></li>";
echo "<li><a href='fix_critical_issues.php'>Fix Critical Issues</a></li>";
echo "</ul>";

$conn->close();
?>
