<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5G Smart VPN - API Test"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#FF5722"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Warning -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚠️ This is a development testing tool. Remove from production builds."
            android:textSize="14sp"
            android:textColor="#FF9800"
            android:background="#FFF3E0"
            android:padding="12dp"
            android:layout_marginBottom="24dp" />

        <!-- Test Buttons -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="API Tests"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/testStatusBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test API Status"
            android:backgroundTint="#4CAF50"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/testConfigBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test App Configuration"
            android:backgroundTint="#2196F3"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/testServersBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Servers API"
            android:backgroundTint="#9C27B0"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/testCustomAdsBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Custom Ads"
            android:backgroundTint="#FF9800"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/testIpBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test IP Detection"
            android:backgroundTint="#607D8B"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/testTrackingBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Ad Tracking"
            android:backgroundTint="#795548"
            android:textColor="@android:color/white"
            android:layout_marginBottom="24dp" />

        <!-- Status Display -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Results"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Click a test button to begin..."
            android:textSize="14sp"
            android:textColor="#666"
            android:background="@android:color/white"
            android:padding="16dp"
            android:fontFamily="monospace"
            android:minHeight="200dp"
            android:gravity="top"
            android:scrollbars="vertical" />

        <!-- API Configuration Info -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Configuration"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Base URL: http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/\n\nAPI Key: Check Const.API_SECRET_KEY\n\nAuthentication: HMAC-SHA256\n\nVersion: 3.0"
            android:textSize="12sp"
            android:textColor="#666"
            android:background="@android:color/white"
            android:padding="12dp"
            android:fontFamily="monospace" />

    </LinearLayout>

</ScrollView>
