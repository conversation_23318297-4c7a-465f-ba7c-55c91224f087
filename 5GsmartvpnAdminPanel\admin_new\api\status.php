<?php
/**
 * 5G Smart VPN Admin Panel - API Status/Health Check
 * Provides API health status and system information
 */

require_once '../includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    $status = [
        'api_status' => 'healthy',
        'timestamp' => time(),
        'api_version' => '3.0',
        'source' => 'modern_admin_panel',
        'endpoint' => 'status'
    ];

    // Check database connection
    try {
        $status['database'] = 'connected';

        // Get database stats
        $tables_check = [
            'settings' => 'SELECT COUNT(*) as count FROM settings',
            'servers' => 'SELECT COUNT(*) as count FROM servers WHERE status = 1',
            'custom_ads' => 'SELECT COUNT(*) as count FROM custom_ads WHERE `on` = 1'
        ];

        $status['database_stats'] = [];

        foreach ($tables_check as $table => $query) {
            try {
                $result = mysqli_query($conn, $query);
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $status['database_stats'][$table] = (int)$row['count'];
                } else {
                    $status['database_stats'][$table] = 'query_failed';
                }
            } catch (Exception $e) {
                $status['database_stats'][$table] = 'table_missing';
            }
        }

    } catch (Exception $e) {
        $status['database'] = 'disconnected';
        $status['api_status'] = 'degraded';
    }

    // Check critical settings
    $critical_settings = [
        'admob_app_id',
        'admob_banner_id',
        'admob_interstitial_id'
    ];

    try {
        $placeholders = str_repeat('?,', count($critical_settings) - 1) . '?';
        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
        $stmt->bind_param(str_repeat('s', count($critical_settings)), ...$critical_settings);
        $stmt->execute();
        $result = $stmt->get_result();

        $status['critical_settings'] = [];
        while ($row = $result->fetch_assoc()) {
            $status['critical_settings'][$row['setting_key']] = !empty($row['setting_value']) ? 'configured' : 'empty';
        }
    } catch (Exception $e) {
        $status['critical_settings'] = 'error';
    }

    // System information
    $status['system'] = [
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        'memory_limit' => ini_get('memory_limit')
    ];

    // API endpoints status
    $status['endpoints'] = [
        'config' => 'available',
        'servers' => 'available',
        'custom_ads' => 'available',
        'settings' => 'available',
        'track_ad' => 'available',
        'ip' => 'available',
        'legacy' => 'available'
    ];

    // Security check
    $status['security'] = [
        'https_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        'api_key_configured' => !empty(API_KEY) && API_KEY !== 'your-secret-api-key-here',
        'hmac_validation' => 'enabled'
    ];

    // Overall health determination
    if ($status['database'] === 'disconnected') {
        $status['api_status'] = 'unhealthy';
        http_response_code(503);
    } elseif (empty($status['critical_settings']) || in_array('empty', $status['critical_settings'])) {
        $status['api_status'] = 'degraded';
        $status['warnings'] = ['Some critical settings are not configured'];
    }

    echo json_encode($status, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'api_status' => 'error',
        'error' => 'Health check failed',
        'message' => $e->getMessage(),
        'timestamp' => time(),
        'api_version' => '3.0'
    ]);
}
?>
