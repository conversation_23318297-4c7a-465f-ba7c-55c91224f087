<?php
/**
 * 5G Smart VPN Admin Panel - Modern Login Page
 */

session_start();
require_once 'includes/config.php';

// Redirect if already logged in
if (isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);

    if (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
    } else {
        // Check database connection
        if (!$conn) {
            $error_message = 'Database connection failed: ' . mysqli_connect_error();
        } else {
            // Sanitize input
            $username = mysqli_real_escape_string($conn, $username);
            $password = mysqli_real_escape_string($conn, $password);

            // First, check if admin_users table exists
            $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'admin_users'");
            if (mysqli_num_rows($table_check) == 0) {
                // Create admin_users table if it doesn't exist
                $create_table = "
                    CREATE TABLE admin_users (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(255) NOT NULL UNIQUE,
                        password VARCHAR(255) NOT NULL,
                        email VARCHAR(255),
                        role ENUM('admin','moderator','viewer') DEFAULT 'admin',
                        status TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ";
                mysqli_query($conn, $create_table);

                // Insert default admin user
                $insert_admin = "INSERT INTO admin_users (username, password, email, role) VALUES ('admin', '1234', '<EMAIL>', 'admin')";
                mysqli_query($conn, $insert_admin);
            }

            // Query database
            $query = "SELECT id, username FROM admin_users WHERE username = '$username' AND password = '$password'";
            $result = mysqli_query($conn, $query);

            if ($result && mysqli_num_rows($result) > 0) {
                $admin = mysqli_fetch_assoc($result);

                // Set session variables
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['login_time'] = time();

                // Force redirect with JavaScript as backup
                echo "<script>window.location.href = 'index.php';</script>";
                header('Location: index.php');
                exit();
            } else {
                $error_message = 'Invalid username or password.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo ADMIN_PANEL_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Login CSS -->
    <style>
        :root {
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --error-500: #ef4444;
            --error-600: #dc2626;
            --success-500: #22c55e;
            --success-600: #16a34a;
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-family);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .login-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            width: 100%;
            max-width: 400px;
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1rem;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .login-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--gray-900);
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            transition: all 0.15s ease-in-out;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .form-control::placeholder {
            color: var(--gray-400);
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.125rem;
        }

        .input-group .form-control {
            padding-left: 2.5rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.5;
            border: 1px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.15s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
            border-color: var(--primary-600);
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-700);
            border-color: var(--primary-700);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .login-footer {
            padding: 1.5rem 2rem;
            background: var(--gray-50);
            text-align: center;
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .loading-spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container fade-in">
        <!-- Header -->
        <div class="login-header">
            <div class="logo-icon">
                <i class="ri-5g-line"></i>
            </div>
            <h1 class="login-title">5G Smart VPN</h1>
            <p class="login-subtitle">Admin Panel Login</p>
        </div>

        <!-- Body -->
        <div class="login-body">
            <!-- Error/Success Messages -->
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i class="ri-error-warning-line"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="ri-check-line"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <div id="loginFormContainer">
                <input type="hidden" id="hiddenUsername" name="username" value="">
                <input type="hidden" id="hiddenPassword" name="password" value="">

                <div class="form-group">
                    <label for="userInput" class="form-label">Username</label>
                    <div class="input-group">
                        <i class="input-icon ri-user-line"></i>
                        <input type="text"
                               id="userInput"
                               class="form-control"
                               placeholder="Enter your username"
                               value="admin"
                               autocomplete="off"
                               autocorrect="off"
                               autocapitalize="off"
                               spellcheck="false"
                               data-lpignore="true"
                               data-form-type="other">
                    </div>
                </div>

                <div class="form-group">
                    <label for="passInput" class="form-label">Password</label>
                    <div class="input-group">
                        <i class="input-icon ri-lock-line"></i>
                        <input type="text"
                               id="passInput"
                               class="form-control"
                               placeholder="Enter your password"
                               value="1234"
                               autocomplete="off"
                               autocorrect="off"
                               autocapitalize="off"
                               spellcheck="false"
                               data-lpignore="true"
                               data-form-type="other"
                               style="font-family: monospace; -webkit-text-security: disc; text-security: disc;">
                    </div>
                </div>

                <button type="button" id="loginBtn" class="btn btn-primary">
                    <span class="btn-text">Sign In</span>
                </button>

                <form method="POST" action="login.php" id="hiddenForm" style="display: none;">
                    <input type="hidden" name="username" id="finalUsername">
                    <input type="hidden" name="password" id="finalPassword">
                    <input type="hidden" name="login" value="1">
                </form>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <p>&copy; <?php echo date('Y'); ?> 5G Smart VPN. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Login form handling
        document.addEventListener('DOMContentLoaded', function() {
            const btn = document.getElementById('loginBtn');
            const btnText = btn.querySelector('.btn-text');
            const userInput = document.getElementById('userInput');
            const passInput = document.getElementById('passInput');
            const hiddenForm = document.getElementById('hiddenForm');
            const finalUsername = document.getElementById('finalUsername');
            const finalPassword = document.getElementById('finalPassword');

            // Button click handler
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                const username = userInput.value.trim();
                const password = passInput.value.trim();

                // Validate fields
                if (!username || !password) {
                    alert('Please enter both username and password');
                    return false;
                }

                // Show loading state
                btnText.innerHTML = '<span class="loading-spinner"></span> Signing in...';
                btn.disabled = true;

                // Set hidden form values and submit
                finalUsername.value = username;
                finalPassword.value = password;

                hiddenForm.submit();
            });

            // Enter key handling
            function handleEnterKey(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    btn.click();
                }
            }

            userInput.addEventListener('keypress', handleEnterKey);
            passInput.addEventListener('keypress', handleEnterKey);

            // Auto-focus username field
            userInput.focus();
        });
    </script>
</body>
</html>
