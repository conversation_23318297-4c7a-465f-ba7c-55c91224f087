package com.official.fivegfastvpn.model;

/**
 * Base API Response Model for 5G Smart VPN
 * Common structure for all API responses
 */
public class ApiResponse<T> {
    private boolean success;
    private String error;
    private String message;
    private int code;
    private long timestamp;
    private String apiVersion;
    private String source;
    private String endpoint;
    private T data;
    
    // Constructors
    public ApiResponse() {}
    
    public ApiResponse(boolean success, T data) {
        this.success = success;
        this.data = data;
    }
    
    public ApiResponse(boolean success, String error, String message) {
        this.success = success;
        this.error = error;
        this.message = message;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    /**
     * Check if this is an error response
     * @return true if response indicates an error
     */
    public boolean isError() {
        return !success || error != null;
    }
    
    /**
     * Get error message for display
     * @return Error message or generic message if none available
     */
    public String getDisplayMessage() {
        if (message != null && !message.isEmpty()) {
            return message;
        }
        if (error != null && !error.isEmpty()) {
            return error;
        }
        return success ? "Success" : "Unknown error occurred";
    }
    
    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", error='" + error + '\'' +
                ", message='" + message + '\'' +
                ", code=" + code +
                ", timestamp=" + timestamp +
                ", apiVersion='" + apiVersion + '\'' +
                ", source='" + source + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", data=" + data +
                '}';
    }
}
