<?php
/**
 * 5G Smart VPN Admin Panel - Unified Configuration
 * Database connection and system configuration
 * Version: 3.0 - Unified System
 */

// Prevent multiple inclusions
if (defined('ADMIN_CONFIG_LOADED')) {
    return;
}
define('ADMIN_CONFIG_LOADED', true);

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', '5gsmartvpnnewupdate');
define('DB_CHARSET', 'utf8mb4');

// Admin Panel Configuration
define('ADMIN_PANEL_VERSION', '3.0.0');
define('ADMIN_PANEL_NAME', '5G Smart VPN Admin Panel');
define('ADMIN_PANEL_URL', 'http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/');

// API Configuration
define('API_KEY', '5g-smart-vpn-api-key-2024-secure');
define('API_VERSION', '3.0');

// File Upload Configuration
define('UPLOAD_PATH', dirname(__DIR__, 2) . '/uploads/');
define('UPLOAD_URL', '/Svpn5g/5GsmartvpnAdminPanel/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination Configuration
define('ITEMS_PER_PAGE', 20);

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Notification Configuration
define('FCM_SERVER_KEY', 'your-fcm-server-key-here');
define('FCM_SENDER_ID', 'your-fcm-sender-id-here');

// Set timezone
date_default_timezone_set('Asia/Dhaka');

// Create uploads directory if it doesn't exist
if (!file_exists(UPLOAD_PATH)) {
    @mkdir(UPLOAD_PATH, 0755, true);
}

// Primary MySQLi Database Connection
try {
    $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Set charset
    $conn->set_charset(DB_CHARSET);

    // Set SQL mode for better compatibility
    $conn->query("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}

// Global utility functions (with conflict protection)
if (!function_exists('logError')) {
    function logError($message, $file = __FILE__, $line = __LINE__) {
        error_log("[" . date('Y-m-d H:i:s') . "] Error in $file:$line - $message");
    }
}

if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

// Start output buffering
ob_start();
?>
