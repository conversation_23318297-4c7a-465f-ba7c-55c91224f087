<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets"><file name="nopie_openvpn.arm64-v8a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\nopie_openvpn.arm64-v8a"/><file name="nopie_openvpn.armeabi-v7a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\nopie_openvpn.armeabi-v7a"/><file name="nopie_openvpn.x86" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\nopie_openvpn.x86"/><file name="nopie_openvpn.x86_64" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\nopie_openvpn.x86_64"/><file name="pie_openvpn.arm64-v8a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\pie_openvpn.arm64-v8a"/><file name="pie_openvpn.armeabi-v7a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\pie_openvpn.armeabi-v7a"/><file name="pie_openvpn.x86" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\pie_openvpn.x86"/><file name="pie_openvpn.x86_64" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\assets\pie_openvpn.x86_64"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\release\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>