<?php
/**
 * Custom Ads Provider - Create Ad Page
 * Create new advertisements
 */

session_start();
require_once '../admin_new/includes/config.php';

// Check if provider is logged in
if (!isset($_SESSION['provider_id'])) {
    header("Location: login.php");
    exit;
}

// Get provider information
$stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE provider_id = ?");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$provider = $stmt->get_result()->fetch_assoc();

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_ad'])) {
    $title = trim($_POST['title']);
    $text = trim($_POST['text']);
    $url = trim($_POST['url']);
    $url_type = $_POST['url_type'] ?? 'website';
    $button_text = trim($_POST['button_text']);
    $date_start = $_POST['date_start'];
    $date_end = $_POST['date_end'];
    
    if (empty($title) || empty($text) || empty($date_start) || empty($date_end)) {
        $error = 'Please fill in all required fields.';
    } elseif (strtotime($date_end) < strtotime($date_start)) {
        $error = 'End date must be after start date.';
    } else {
        // Handle image upload
        $image_path = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../admin_new/images/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $filename = 'ad_' . time() . '_' . uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $image_path = 'images/' . $filename;
                } else {
                    $error = 'Failed to upload image.';
                }
            } else {
                $error = 'Invalid image format. Please use JPG, PNG, GIF, or WebP.';
            }
        }
        
        if (empty($error)) {
            // Auto-detect URL type and button text if not provided
            if (!empty($url) && empty($button_text)) {
                $url_lower = strtolower($url);
                if (strpos($url_lower, 'play.google.com') !== false) {
                    $url_type = 'playstore';
                    $button_text = 'Install';
                } elseif (strpos($url_lower, 'apps.apple.com') !== false || strpos($url_lower, 'itunes.apple.com') !== false) {
                    $url_type = 'appstore';
                    $button_text = 'Install';
                } else {
                    $url_type = 'website';
                    $button_text = 'Visit';
                }
            }
            
            // Insert ad into database
            $stmt = $conn->prepare("INSERT INTO custom_ads (provider_id, title, text, image, url, url_type, button_text, date_start, date_end, `on`, view_count, click_count, is_approved, priority) VALUES ((SELECT id FROM custom_ads_providers WHERE provider_id = ?), ?, ?, ?, ?, ?, ?, ?, ?, 0, 0, 0, 0, 1)");
            $stmt->bind_param("sssssssss", $_SESSION['provider_id'], $title, $text, $image_path, $url, $url_type, $button_text, $date_start, $date_end);
            
            if ($stmt->execute()) {
                $success = 'Ad created successfully! It will be reviewed before going live.';
                // Clear form data
                $title = $text = $url = $button_text = $date_start = $date_end = '';
                $url_type = 'website';
            } else {
                $error = 'Failed to create ad: ' . $stmt->error;
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Ad - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .form-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .preview-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
        }
        .preview-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h5 class="text-white">5G Smart VPN</h5>
                    <small class="text-white-50">Custom Ads Provider</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link" href="ads.php">
                        <i class="fas fa-ad me-2"></i> My Ads
                    </a>
                    <a class="nav-link active" href="create-ad.php">
                        <i class="fas fa-plus-circle me-2"></i> Create Ad
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a class="nav-link" href="packages.php">
                        <i class="fas fa-box me-2"></i> Packages
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i> Payments
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user me-2"></i> Profile
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="?logout=1">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>Create New Ad</h2>
                        <p class="text-muted">Design your custom advertisement</p>
                    </div>
                    <div>
                        <a href="ads.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Ads
                        </a>
                    </div>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Form -->
                    <div class="col-lg-8">
                        <div class="card form-card">
                            <div class="card-header">
                                <h5 class="mb-0">Ad Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Ad Title *</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?php echo isset($title) ? htmlspecialchars($title) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="text" class="form-label">Ad Description *</label>
                                        <textarea class="form-control" id="text" name="text" rows="4" required><?php echo isset($text) ? htmlspecialchars($text) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Ad Image</label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <div class="form-text">Supported formats: JPG, PNG, GIF, WebP. Max size: 5MB</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="url" class="form-label">Action URL</label>
                                        <input type="url" class="form-control" id="url" name="url" 
                                               value="<?php echo isset($url) ? htmlspecialchars($url) : ''; ?>"
                                               placeholder="https://example.com">
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="url_type" class="form-label">URL Type</label>
                                            <select class="form-select" id="url_type" name="url_type">
                                                <option value="website" <?php echo (isset($url_type) && $url_type === 'website') ? 'selected' : ''; ?>>Website</option>
                                                <option value="playstore" <?php echo (isset($url_type) && $url_type === 'playstore') ? 'selected' : ''; ?>>Play Store</option>
                                                <option value="appstore" <?php echo (isset($url_type) && $url_type === 'appstore') ? 'selected' : ''; ?>>App Store</option>
                                                <option value="other" <?php echo (isset($url_type) && $url_type === 'other') ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="button_text" class="form-label">Button Text</label>
                                            <input type="text" class="form-control" id="button_text" name="button_text" 
                                                   value="<?php echo isset($button_text) ? htmlspecialchars($button_text) : ''; ?>"
                                                   placeholder="Visit, Install, Download, etc.">
                                            <div class="form-text">Leave empty for auto-detection</div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="date_start" class="form-label">Start Date *</label>
                                            <input type="date" class="form-control" id="date_start" name="date_start" 
                                                   value="<?php echo isset($date_start) ? $date_start : date('Y-m-d'); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="date_end" class="form-label">End Date *</label>
                                            <input type="date" class="form-control" id="date_end" name="date_end" 
                                                   value="<?php echo isset($date_end) ? $date_end : date('Y-m-d', strtotime('+30 days')); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="create_ad" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Create Ad
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Preview -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Preview</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="preview-card">
                                    <div class="preview-image" id="previewImage">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                    <div class="p-3">
                                        <h6 id="previewTitle">Ad Title</h6>
                                        <p id="previewText" class="text-muted mb-0">Ad description will appear here...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Tips</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Use clear, compelling titles</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Keep descriptions concise</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Use high-quality images</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Set appropriate date ranges</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Live preview functionality
        document.getElementById('title').addEventListener('input', function() {
            document.getElementById('previewTitle').textContent = this.value || 'Ad Title';
        });
        
        document.getElementById('text').addEventListener('input', function() {
            document.getElementById('previewText').textContent = this.value || 'Ad description will appear here...';
        });
        
        document.getElementById('image').addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImage').innerHTML = 
                        '<img src="' + e.target.result + '" style="width: 100%; height: 100%; object-fit: cover;">';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date_start').min = today;
        document.getElementById('date_end').min = today;
        
        // Update end date minimum when start date changes
        document.getElementById('date_start').addEventListener('change', function() {
            document.getElementById('date_end').min = this.value;
        });
    </script>
</body>
</html>
