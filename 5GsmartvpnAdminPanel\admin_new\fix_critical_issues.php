<?php
/**
 * Fix Critical Issues - Ad Approval System & Firebase Notifications
 * Comprehensive fix for both reported issues
 */

session_start();
require_once 'includes/config.php';

echo "<h2>Critical Issues Fix Tool</h2>";
echo "<p>This tool will diagnose and fix both the Ad Approval System and Firebase Notification issues.</p>";

try {
    // Check database connection
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // ISSUE 1: AD APPROVAL SYSTEM
    echo "<h3>ISSUE 1: Ad Approval System Diagnosis</h3>";
    
    // Check if custom_ads table has required columns
    echo "<h4>1.1 Database Schema Check</h4>";
    $required_columns = ['is_approved', 'approved_by', 'approved_at', 'customer_id', 'package_id', 'payment_id', 'created_at'];
    $columns_result = $conn->query("DESCRIBE custom_ads");
    $existing_columns = [];
    if ($columns_result) {
        while ($col = $columns_result->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }
    }
    
    $missing_columns = array_diff($required_columns, $existing_columns);
    if (empty($missing_columns)) {
        echo "<p style='color: green;'>✓ All required columns exist in custom_ads table</p>";
    } else {
        echo "<p style='color: red;'>✗ Missing columns: " . implode(', ', $missing_columns) . "</p>";
    }
    
    // Check for ads that should be pending approval
    echo "<h4>1.2 Pending Ads Analysis</h4>";
    $pending_ads_query = "SELECT COUNT(*) as count FROM custom_ads WHERE is_approved IS NULL";
    $pending_result = $conn->query($pending_ads_query);
    $pending_count = $pending_result ? $pending_result->fetch_assoc()['count'] : 0;
    
    echo "<p><strong>Ads pending approval:</strong> $pending_count</p>";
    
    // Check for approved ads
    $approved_ads_query = "SELECT COUNT(*) as count FROM custom_ads WHERE is_approved = 1";
    $approved_result = $conn->query($approved_ads_query);
    $approved_count = $approved_result ? $approved_result->fetch_assoc()['count'] : 0;
    
    echo "<p><strong>Approved ads:</strong> $approved_count</p>";
    
    // Check for ads with missing relationships
    echo "<h4>1.3 Data Relationship Analysis</h4>";
    $orphaned_ads_query = "
        SELECT COUNT(*) as count 
        FROM custom_ads ca 
        WHERE ca.customer_id IS NOT NULL 
        AND NOT EXISTS (SELECT 1 FROM customer_accounts cac WHERE cac.id = ca.customer_id)
    ";
    $orphaned_result = $conn->query($orphaned_ads_query);
    $orphaned_count = $orphaned_result ? $orphaned_result->fetch_assoc()['count'] : 0;
    
    if ($orphaned_count > 0) {
        echo "<p style='color: orange;'>⚠ Found $orphaned_count ads with invalid customer references</p>";
    } else {
        echo "<p style='color: green;'>✓ All ad-customer relationships are valid</p>";
    }

    // ISSUE 2: FIREBASE NOTIFICATIONS
    echo "<h3>ISSUE 2: Firebase Notifications Diagnosis</h3>";
    
    // Check Firebase configuration files
    echo "<h4>2.1 Firebase Configuration</h4>";
    $service_account_file = __DIR__ . '/notifications/service-account-file.json';
    
    if (file_exists($service_account_file)) {
        echo "<p style='color: green;'>✓ Firebase service account file exists</p>";
        
        $service_content = file_get_contents($service_account_file);
        $service_data = json_decode($service_content, true);
        
        if ($service_data && isset($service_data['project_id'])) {
            echo "<p style='color: green;'>✓ Service account file is valid (Project: " . htmlspecialchars($service_data['project_id']) . ")</p>";
        } else {
            echo "<p style='color: red;'>✗ Service account file is invalid or corrupted</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Firebase service account file missing</p>";
        echo "<p><strong>Required path:</strong> " . htmlspecialchars($service_account_file) . "</p>";
    }
    
    // Check notifications table
    echo "<h4>2.2 Notifications Database</h4>";
    $notifications_table_check = $conn->query("SHOW TABLES LIKE 'notifications'");
    if ($notifications_table_check && $notifications_table_check->num_rows > 0) {
        echo "<p style='color: green;'>✓ Notifications table exists</p>";
        
        // Check recent notification attempts
        $recent_notifications = $conn->query("
            SELECT status, COUNT(*) as count 
            FROM notifications 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) 
            GROUP BY status
        ");
        
        if ($recent_notifications && $recent_notifications->num_rows > 0) {
            echo "<p><strong>Last 24 hours notification status:</strong></p>";
            echo "<ul>";
            while ($row = $recent_notifications->fetch_assoc()) {
                $color = $row['status'] === 'sent' ? 'green' : ($row['status'] === 'error' ? 'red' : 'orange');
                echo "<li style='color: $color;'>" . ucfirst($row['status']) . ": " . $row['count'] . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠ No notifications sent in the last 24 hours</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Notifications table missing</p>";
    }
    
    // Check notification functions
    echo "<h4>2.3 Notification Functions</h4>";
    if (file_exists(__DIR__ . '/notifications/includes/notification_functions.php')) {
        echo "<p style='color: green;'>✓ Notification functions file exists</p>";
        
        require_once __DIR__ . '/notifications/includes/notification_functions.php';
        
        if (function_exists('sendFCMNotification')) {
            echo "<p style='color: green;'>✓ sendFCMNotification function available</p>";
        } else {
            echo "<p style='color: red;'>✗ sendFCMNotification function missing</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Notification functions file missing</p>";
    }

    // FIX ACTIONS
    echo "<h3>Available Fix Actions</h3>";
    
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
    
    // Ad Approval Fixes
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='fix_ad_approval_schema' style='background: #2196F3; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Fix Ad Approval Schema</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='create_test_pending_ad' style='background: #FF9800; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Create Test Pending Ad</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='fix_orphaned_ads' style='background: #9C27B0; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Fix Orphaned Ads</button>";
    echo "</form>";
    
    // Firebase Notification Fixes
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='fix_notifications_table' style='background: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Fix Notifications Table</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='test_firebase_connection' style='background: #F44336; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Test Firebase Connection</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline-block;'>";
    echo "<button type='submit' name='send_test_notification' style='background: #607D8B; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Send Test Notification</button>";
    echo "</form>";
    
    echo "</div>";

    // Handle fix actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<h3>Fix Results</h3>";
        
        if (isset($_POST['fix_ad_approval_schema'])) {
            echo "<h4>Fixing Ad Approval Schema...</h4>";
            
            $schema_fixes = [
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT NULL",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS approved_by INT NULL",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP NULL",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS customer_id INT NULL",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS package_id INT NULL", 
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS payment_id INT NULL",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "ALTER TABLE custom_ads ADD COLUMN IF NOT EXISTS admin_notes TEXT NULL"
            ];
            
            foreach ($schema_fixes as $query) {
                if ($conn->query($query)) {
                    echo "<p style='color: green;'>✓ " . htmlspecialchars($query) . "</p>";
                } else {
                    echo "<p style='color: red;'>✗ " . htmlspecialchars($query) . " - " . $conn->error . "</p>";
                }
            }
        }
        
        if (isset($_POST['create_test_pending_ad'])) {
            echo "<h4>Creating Test Pending Ad...</h4>";
            
            $test_title = "Test Ad Pending Approval - " . date('Y-m-d H:i:s');
            $test_text = "This is a test advertisement created to test the approval system workflow.";
            $test_url = "https://example.com/test-ad";
            $test_start = date('Y-m-d');
            $test_end = date('Y-m-d', strtotime('+30 days'));
            
            $insert_query = "INSERT INTO custom_ads (title, text, url, date_start, date_end, `on`, view_count, click_count, is_approved, priority, url_type, button_text, created_at) 
                           VALUES (?, ?, ?, ?, ?, 1, 0, 0, NULL, 1, 'website', 'Visit', NOW())";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("sssss", $test_title, $test_text, $test_url, $test_start, $test_end);
            
            if ($stmt->execute()) {
                $new_id = $conn->insert_id;
                echo "<p style='color: green;'>✓ Test ad created successfully (ID: $new_id)</p>";
                echo "<p><a href='ad-approval.php?filter=pending' target='_blank'>View in Ad Approval System</a></p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create test ad: " . $conn->error . "</p>";
            }
        }
        
        if (isset($_POST['fix_orphaned_ads'])) {
            echo "<h4>Fixing Orphaned Ads...</h4>";
            
            // Set orphaned ads to NULL customer_id
            $fix_orphaned = "UPDATE custom_ads SET customer_id = NULL WHERE customer_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM customer_accounts WHERE id = custom_ads.customer_id)";
            
            if ($conn->query($fix_orphaned)) {
                $affected = $conn->affected_rows;
                echo "<p style='color: green;'>✓ Fixed $affected orphaned ad references</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to fix orphaned ads: " . $conn->error . "</p>";
            }
        }
        
        if (isset($_POST['fix_notifications_table'])) {
            echo "<h4>Fixing Notifications Table...</h4>";
            
            $create_notifications_table = "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('pending', 'sent', 'error', 'scheduled') DEFAULT 'pending',
                response TEXT,
                sent_to VARCHAR(100) DEFAULT 'all',
                notification_type VARCHAR(50) DEFAULT 'general',
                schedule_type VARCHAR(20) DEFAULT 'immediate',
                scheduled_time DATETIME DEFAULT NULL,
                recurring_interval VARCHAR(20) DEFAULT NULL,
                next_run_time DATETIME DEFAULT NULL,
                last_run_time DATETIME DEFAULT NULL,
                data JSON DEFAULT NULL,
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                category VARCHAR(50) DEFAULT 'general',
                target_audience VARCHAR(100) DEFAULT 'all_users',
                delivery_count INT DEFAULT 0,
                success_count INT DEFAULT 0,
                failure_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if ($conn->query($create_notifications_table)) {
                echo "<p style='color: green;'>✓ Notifications table created/updated successfully</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create notifications table: " . $conn->error . "</p>";
            }
        }
        
        if (isset($_POST['test_firebase_connection'])) {
            echo "<h4>Testing Firebase Connection...</h4>";
            
            $test_urls = [
                'Firebase FCM' => 'https://fcm.googleapis.com',
                'Google OAuth' => 'https://oauth2.googleapis.com'
            ];
            
            foreach ($test_urls as $name => $url) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                
                $result = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code >= 200 && $http_code < 400) {
                    echo "<p style='color: green;'>✓ $name: Connected (HTTP $http_code)</p>";
                } else {
                    echo "<p style='color: red;'>✗ $name: Failed (HTTP $http_code)</p>";
                }
            }
        }
        
        if (isset($_POST['send_test_notification'])) {
            echo "<h4>Sending Test Notification...</h4>";
            
            if (function_exists('sendFCMNotification')) {
                $test_title = "Test Notification - " . date('H:i:s');
                $test_message = "This is a test notification to verify the Firebase system is working. Sent at " . date('Y-m-d H:i:s');
                
                $result = sendFCMNotification($test_title, $test_message, 'all', 'immediate', null, null, [
                    'test' => true,
                    'debug' => true,
                    'timestamp' => time(),
                    'source' => 'critical_issues_fix'
                ]);
                
                echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
                echo json_encode($result, JSON_PRETTY_PRINT);
                echo "</pre>";
                
                if ($result['success']) {
                    echo "<p style='color: green;'>✓ Test notification sent successfully!</p>";
                } else {
                    echo "<p style='color: red;'>✗ Test notification failed</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ sendFCMNotification function not available</p>";
            }
        }
        
        echo "<script>setTimeout(function(){ window.scrollTo(0, document.body.scrollHeight); }, 100);</script>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Critical Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Quick Links</h3>";
echo "<ul>";
echo "<li><a href='ad-approval.php' target='_blank'>Ad Approval System</a></li>";
echo "<li><a href='notifications.php' target='_blank'>Notifications Management</a></li>";
echo "<li><a href='debug_custom_ads.php' target='_blank'>Debug Custom Ads</a></li>";
echo "<li><a href='debug_firebase_notifications.php' target='_blank'>Debug Firebase Notifications</a></li>";
echo "</ul>";

$conn->close();
?>
