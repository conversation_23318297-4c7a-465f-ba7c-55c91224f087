<?php
/**
 * Simple Analytics Page - Minimal working version
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Ad Analytics (Simple)';

// Initialize analytics with safe defaults
$analytics = [
    'total_impressions' => 0,
    'total_clicks' => 0,
    'total_revenue' => 0,
    'ctr' => 0,
    'custom_ads_views' => 0,
    'custom_ads_clicks' => 0,
    'custom_ads_ctr' => 0
];

// Safely get analytics data
if ($conn) {
    try {
        // Get custom ads data
        $custom_query = "SELECT SUM(view_count) as total_views, SUM(click_count) as total_clicks FROM custom_ads WHERE `on` = 1";
        $result = mysqli_query($conn, $custom_query);
        if ($result && $row = mysqli_fetch_assoc($result)) {
            $analytics['custom_ads_views'] = (int)($row['total_views'] ?? 0);
            $analytics['custom_ads_clicks'] = (int)($row['total_clicks'] ?? 0);
            $analytics['total_impressions'] = $analytics['custom_ads_views'];
            $analytics['total_clicks'] = $analytics['custom_ads_clicks'];
            
            if ($analytics['total_impressions'] > 0) {
                $analytics['ctr'] = round(($analytics['total_clicks'] / $analytics['total_impressions']) * 100, 2);
                $analytics['custom_ads_ctr'] = $analytics['ctr'];
            }
            
            $analytics['total_revenue'] = $analytics['total_clicks'] * 0.05;
        }
    } catch (Exception $e) {
        error_log("Simple analytics error: " . $e->getMessage());
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Ad Analytics (Simple Version)</h1>
                <p class="page-subtitle">Basic advertising performance metrics</p>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Overview Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-eye-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($analytics['total_impressions']); ?></h3>
                        <p class="stat-label">Total Impressions</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            Custom ads only
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-cursor-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($analytics['total_clicks']); ?></h3>
                        <p class="stat-label">Total Clicks</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            Custom ads only
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-money-dollar-circle-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">$<?php echo number_format($analytics['total_revenue'], 2); ?></h3>
                        <p class="stat-label">Estimated Revenue</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            $0.05 per click
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-percent-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $analytics['ctr']; ?>%</h3>
                        <p class="stat-label">Click-Through Rate</p>
                        <span class="stat-change neutral">
                            <i class="ri-subtract-line"></i>
                            Custom ads CTR
                        </span>
                    </div>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- Custom Ads Performance -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Custom Ads Performance</h3>
                        <div class="card-actions">
                            <a href="custom-ads.php" class="btn btn-sm btn-primary">
                                Manage Custom Ads
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="custom-ads-stats">
                            <div class="stat-row">
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo number_format($analytics['custom_ads_views']); ?></h4>
                                    <p class="stat-label">Total Views</p>
                                </div>
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo number_format($analytics['custom_ads_clicks']); ?></h4>
                                    <p class="stat-label">Total Clicks</p>
                                </div>
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo $analytics['custom_ads_ctr']; ?>%</h4>
                                    <p class="stat-label">CTR</p>
                                </div>
                            </div>
                            
                            <?php if ($analytics['custom_ads_views'] == 0): ?>
                            <div class="empty-state">
                                <i class="ri-advertisement-line empty-state-icon"></i>
                                <h3>No Custom Ads Data</h3>
                                <p>Create custom ads to see performance metrics here.</p>
                                <a href="custom-ad-add.php" class="btn btn-primary">
                                    <i class="ri-add-line"></i>
                                    Create Custom Ad
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Integration Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Integration Status</h3>
                    </div>
                    <div class="card-body">
                        <div class="metrics-list">
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-database-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">Database Connection</h4>
                                    <p class="metric-value"><?php echo $conn ? 'Connected' : 'Disconnected'; ?></p>
                                    <small class="metric-description">MySQL database status</small>
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-smartphone-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">Android App Integration</h4>
                                    <p class="metric-value">Ready</p>
                                    <small class="metric-description">API endpoints configured</small>
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-line-chart-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">Real-time Tracking</h4>
                                    <p class="metric-value">
                                        <?php 
                                        $tracking_exists = false;
                                        if ($conn) {
                                            $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'ad_tracking'");
                                            $tracking_exists = mysqli_num_rows($table_check) > 0;
                                        }
                                        echo $tracking_exists ? 'Active' : 'Pending';
                                        ?>
                                    </p>
                                    <small class="metric-description">
                                        <?php echo $tracking_exists ? 'Tracking table exists' : 'Will be created on first tracking event'; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="action-buttons">
                            <a href="debug_analytics.php" class="btn btn-outline-primary btn-block">
                                <i class="ri-bug-line"></i>
                                Debug Integration
                            </a>
                            <a href="test_analytics_integration.php" class="btn btn-outline-info btn-block">
                                <i class="ri-test-tube-line"></i>
                                Test Integration
                            </a>
                            <a href="ad-analytics.php" class="btn btn-outline-success btn-block">
                                <i class="ri-dashboard-line"></i>
                                Full Analytics Dashboard
                            </a>
                            <a href="api/analytics.php?action=summary&timestamp=<?php echo time(); ?>&signature=<?php echo hash_hmac('sha256', time(), $API_KEY); ?>" class="btn btn-outline-warning btn-block" target="_blank">
                                <i class="ri-api-line"></i>
                                Test Analytics API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.stat-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.metrics-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

.metric-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-100);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.5rem;
}

.metric-content {
    flex: 1;
}

.metric-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.metric-content .metric-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.metric-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-block {
    width: 100%;
    justify-content: flex-start;
}
</style>

<?php include 'includes/footer.php'; ?>
