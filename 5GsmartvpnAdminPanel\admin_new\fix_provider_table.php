<?php
/**
 * Fix Custom Ads Providers Table
 * Adds missing business_address column
 */

require_once 'includes/config.php';

echo "<h2>Custom Ads Providers Table Fix</h2>";

try {
    // Add business_address column if it doesn't exist
    $sql = "ALTER TABLE custom_ads_providers ADD COLUMN IF NOT EXISTS business_address TEXT NULL";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>✓ Successfully added business_address column to custom_ads_providers table</p>";
    } else {
        echo "<p style='color: red;'>✗ Error adding business_address column: " . $conn->error . "</p>";
    }
    
    // Verify table structure
    echo "<h3>Current Table Structure:</h3>";
    $result = $conn->query("DESCRIBE custom_ads_providers");
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p style='color: blue;'>✓ Database fix completed successfully!</p>";
    echo "<p><a href='../customads-provider/profile.php'>Test Profile Page</a> | <a href='../customads-provider/create-ad.php'>Test Create Ad Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
