package com.official.fivegfastvpn.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.official.fivegfastvpn.activity.NotificationsActivity;
import com.official.fivegfastvpn.api.NotificationApiService;
import com.official.fivegfastvpn.model.NotificationModel;

import java.util.List;

/**
 * Central notification manager for the app
 * Handles notification operations and provides a unified interface
 */
public class NotificationManager {
    private static final String TAG = "NotificationManager";
    private static NotificationManager instance;
    
    private Context context;
    private NotificationStorage storage;
    private NotificationApiService apiService;
    
    private NotificationManager(Context context) {
        this.context = context.getApplicationContext();
        this.storage = NotificationStorage.getInstance(this.context);
        this.apiService = NotificationApiService.getInstance();
    }
    
    public static synchronized NotificationManager getInstance(Context context) {
        if (instance == null) {
            instance = new NotificationManager(context);
        }
        return instance;
    }
    
    /**
     * Get unread notification count
     */
    public int getUnreadCount() {
        return storage.getUnreadCount();
    }
    
    /**
     * Get cached unread count (faster)
     */
    public int getCachedUnreadCount() {
        return storage.getCachedUnreadCount();
    }
    
    /**
     * Mark a notification as read
     */
    public void markAsRead(int notificationId) {
        storage.markAsRead(notificationId);
        Log.d(TAG, "Marked notification " + notificationId + " as read");
    }
    
    /**
     * Mark all notifications as read
     */
    public void markAllAsRead() {
        storage.markAllAsRead();
        Log.d(TAG, "Marked all notifications as read");
    }
    
    /**
     * Get all cached notifications
     */
    public List<NotificationModel> getCachedNotifications() {
        return storage.loadNotifications();
    }
    
    /**
     * Add a new notification (typically from FCM)
     */
    public void addNotification(NotificationModel notification) {
        storage.addNotification(notification);
        Log.d(TAG, "Added new notification: " + notification.getTitle());
    }
    
    /**
     * Sync notifications from server
     */
    public void syncNotifications(SyncCallback callback) {
        apiService.getNotifications(1, 50, new NotificationApiService.ApiCallback<NotificationApiService.NotificationResponse>() {
            @Override
            public void onSuccess(NotificationApiService.NotificationResponse response) {
                List<NotificationModel> notifications = response.getNotifications();
                storage.saveNotifications(notifications);
                
                if (callback != null) {
                    callback.onSyncComplete(true, notifications.size());
                }
                
                Log.d(TAG, "Synced " + notifications.size() + " notifications from server");
            }
            
            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onSyncComplete(false, 0);
                }
                
                Log.e(TAG, "Failed to sync notifications: " + error);
            }
        });
    }
    
    /**
     * Open notifications activity
     */
    public void openNotificationsActivity() {
        Intent intent = new Intent(context, NotificationsActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * Check if cache is expired and needs refresh
     */
    public boolean isCacheExpired() {
        return storage.isCacheExpired();
    }
    
    /**
     * Clear all notification cache
     */
    public void clearCache() {
        storage.clearCache();
        Log.d(TAG, "Cleared notification cache");
    }
    
    /**
     * Get last sync time
     */
    public long getLastSyncTime() {
        return storage.getLastSyncTime();
    }
    
    /**
     * Send a notification through the admin panel
     */
    public void sendNotification(String title, String message, String priority, String category, SendCallback callback) {
        apiService.sendNotification(title, message, priority, category, "all", new NotificationApiService.ApiCallback<String>() {
            @Override
            public void onSuccess(String result) {
                if (callback != null) {
                    callback.onSendComplete(true, result);
                }
                Log.d(TAG, "Notification sent successfully: " + title);
            }
            
            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onSendComplete(false, error);
                }
                Log.e(TAG, "Failed to send notification: " + error);
            }
        });
    }
    
    /**
     * Get notification statistics
     */
    public NotificationStats getStats() {
        List<NotificationModel> notifications = getCachedNotifications();
        
        int total = notifications.size();
        int unread = 0;
        int urgent = 0;
        int today = 0;
        
        long todayStart = System.currentTimeMillis() - (24 * 60 * 60 * 1000); // 24 hours ago
        
        for (NotificationModel notification : notifications) {
            if (!notification.isRead()) {
                unread++;
            }
            
            if ("urgent".equals(notification.getPriority())) {
                urgent++;
            }
            
            try {
                java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
                java.util.Date date = format.parse(notification.getCreatedAt());
                if (date != null && date.getTime() > todayStart) {
                    today++;
                }
            } catch (Exception e) {
                // Ignore parsing errors
            }
        }
        
        return new NotificationStats(total, unread, urgent, today);
    }
    
    /**
     * Callback interface for sync operations
     */
    public interface SyncCallback {
        void onSyncComplete(boolean success, int count);
    }
    
    /**
     * Callback interface for send operations
     */
    public interface SendCallback {
        void onSendComplete(boolean success, String message);
    }
    
    /**
     * Notification statistics class
     */
    public static class NotificationStats {
        public final int total;
        public final int unread;
        public final int urgent;
        public final int today;
        
        public NotificationStats(int total, int unread, int urgent, int today) {
            this.total = total;
            this.unread = unread;
            this.urgent = urgent;
            this.today = today;
        }
        
        @Override
        public String toString() {
            return "NotificationStats{" +
                    "total=" + total +
                    ", unread=" + unread +
                    ", urgent=" + urgent +
                    ", today=" + today +
                    '}';
        }
    }
    
    /**
     * Initialize notification manager (call from Application class)
     */
    public static void initialize(Context context) {
        getInstance(context);
        Log.d(TAG, "NotificationManager initialized");
    }
    
    /**
     * Handle notification click from system notification
     */
    public void handleNotificationClick(Intent intent) {
        if (intent != null && intent.getBooleanExtra("from_notification", false)) {
            // Extract notification data from intent
            String title = intent.getStringExtra("title");
            String message = intent.getStringExtra("message");
            String priority = intent.getStringExtra("priority");
            String category = intent.getStringExtra("category");
            
            Log.d(TAG, "Handling notification click: " + title);
            
            // Open notifications activity
            openNotificationsActivity();
        }
    }
}
