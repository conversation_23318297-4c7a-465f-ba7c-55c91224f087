package com.official.fivegfastvpn.model;

import java.util.Date;

/**
 * Notification Model for 5G Smart VPN
 * Represents a notification from the admin panel
 */
public class NotificationModel {
    private int id;
    private String title;
    private String message;
    private String messagePreview;
    private String status;
    private String sentTo;
    private String notificationType;
    private String scheduleType;
    private String scheduledTime;
    private String recurringInterval;
    private String priority;
    private String category;
    private String targetAudience;
    private int deliveryCount;
    private int successCount;
    private int failureCount;
    private String createdAt;
    private String updatedAt;
    private boolean isRead;
    private String data;

    // Constructors
    public NotificationModel() {}

    public NotificationModel(int id, String title, String message, String status, String createdAt) {
        this.id = id;
        this.title = title;
        this.message = message;
        this.status = status;
        this.createdAt = createdAt;
        this.isRead = false;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessagePreview() {
        return messagePreview;
    }

    public void setMessagePreview(String messagePreview) {
        this.messagePreview = messagePreview;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSentTo() {
        return sentTo;
    }

    public void setSentTo(String sentTo) {
        this.sentTo = sentTo;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(String scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public String getRecurringInterval() {
        return recurringInterval;
    }

    public void setRecurringInterval(String recurringInterval) {
        this.recurringInterval = recurringInterval;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTargetAudience() {
        return targetAudience;
    }

    public void setTargetAudience(String targetAudience) {
        this.targetAudience = targetAudience;
    }

    public int getDeliveryCount() {
        return deliveryCount;
    }

    public void setDeliveryCount(int deliveryCount) {
        this.deliveryCount = deliveryCount;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    // Utility methods
    public String getFormattedDate() {
        if (createdAt != null && !createdAt.isEmpty()) {
            try {
                // Parse the date and format it for display
                // This is a simple implementation, you might want to use a proper date formatter
                return createdAt.substring(0, 10); // Returns YYYY-MM-DD part
            } catch (Exception e) {
                return createdAt;
            }
        }
        return "";
    }

    public String getFormattedTime() {
        if (createdAt != null && !createdAt.isEmpty()) {
            try {
                // Extract time part
                if (createdAt.length() > 10) {
                    return createdAt.substring(11, 16); // Returns HH:MM part
                }
            } catch (Exception e) {
                return "";
            }
        }
        return "";
    }

    public String getPriorityColor() {
        if (priority == null) return "#6B7280";
        
        switch (priority.toLowerCase()) {
            case "urgent":
                return "#EF4444";
            case "high":
                return "#F59E0B";
            case "normal":
                return "#10B981";
            case "low":
                return "#6B7280";
            default:
                return "#6B7280";
        }
    }

    public String getCategoryColor() {
        if (category == null) return "#3B82F6";
        
        switch (category.toLowerCase()) {
            case "urgent":
                return "#EF4444";
            case "security":
                return "#DC2626";
            case "update":
                return "#3B82F6";
            case "maintenance":
                return "#F59E0B";
            case "welcome":
                return "#10B981";
            default:
                return "#6366F1";
        }
    }

    @Override
    public String toString() {
        return "NotificationModel{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", message='" + message + '\'' +
                ", status='" + status + '\'' +
                ", priority='" + priority + '\'' +
                ", category='" + category + '\'' +
                ", createdAt='" + createdAt + '\'' +
                ", isRead=" + isRead +
                '}';
    }
}
