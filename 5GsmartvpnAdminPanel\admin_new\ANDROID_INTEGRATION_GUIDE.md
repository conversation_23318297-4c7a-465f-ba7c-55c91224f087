# 📱 Android App Integration Guide

## 🔥 Firebase Notification Setup

### **Why Notifications May Not Reach Your App**

1. **Project ID Mismatch** ✅ FIXED
2. **Missing Topic Subscription** 
3. **Incorrect google-services.json**
4. **App Not Running FCM Service**
5. **Permission Issues**

---

## 🛠️ **Android App Requirements**

### 1. **Add Firebase Dependencies**

In your `app/build.gradle`:

```gradle
dependencies {
    implementation 'com.google.firebase:firebase-messaging:23.4.0'
    implementation 'com.google.firebase:firebase-analytics:21.5.0'
}
```

In your project-level `build.gradle`:

```gradle
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
    }
}
```

Apply the plugin in `app/build.gradle`:

```gradle
apply plugin: 'com.google.gms.google-services'
```

### 2. **Add google-services.json**

1. Download from Firebase Console
2. Place in `app/` directory
3. Ensure it matches your Firebase project

### 3. **Add Permissions**

In `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

<permission
    android:name="${applicationId}.permission.C2D_MESSAGE"
    android:protectionLevel="signature" />
<uses-permission android:name="${applicationId}.permission.C2D_MESSAGE" />
```

### 4. **Create FCM Service**

Create `MyFirebaseMessagingService.java`:

```java
public class MyFirebaseMessagingService extends FirebaseMessagingService {
    private static final String TAG = "FCMService";

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        
        Log.d(TAG, "From: " + remoteMessage.getFrom());
        Log.d(TAG, "Message data: " + remoteMessage.getData());

        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
            showNotification(
                remoteMessage.getNotification().getTitle(),
                remoteMessage.getNotification().getBody()
            );
        }

        // Handle data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());
            handleDataMessage(remoteMessage.getData());
        }
    }

    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "Refreshed token: " + token);
        sendRegistrationToServer(token);
    }

    private void showNotification(String title, String body) {
        NotificationManager notificationManager = 
            (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        String channelId = "vpn_notifications";
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                channelId,
                "VPN Notifications",
                NotificationManager.IMPORTANCE_HIGH
            );
            notificationManager.createNotificationChannel(channel);
        }

        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder notificationBuilder =
            new NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH);

        notificationManager.notify(0, notificationBuilder.build());
    }

    private void handleDataMessage(Map<String, String> data) {
        // Handle custom data here
        Log.d(TAG, "Handling data message: " + data);
    }

    private void sendRegistrationToServer(String token) {
        // Send token to your server if needed
        Log.d(TAG, "Token sent to server: " + token);
    }
}
```

### 5. **Register Service in Manifest**

```xml
<service
    android:name=".MyFirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

### 6. **Subscribe to Topics**

In your `MainActivity.java` or `Application` class:

```java
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Subscribe to topics
        subscribeToTopics();
        
        // Get FCM token
        getFCMToken();
    }

    private void subscribeToTopics() {
        // Subscribe to 'all' topic for general notifications
        FirebaseMessaging.getInstance().subscribeToTopic("all")
            .addOnCompleteListener(task -> {
                String msg = "Subscribed to 'all' topic";
                if (!task.isSuccessful()) {
                    msg = "Failed to subscribe to 'all' topic";
                }
                Log.d(TAG, msg);
            });

        // Subscribe to other topics as needed
        FirebaseMessaging.getInstance().subscribeToTopic("premium")
            .addOnCompleteListener(task -> {
                String msg = "Subscribed to 'premium' topic";
                if (!task.isSuccessful()) {
                    msg = "Failed to subscribe to 'premium' topic";
                }
                Log.d(TAG, msg);
            });
    }

    private void getFCMToken() {
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                if (!task.isSuccessful()) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                    return;
                }

                // Get new FCM registration token
                String token = task.getResult();
                Log.d(TAG, "FCM Registration Token: " + token);
                
                // Send token to your server if needed
                sendTokenToServer(token);
            });
    }

    private void sendTokenToServer(String token) {
        // Optional: Send token to your admin panel for targeted notifications
        Log.d(TAG, "Sending token to server: " + token);
    }
}
```

---

## 🧪 **Testing Steps**

### 1. **Test from Firebase Console**
1. Go to Firebase Console > Cloud Messaging
2. Send a test message to your app
3. If this works, your app setup is correct

### 2. **Test from Admin Panel**
1. Use the diagnostic tool: `firebase_diagnostic.php`
2. Send test notification from admin panel
3. Check notification status in database

### 3. **Debug Logs**
Add these logs to track issues:

```java
// In your FCM service
Log.d("FCM", "Token: " + FirebaseMessaging.getInstance().getToken());
Log.d("FCM", "Message received: " + remoteMessage.toString());

// In your main activity
Log.d("FCM", "Subscribed to topics successfully");
```

---

## 🔧 **Common Issues & Solutions**

### **Issue 1: Notifications not received**
- ✅ Check topic subscription
- ✅ Verify google-services.json
- ✅ Test from Firebase Console first

### **Issue 2: App crashes on notification**
- ✅ Check notification channel setup (Android 8+)
- ✅ Verify pending intent flags
- ✅ Add proper permissions

### **Issue 3: Background notifications not working**
- ✅ Check battery optimization settings
- ✅ Ensure FCM service is not killed
- ✅ Test on different Android versions

### **Issue 4: Token not generated**
- ✅ Check internet connection
- ✅ Verify Firebase initialization
- ✅ Check Google Play Services

---

## 📊 **API Integration**

Your Android app should use these API endpoints:

```
Base URL: http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/

Endpoints:
- config.php - App configuration
- servers.php - VPN server list  
- custom_ads.php - Advertisement data
- track.php - Usage analytics
```

---

## ✅ **Verification Checklist**

- [ ] Firebase project created and configured
- [ ] google-services.json added to app
- [ ] FCM dependencies added
- [ ] FCM service implemented and registered
- [ ] Topic subscription implemented
- [ ] Notification permissions added
- [ ] Test notification sent from Firebase Console
- [ ] Test notification sent from admin panel
- [ ] App receives notifications in foreground
- [ ] App receives notifications in background
- [ ] Notification click handling works

---

## 🆘 **Still Having Issues?**

1. **Check Firebase Diagnostic Tool**: `firebase_diagnostic.php`
2. **Review Android Logs**: Look for FCM-related errors
3. **Test Step by Step**: Start with Firebase Console, then admin panel
4. **Verify Project Setup**: Ensure all IDs match between Firebase and app

**Remember**: The admin panel now uses Firebase v1 API with dynamic project ID detection, so make sure your service account file is properly configured!
