<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/vpnhome"
    android:id="@+id/main"
    tools:context=".SplashActivity">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="100dp"
        android:src="@drawable/fivegsmartvpn"
        />

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:layout_marginTop="30dp"
        android:layout_below="@+id/logo"
        android:textStyle="bold"
        android:textSize="20dp"
        android:text="@string/app_name"/>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animationView"
        android:layout_width="match_parent"
        android:padding="19dp"
        android:layout_height="200dp"
        android:layout_below="@id/name"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        app:lottie_autoPlay="true"
        app:lottie_rawRes="@raw/loading"
        app:lottie_loop="true" />


</RelativeLayout>