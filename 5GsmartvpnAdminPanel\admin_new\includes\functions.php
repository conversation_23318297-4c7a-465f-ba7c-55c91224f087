<?php
/**
 * 5G Smart VPN Admin Panel - Helper Functions
 * Common functions used throughout the admin panel
 */

/**
 * Get dashboard statistics
 */
function getDashboardStats($conn) {
    $stats = [];

    // Initialize default values
    $stats['total_servers'] = 0;
    $stats['active_servers'] = 0;
    $stats['total_ads'] = 0;
    $stats['running_ads'] = 0;
    $stats['total_ad_views'] = 0;
    $stats['total_ad_clicks'] = 0;
    $stats['ctr'] = 0;
    $stats['total_notifications'] = 0;
    $stats['pending_notifications'] = 0;
    $stats['total_contacts'] = 0;
    $stats['unread_contacts'] = 0;
    $stats['recent_activities'] = [];
    $stats['servers'] = [];

    // Check database connection
    if (!$conn) {
        return $stats;
    }

    // Server statistics with error handling
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'servers'");
    if ($table_check && mysqli_num_rows($table_check) > 0) {
        $result = mysqli_query($conn, "SELECT COUNT(*) as total, SUM(status) as active FROM servers");
        if ($result) {
            $server_stats = mysqli_fetch_assoc($result);
            $stats['total_servers'] = $server_stats['total'] ?? 0;
            $stats['active_servers'] = $server_stats['active'] ?? 0;
        }
    }

    // Custom ads statistics with error handling
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'custom_ads'");
    if ($table_check && mysqli_num_rows($table_check) > 0) {
        $current_date = date('Y-m-d');
        $result = mysqli_query($conn, "
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN date_start <= '$current_date' AND date_end >= '$current_date' AND `on` = 1 THEN 1 ELSE 0 END) as running,
                SUM(view_count) as total_views,
                SUM(click_count) as total_clicks
            FROM custom_ads
        ");
        if ($result) {
            $ad_stats = mysqli_fetch_assoc($result);
            $stats['total_ads'] = $ad_stats['total'] ?? 0;
            $stats['running_ads'] = $ad_stats['running'] ?? 0;
            $stats['total_ad_views'] = $ad_stats['total_views'] ?? 0;
            $stats['total_ad_clicks'] = $ad_stats['total_clicks'] ?? 0;
            $stats['ctr'] = $stats['total_ad_views'] > 0 ? round(($stats['total_ad_clicks'] / $stats['total_ad_views']) * 100, 2) : 0;
        }
    }

    // Notification statistics with error handling
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'notifications'");
    if ($table_check && mysqli_num_rows($table_check) > 0) {
        $result = mysqli_query($conn, "
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' OR status = 'scheduled' THEN 1 ELSE 0 END) as pending
            FROM notifications
        ");
        if ($result) {
            $notification_stats = mysqli_fetch_assoc($result);
            $stats['total_notifications'] = $notification_stats['total'] ?? 0;
            $stats['pending_notifications'] = $notification_stats['pending'] ?? 0;
        }
    }

    // Contact statistics with error handling
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'contact'");
    if ($table_check && mysqli_num_rows($table_check) > 0) {
        $result = mysqli_query($conn, "
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN unread = '1' THEN 1 ELSE 0 END) as unread
            FROM contact
        ");
        if ($result) {
            $contact_stats = mysqli_fetch_assoc($result);
            $stats['total_contacts'] = $contact_stats['total'] ?? 0;
            $stats['unread_contacts'] = $contact_stats['unread'] ?? 0;
        }
    }

    // Recent activities
    $stats['recent_activities'] = getRecentActivities($conn, 5);

    // Server list for dashboard with error handling
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'servers'");
    if ($table_check && mysqli_num_rows($table_check) > 0) {
        $result = mysqli_query($conn, "SELECT id, name, flagURL, type, status FROM servers ORDER BY pos ASC LIMIT 5");
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $stats['servers'][] = $row;
            }
        }
    }

    return $stats;
}

/**
 * Get recent activities
 */
function getRecentActivities($conn, $limit = 10) {
    $activities = [];

    // Check if admin_logs table exists
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'admin_logs'");
    if (mysqli_num_rows($table_check) == 0) {
        // Create admin_logs table if it doesn't exist
        $create_table = "
            CREATE TABLE admin_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT DEFAULT 1,
                admin_username VARCHAR(255) DEFAULT 'admin',
                action VARCHAR(100),
                description TEXT,
                ip_address VARCHAR(45) DEFAULT '127.0.0.1',
                user_agent TEXT DEFAULT 'Dashboard',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_created_at (created_at),
                INDEX idx_action (action)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        mysqli_query($conn, $create_table);

        // Add a sample activity to show the system is working
        $sample_sql = "INSERT INTO admin_logs (action, description) VALUES ('system_init', 'Activity logging system initialized')";
        mysqli_query($conn, $sample_sql);
    }

    $query = "SELECT * FROM admin_logs ORDER BY created_at DESC LIMIT $limit";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $icon = getActivityIcon($row['action']);
            $activities[] = [
                'icon' => $icon,
                'text' => $row['description'],
                'time' => $row['created_at']
            ];
        }
    } else {
        // If no activities exist, add some default ones
        $default_activities = [
            ['action' => 'dashboard_view', 'description' => 'Dashboard accessed'],
            ['action' => 'system_status', 'description' => 'System status checked']
        ];

        foreach ($default_activities as $activity) {
            $insert_sql = "INSERT INTO admin_logs (action, description) VALUES (?, ?)";
            $stmt = $conn->prepare($insert_sql);
            $stmt->bind_param("ss", $activity['action'], $activity['description']);
            $stmt->execute();

            $activities[] = [
                'icon' => getActivityIcon($activity['action']),
                'text' => $activity['description'],
                'time' => date('Y-m-d H:i:s')
            ];
        }
    }

    return $activities;
}

/**
 * Get activity icon based on action
 */
function getActivityIcon($action) {
    $icons = [
        'login' => 'ri-login-box-line',
        'logout' => 'ri-logout-box-line',
        'create' => 'ri-add-circle-line',
        'update' => 'ri-edit-circle-line',
        'delete' => 'ri-delete-bin-line',
        'upload' => 'ri-upload-cloud-line',
        'download' => 'ri-download-cloud-line',
        'send' => 'ri-send-plane-line',
        'view' => 'ri-eye-line',
        'settings' => 'ri-settings-line'
    ];

    return $icons[$action] ?? 'ri-information-line';
}

/**
 * Format time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';

    return floor($time/31536000) . ' years ago';
}

/**
 * Sanitize input (only declare if not already defined)
 */
if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Upload file
 */
function uploadFile($file, $destination_dir, $allowed_types = null) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }

    $allowed_types = $allowed_types ?? ALLOWED_IMAGE_TYPES;
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File too large'];
    }

    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $destination = UPLOAD_PATH . $destination_dir . '/' . $filename;

    if (move_uploaded_file($file['tmp_name'], $destination)) {
        return [
            'success' => true,
            'filename' => $filename,
            'path' => $destination_dir . '/' . $filename,
            'url' => UPLOAD_URL . $destination_dir . '/' . $filename
        ];
    }

    return ['success' => false, 'message' => 'Failed to upload file'];
}

/**
 * Delete file
 */
function deleteFile($file_path) {
    $full_path = UPLOAD_PATH . $file_path;
    if (file_exists($full_path)) {
        return unlink($full_path);
    }
    return false;
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Generate pagination
 */
function generatePagination($current_page, $total_pages, $base_url) {
    if ($total_pages <= 1) return '';

    $pagination = '<div class="pagination">';

    // Previous button
    if ($current_page > 1) {
        $pagination .= '<a href="' . $base_url . '?page=' . ($current_page - 1) . '" class="pagination-btn">Previous</a>';
    }

    // Page numbers
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);

    for ($i = $start; $i <= $end; $i++) {
        $active = $i == $current_page ? 'active' : '';
        $pagination .= '<a href="' . $base_url . '?page=' . $i . '" class="pagination-btn ' . $active . '">' . $i . '</a>';
    }

    // Next button
    if ($current_page < $total_pages) {
        $pagination .= '<a href="' . $base_url . '?page=' . ($current_page + 1) . '" class="pagination-btn">Next</a>';
    }

    $pagination .= '</div>';

    return $pagination;
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit();
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return $message;
    }
    return null;
}
?>
