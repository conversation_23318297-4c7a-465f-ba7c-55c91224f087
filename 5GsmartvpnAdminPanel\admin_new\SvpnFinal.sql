-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 02, 2025 at 04:36 AM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `5gsmartvpnnewupdate`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `admin_username` varchar(255) DEFAULT NULL,
  `action` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`id`, `admin_id`, `admin_username`, `action`, `description`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:25:35'),
(2, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:25:37'),
(3, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:25:40'),
(4, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:26:20'),
(5, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:26:38'),
(6, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:27:35'),
(7, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:16'),
(8, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:17'),
(9, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:17'),
(10, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:17'),
(11, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:18'),
(12, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:34:20'),
(13, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:35:29'),
(14, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:27'),
(15, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:28'),
(16, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:43'),
(17, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:44'),
(18, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:50'),
(19, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:51'),
(20, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:51'),
(21, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:51'),
(22, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:39:52'),
(23, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:40:53'),
(24, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:40:53'),
(25, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:40:55'),
(26, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:43:02'),
(27, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:43:37'),
(28, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:43:38'),
(29, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-06-01 21:44:34'),
(30, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 21:52:45'),
(31, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 21:53:01'),
(32, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:34:30'),
(33, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:35:07'),
(34, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:36:46'),
(35, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:36:56'),
(36, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:37:42'),
(37, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:37:58'),
(38, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:39:30'),
(39, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:41:16'),
(40, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:44:31'),
(41, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:45:11'),
(42, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:45:14'),
(43, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:49:32'),
(44, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:11'),
(45, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:16'),
(46, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:19'),
(47, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:26'),
(48, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:42'),
(49, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:50:57'),
(50, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:54:37'),
(51, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:54:41'),
(52, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:55:12'),
(53, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:55:12'),
(54, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:59:29'),
(55, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:59:51'),
(56, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 22:59:56'),
(57, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:00:51'),
(58, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:03:51'),
(59, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:03:55'),
(60, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:04:04'),
(61, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:04:41'),
(62, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:04:50'),
(63, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:06:17'),
(64, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:12:39'),
(65, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:20:49'),
(66, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:20:54'),
(67, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:21:38'),
(68, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:22:14'),
(69, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:22:38'),
(70, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:22:52'),
(71, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:22:57'),
(72, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:33:19'),
(73, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:34:27'),
(74, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:35:20'),
(75, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:38:04'),
(76, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:38:07'),
(77, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:38:16'),
(78, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:39:18'),
(79, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:39:23'),
(80, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:39:46'),
(81, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:39:49'),
(82, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:40:03'),
(83, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:40:06'),
(84, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:06'),
(85, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:08'),
(86, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:14'),
(87, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:20'),
(88, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:26'),
(89, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:41:48'),
(90, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:44:53'),
(91, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:44:57'),
(92, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:45:50'),
(93, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:51:26'),
(94, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-01 23:51:50'),
(95, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-02 00:14:43'),
(96, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-02 00:15:30'),
(97, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-02 00:15:34'),
(98, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', '2025-06-02 00:15:38'),
(99, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '192.168.0.101', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', '2025-06-02 01:40:49'),
(100, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '192.168.0.101', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', '2025-06-02 01:41:01'),
(101, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '192.168.0.101', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-06-02 01:41:13'),
(102, 1, 'admin', 'dashboard_access', 'Accessed admin dashboard', '192.168.0.101', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', '2025-06-02 01:41:19');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `role` enum('admin','moderator','viewer') DEFAULT 'admin',
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password`, `email`, `role`, `status`, `created_at`) VALUES
(1, 'admin', '12345', '<EMAIL>', 'admin', 1, '2025-05-26 22:17:05');

-- --------------------------------------------------------

--
-- Table structure for table `ad_analytics`
--

CREATE TABLE `ad_analytics` (
  `id` int(11) NOT NULL,
  `ad_id` int(11) NOT NULL,
  `event_type` enum('view','click','install','conversion') NOT NULL,
  `user_ip` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `device_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`device_info`)),
  `location_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`location_data`)),
  `referrer` varchar(500) DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ad_packages`
--

CREATE TABLE `ad_packages` (
  `id` int(11) NOT NULL,
  `package_name` varchar(100) NOT NULL,
  `duration_days` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'BDT',
  `max_ads` int(11) DEFAULT 1,
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `is_active` tinyint(1) DEFAULT 1,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ad_packages`
--

INSERT INTO `ad_packages` (`id`, `package_name`, `duration_days`, `price`, `currency`, `max_ads`, `features`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'Basic Package', 7, '500.00', 'BDT', 1, '{\"priority\": 1, \"analytics\": true, \"support\": \"basic\"}', 1, 1, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(2, 'Standard Package', 30, '1800.00', 'BDT', 3, '{\"priority\": 2, \"analytics\": true, \"support\": \"standard\", \"targeting\": true}', 1, 2, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(3, 'Premium Package', 90, '4500.00', 'BDT', 5, '{\"priority\": 3, \"analytics\": true, \"support\": \"premium\", \"targeting\": true, \"featured\": true}', 1, 3, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(4, 'Enterprise Package', 365, '15000.00', 'BDT', 10, '{\"priority\": 4, \"analytics\": true, \"support\": \"enterprise\", \"targeting\": true, \"featured\": true, \"dedicated_support\": true}', 1, 4, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(5, 'Basic Ad', 7, '500.00', 'BDT', 1, '{\"priority\":1,\"analytics\":false,\"support\":\"basic\",\"targeting\":false,\"featured\":false,\"dedicated_support\":false}', 1, 1, '2025-06-01 23:34:01', '2025-06-02 00:02:06'),
(6, 'Premium Ad', 15, '1000.00', 'BDT', 1, '{\"description\": \"Premium ad package for 15 days\", \"features\": [\"Priority placement\", \"Advanced analytics\", \"Featured listing\"]}', 1, 2, '2025-06-01 23:34:01', '2025-06-01 23:34:01'),
(7, 'Extended Ad', 30, '1800.00', 'BDT', 1, '{\"description\": \"Extended ad package for 30 days\", \"features\": [\"Top placement\", \"Detailed analytics\", \"Featured listing\", \"Social media promotion\"]}', 1, 3, '2025-06-01 23:34:01', '2025-06-01 23:34:01');

-- --------------------------------------------------------

--
-- Table structure for table `ad_tracking`
--

CREATE TABLE `ad_tracking` (
  `id` int(11) NOT NULL,
  `ad_type` varchar(50) NOT NULL,
  `ad_id` int(11) DEFAULT NULL,
  `event_type` varchar(20) NOT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `device_info` text DEFAULT NULL,
  `app_version` varchar(20) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ad_tracking`
--

INSERT INTO `ad_tracking` (`id`, `ad_type`, `ad_id`, `event_type`, `user_id`, `device_info`, `app_version`, `created_at`) VALUES
(1, 'custom', 1, 'view', 'test_user_9477', '{\"model\":\"Test Device\",\"os_version\":\"Android 12\",\"app_version\":\"1.0.0\"}', NULL, '2025-06-01 21:24:53'),
(2, 'custom', 1, 'click', 'test_user_3464', '{\"model\":\"Test Device\",\"os_version\":\"Android 12\",\"app_version\":\"1.0.0\"}', NULL, '2025-06-01 21:24:53');

-- --------------------------------------------------------

--
-- Table structure for table `contact_messages`
--

CREATE TABLE `contact_messages` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('unread','read','replied','archived') NOT NULL DEFAULT 'unread',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `category` varchar(50) DEFAULT 'general',
  `reply_text` text DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `replied_at` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_messages`
--

INSERT INTO `contact_messages` (`id`, `name`, `email`, `subject`, `message`, `status`, `priority`, `category`, `reply_text`, `admin_id`, `read_at`, `replied_at`, `ip_address`, `user_agent`, `created_at`, `updated_at`) VALUES
(1, 'John Doe', '<EMAIL>', 'Connection Issue', 'I am having trouble connecting to the VPN servers. Can you help?', 'replied', 'normal', 'technical', 'wer', NULL, '2025-05-27 07:21:02', '2025-05-27 07:21:22', '*************', NULL, '2025-05-27 07:20:52', '2025-05-27 07:21:22');

-- --------------------------------------------------------

--
-- Table structure for table `customer_accounts`
--

CREATE TABLE `customer_accounts` (
  `id` int(11) NOT NULL,
  `whatsapp_number` varchar(20) NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `total_orders` int(11) DEFAULT 0,
  `status` enum('active','suspended','blocked') DEFAULT 'active',
  `registration_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_activity` timestamp NULL DEFAULT NULL,
  `verification_code` varchar(6) DEFAULT NULL,
  `verification_expires` timestamp NULL DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customer_accounts`
--

INSERT INTO `customer_accounts` (`id`, `whatsapp_number`, `customer_name`, `email`, `total_spent`, `total_orders`, `status`, `registration_date`, `last_activity`, `verification_code`, `verification_expires`, `is_verified`) VALUES
(1, '***********', 'Jane Smith', '<EMAIL>', '0.00', 0, 'active', '2025-06-01 22:33:00', NULL, NULL, NULL, 1),
(2, '***********', 'test', '<EMAIL>', '2300.00', 2, 'active', '2025-06-01 22:52:59', NULL, NULL, NULL, 1);

-- --------------------------------------------------------

--
-- Table structure for table `customer_payments`
--

CREATE TABLE `customer_payments` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL,
  `transaction_id` varchar(100) NOT NULL,
  `payment_method` enum('bkash','nagad','rocket','google_pay','paypal','manual') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'BDT',
  `payment_status` enum('pending','verified','rejected','refunded') DEFAULT 'pending',
  `payment_proof` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customer_payments`
--

INSERT INTO `customer_payments` (`id`, `customer_id`, `package_id`, `transaction_id`, `payment_method`, `amount`, `currency`, `payment_status`, `payment_proof`, `admin_notes`, `verified_by`, `verified_at`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 'eewrew', 'bkash', '1800.00', 'BDT', 'verified', 'fsfas', '', 1, '2025-06-01 23:55:20', '2025-06-01 22:52:59', '2025-06-01 23:55:20'),
(2, 2, 5, 'rweerewrew', 'bkash', '500.00', 'BDT', 'verified', '', 'rewrw', 1, '2025-06-02 00:02:45', '2025-06-02 00:02:25', '2025-06-02 00:02:45');

-- --------------------------------------------------------

--
-- Table structure for table `custom_ads`
--

CREATE TABLE `custom_ads` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `text` text DEFAULT NULL,
  `url` text DEFAULT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `on` tinyint(4) NOT NULL DEFAULT 1,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `click_count` int(11) NOT NULL DEFAULT 0,
  `provider_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `payment_id` int(11) DEFAULT NULL,
  `url_type` enum('website','playstore','appstore','other') DEFAULT 'website',
  `button_text` varchar(50) DEFAULT 'Visit',
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_approved` tinyint(1) DEFAULT 0,
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `priority` int(11) DEFAULT 0,
  `target_audience` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`target_audience`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `admin_notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `custom_ads`
--

INSERT INTO `custom_ads` (`id`, `title`, `image`, `text`, `url`, `date_start`, `date_end`, `on`, `view_count`, `click_count`, `provider_id`, `customer_id`, `package_id`, `payment_id`, `url_type`, `button_text`, `expires_at`, `is_approved`, `approved_by`, `approved_at`, `priority`, `target_audience`, `created_at`, `updated_at`, `admin_notes`) VALUES
(1, 'Testing notification', 'images/bannerBg_1741973854.png', 'tee', 'https://google.com', '2025-04-26', '2025-04-28', 1, 7, 3, NULL, NULL, NULL, NULL, 'website', 'Visit', '2025-07-01 22:33:00', 1, NULL, NULL, 1, NULL, '2025-06-01 22:32:52', '2025-06-01 22:33:00', NULL),
(2, 'yrdy', 'images/ad_2_1748799987.jpg', 'testing', 'https://google.com', '2025-05-16', '2025-06-17', 1, 47, 13, NULL, NULL, NULL, NULL, 'website', 'Visit', '2025-07-01 22:33:00', 1, NULL, NULL, 1, NULL, '2025-06-01 22:32:52', '2025-06-02 02:32:16', NULL),
(3, 'Tesing Dialog', 'images/ad_1748822892_683ceb6c73f23.jpg', 'tell me', 'https://test.com', '2025-06-02', '2025-07-03', 1, 3, 0, 2, NULL, NULL, NULL, 'website', 'Visit', NULL, 1, NULL, NULL, 1, NULL, '2025-06-02 00:08:12', '2025-06-02 01:46:24', NULL),
(4, 'Tesing Dialog', 'images/ad_1748824366_683cf12e8c6af.png', 'tell me', 'https://test.com', '2025-06-02', '2025-07-02', 1, 0, 0, 2, NULL, NULL, NULL, 'website', 'test', NULL, 0, NULL, NULL, 1, NULL, '2025-06-02 00:32:46', '2025-06-02 00:32:55', NULL),
(5, 'test', 'images/ad_1748824635_683cf23b236fe.png', 'tell me', 'https://test.com', '2025-06-02', '2025-07-02', 1, 0, 0, 2, NULL, NULL, NULL, 'website', 'because', NULL, 0, NULL, NULL, 1, NULL, '2025-06-02 00:37:15', '2025-06-02 00:37:37', NULL),
(6, 'Test Ad Pending Approval - 2025-06-02 06:39:51', NULL, 'This is a test advertisement created to test the approval system workflow.', 'https://example.com/test-ad', '2025-06-02', '2025-07-03', 1, 7, 0, NULL, NULL, NULL, NULL, 'website', 'Visit', NULL, 1, 1, '2025-06-02 00:43:04', 1, NULL, '2025-06-02 00:39:51', '2025-06-02 02:23:15', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `custom_ads_providers`
--

CREATE TABLE `custom_ads_providers` (
  `id` int(11) NOT NULL,
  `provider_id` varchar(50) NOT NULL,
  `whatsapp_number` varchar(20) NOT NULL,
  `business_name` varchar(255) NOT NULL,
  `contact_person` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `status` enum('active','suspended','pending') DEFAULT 'pending',
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `total_ads` int(11) DEFAULT 0,
  `registration_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `verification_token` varchar(100) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `business_address` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `custom_ads_providers`
--

INSERT INTO `custom_ads_providers` (`id`, `provider_id`, `whatsapp_number`, `business_name`, `contact_person`, `email`, `status`, `total_spent`, `total_ads`, `registration_date`, `last_login`, `password_hash`, `verification_token`, `is_verified`, `business_address`) VALUES
(1, 'PROV001', '***********', 'Sample Business Ltd', 'John Doe', '<EMAIL>', 'active', '0.00', 0, '2025-06-01 22:33:00', NULL, '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, 1, NULL),
(2, 'PROV7615', '***********', 'test', 'test', '<EMAIL>', 'active', '2300.00', 0, '2025-06-01 22:36:08', '2025-06-02 00:32:48', '$2y$10$y/CTdBVoknSs.KLUyCBkqulMkt9na3yeA1uVnXoqcAiQohAkSW8bK', NULL, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `custom_ads_settings`
--

CREATE TABLE `custom_ads_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `custom_ads_settings`
--

INSERT INTO `custom_ads_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `is_public`, `updated_at`) VALUES
(1, 'system_enabled', 'true', 'boolean', 'Enable/disable the custom ads system', 0, '2025-06-01 22:33:00'),
(2, 'auto_approve_ads', 'false', 'boolean', 'Automatically approve ads after payment verification', 0, '2025-06-01 22:33:00'),
(3, 'max_ads_per_customer', '10', 'number', 'Maximum number of ads per customer', 0, '2025-06-01 22:33:00'),
(4, 'default_ad_duration', '30', 'number', 'Default ad duration in days', 0, '2025-06-01 22:33:00'),
(5, 'payment_verification_required', 'true', 'boolean', 'Require admin verification for payments', 0, '2025-06-01 22:33:00'),
(6, 'whatsapp_verification_enabled', 'true', 'boolean', 'Enable WhatsApp number verification', 0, '2025-06-01 22:33:00'),
(7, 'admin_notification_email', '<EMAIL>', 'string', 'Email for admin notifications', 0, '2025-06-01 22:33:00'),
(8, 'customer_support_whatsapp', '***********', 'string', 'WhatsApp number for customer support', 1, '2025-06-01 22:33:00'),
(9, 'terms_and_conditions_url', 'https://5gsmartvpn.com/terms', 'string', 'Terms and conditions URL', 1, '2025-06-01 22:33:00'),
(10, 'privacy_policy_url', 'https://5gsmartvpn.com/privacy', 'string', 'Privacy policy URL', 1, '2025-06-01 22:33:00'),
(11, 'minimum_ad_duration', '7', 'number', 'Minimum ad duration in days', 0, '2025-06-01 22:33:00'),
(12, 'maximum_ad_duration', '365', 'number', 'Maximum ad duration in days', 0, '2025-06-01 22:33:00'),
(13, 'ad_approval_timeout_hours', '24', 'number', 'Hours to approve ads after payment', 0, '2025-06-01 22:33:00'),
(14, 'refund_policy_days', '3', 'number', 'Days for refund policy', 1, '2025-06-01 22:33:00'),
(15, 'featured_ad_multiplier', '2.0', 'number', 'Price multiplier for featured ads', 0, '2025-06-01 22:33:00');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `status` enum('pending','sent','error','scheduled','completed') NOT NULL DEFAULT 'pending',
  `sent_to` varchar(50) DEFAULT 'all',
  `notification_type` varchar(50) DEFAULT 'general',
  `schedule_type` enum('immediate','scheduled','recurring') DEFAULT 'immediate',
  `scheduled_time` datetime DEFAULT NULL,
  `recurring_interval` enum('daily','weekly','monthly') DEFAULT NULL,
  `next_run_time` datetime DEFAULT NULL,
  `last_run_time` datetime DEFAULT NULL,
  `response` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `topic` varchar(100) DEFAULT 'all',
  `category` varchar(50) DEFAULT 'general',
  `target_audience` varchar(100) DEFAULT 'all_users',
  `delivery_count` int(11) DEFAULT 0,
  `success_count` int(11) DEFAULT 0,
  `failure_count` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `title`, `message`, `type`, `status`, `sent_to`, `notification_type`, `schedule_type`, `scheduled_time`, `recurring_interval`, `next_run_time`, `last_run_time`, `response`, `created_at`, `updated_at`, `data`, `priority`, `topic`, `category`, `target_audience`, `delivery_count`, `success_count`, `failure_count`, `created_by`) VALUES
(27, 'Testing notification', 'tethis', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6569532283941482694\"}', '2025-06-02 03:43:54', '2025-06-02 03:43:54', '{\"priority\":\"normal\",\"category\":\"general\",\"admin_id\":\"1\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(28, 'Testing notification', 'tethis', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7183919726159246654\"}', '2025-06-02 03:53:10', '2025-06-02 03:53:10', '{\"priority\":\"normal\",\"category\":\"general\",\"admin_id\":\"1\",\"resent\":true,\"resent_at\":\"2025-06-02 03:53:08\",\"original_id\":27}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(29, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/3535762797611928178\"}', '2025-06-02 03:57:42', '2025-06-02 03:57:42', '[]', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(30, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/444403064944735576\"}', '2025-06-02 03:58:54', '2025-06-02 03:58:54', '{\"resent\":true,\"resent_at\":\"2025-06-02 03:58:53\",\"original_id\":29}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(31, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/5726188043757073992\"}', '2025-06-02 03:59:21', '2025-06-02 03:59:21', '{\"resent\":true,\"resent_at\":\"2025-06-02 03:59:19\",\"original_id\":30}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(32, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/8076590650251937486\"}', '2025-06-02 03:59:48', '2025-06-02 03:59:48', '{\"resent\":true,\"resent_at\":\"2025-06-02 03:59:46\",\"original_id\":31}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(33, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/1180410179596314257\"}', '2025-06-02 04:01:18', '2025-06-02 04:01:18', '{\"resent\":true,\"resent_at\":\"2025-06-02 04:01:16\",\"original_id\":32}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(34, 'Test Notification', 'This is a test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/8619032366057779296\"}', '2025-06-02 04:37:09', '2025-06-02 04:37:09', '{\"resent\":true,\"resent_at\":\"2025-06-02 04:37:07\",\"original_id\":33}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(35, 'Test Notification - 06:40:05', 'This is a test notification to verify the Firebase system is working. Sent at 2025-06-02 06:40:05', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/4305608059371727512\"}', '2025-06-02 06:40:06', '2025-06-02 06:40:06', '{\"test\":true,\"debug\":true,\"timestamp\":1748824805,\"source\":\"critical_issues_fix\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(36, 'Test Notification 06:41:40', 'This is a detailed test notification sent at 2025-06-02 06:41:40', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/4128621575444125524\"}', '2025-06-02 06:41:41', '2025-06-02 06:41:41', '{\"test\":true,\"detailed_test\":true,\"timestamp\":1748824900,\"source\":\"detailed_firebase_test\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(37, 'Test Notification 06:41:53', 'This is a detailed test notification sent at 2025-06-02 06:41:53', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/1995460409015906105\"}', '2025-06-02 06:41:54', '2025-06-02 06:41:54', '{\"test\":true,\"detailed_test\":true,\"timestamp\":1748824913,\"source\":\"detailed_firebase_test\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(38, 'HIGH PRIORITY TEST', 'This is a high priority test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7657657002668308914\"}', '2025-06-02 06:41:56', '2025-06-02 06:41:56', '{\"priority\":\"high\",\"test_type\":\"high_priority\",\"timestamp\":1748824914}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(39, 'Test Notification 06:42:43', 'This is a detailed test notification sent at 2025-06-02 06:42:43', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/5227516721821092396\"}', '2025-06-02 06:42:45', '2025-06-02 06:42:45', '{\"test\":true,\"detailed_test\":true,\"timestamp\":1748824963,\"source\":\"detailed_firebase_test\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(40, 'HIGH PRIORITY TEST', 'This is a high priority test notification', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7953305526651828461\"}', '2025-06-02 06:42:46', '2025-06-02 06:42:46', '{\"priority\":\"high\",\"test_type\":\"high_priority\",\"timestamp\":1748824965}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(41, 'Test Notification 06:42:47', 'This is a detailed test notification sent at 2025-06-02 06:42:47', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/2307416684903759889\"}', '2025-06-02 06:42:49', '2025-06-02 06:42:49', '{\"test\":true,\"detailed_test\":true,\"timestamp\":1748824967,\"source\":\"detailed_firebase_test\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(42, '', '', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/2776433313487775327\"}', '2025-06-02 06:42:50', '2025-06-02 06:42:50', '{\"data_only\":\"true\",\"action\":\"test_data_notification\",\"timestamp\":1748824969,\"message\":\"This is a data-only notification\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(43, 'Test Notification 06:42:52', 'This is a detailed test notification sent at 2025-06-02 06:42:52', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7048197098672539823\"}', '2025-06-02 06:42:53', '2025-06-02 06:42:53', '{\"test\":true,\"detailed_test\":true,\"timestamp\":1748824972,\"source\":\"detailed_firebase_test\"}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(44, 'Topic Test: all', 'Testing notification delivery to topic: all', 'general', 'sent', 'all', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7921791053595949872\"}', '2025-06-02 06:42:55', '2025-06-02 06:42:55', '{\"topic_test\":true,\"target_topic\":\"all\",\"timestamp\":1748824973}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(45, 'Topic Test: android', 'Testing notification delivery to topic: android', 'general', 'sent', 'android', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/1599578533851037673\"}', '2025-06-02 06:42:56', '2025-06-02 06:42:56', '{\"topic_test\":true,\"target_topic\":\"android\",\"timestamp\":1748824975}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(46, 'Topic Test: test', 'Testing notification delivery to topic: test', 'general', 'sent', 'test', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/3670367436225453806\"}', '2025-06-02 06:42:58', '2025-06-02 06:42:58', '{\"topic_test\":true,\"target_topic\":\"test\",\"timestamp\":1748824976}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(47, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6135979132317729924\"}', '2025-06-02 06:42:59', '2025-06-02 06:42:59', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(48, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6426662929959557734\"}', '2025-06-02 06:48:27', '2025-06-02 06:48:27', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:48:26\",\"original_id\":47}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(49, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/660707254499937013\"}', '2025-06-02 06:48:54', '2025-06-02 06:48:54', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:48:52\",\"original_id\":48}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(50, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6293126115412924108\"}', '2025-06-02 06:49:29', '2025-06-02 06:49:29', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:49:27\",\"original_id\":49}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(51, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/4900066250338781082\"}', '2025-06-02 06:49:47', '2025-06-02 06:49:47', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:49:45\",\"original_id\":50}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(52, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/2907609602614202603\"}', '2025-06-02 06:51:02', '2025-06-02 06:51:02', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:51:00\",\"original_id\":51}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(53, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/5816542468418573996\"}', '2025-06-02 06:51:19', '2025-06-02 06:51:19', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:51:18\",\"original_id\":52}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(54, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/1568846276506745730\"}', '2025-06-02 06:52:28', '2025-06-02 06:52:28', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 06:52:27\",\"original_id\":53}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(55, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/2609341770602922530\"}', '2025-06-02 07:23:47', '2025-06-02 07:23:47', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 07:23:45\",\"original_id\":54}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(56, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/7766960707685228680\"}', '2025-06-02 07:44:38', '2025-06-02 07:44:38', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 07:44:37\",\"original_id\":55}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(57, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6835698849369790671\"}', '2025-06-02 07:46:41', '2025-06-02 07:46:41', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 07:46:39\",\"original_id\":56}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL),
(58, 'Topic Test: vpn_users', 'Testing notification delivery to topic: vpn_users', 'general', 'sent', 'vpn_users', 'general', 'immediate', NULL, NULL, NULL, NULL, '{\"name\":\"projects\\/smartvpn-97d16\\/messages\\/6826601321925181926\"}', '2025-06-02 08:02:15', '2025-06-02 08:02:15', '{\"topic_test\":true,\"target_topic\":\"vpn_users\",\"timestamp\":1748824978,\"resent\":true,\"resent_at\":\"2025-06-02 08:02:14\",\"original_id\":55}', 'normal', 'all', 'general', 'all_users', 0, 0, 0, NULL);

--
-- Triggers `notifications`
--
DELIMITER $$
CREATE TRIGGER `before_notification_insert` BEFORE INSERT ON `notifications` FOR EACH ROW BEGIN
    IF NEW.schedule_type IN ('scheduled', 'recurring') THEN
        SET NEW.next_run_time = NEW.scheduled_time;
        SET NEW.status = 'scheduled';
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_notification_update` BEFORE UPDATE ON `notifications` FOR EACH ROW BEGIN
    IF NEW.schedule_type IN ('scheduled', 'recurring') AND
       (OLD.scheduled_time != NEW.scheduled_time OR OLD.schedule_type != NEW.schedule_type) THEN
        SET NEW.next_run_time = NEW.scheduled_time;
        IF NEW.status = 'sent' THEN
            SET NEW.status = 'scheduled';
        END IF;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `notification_logs`
--

CREATE TABLE `notification_logs` (
  `id` int(11) NOT NULL,
  `notification_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--

CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_settings`
--

INSERT INTO `notification_settings` (`id`, `setting_key`, `setting_value`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'fcm_server_key', '', 'Firebase Cloud Messaging Server Key', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(2, 'fcm_sender_id', '', 'Firebase Cloud Messaging Sender ID', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(3, 'default_topic', 'all', 'Default topic for notifications', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(4, 'max_daily_notifications', '100', 'Maximum notifications per day', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(5, 'notification_retention_days', '30', 'Days to keep notification history', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(6, 'enable_scheduled_notifications', '1', 'Enable scheduled notification processing', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(7, 'enable_recurring_notifications', '1', 'Enable recurring notifications', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(8, 'notification_sound', 'default', 'Default notification sound', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(9, 'notification_icon', 'ic_notification', 'Default notification icon', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(10, 'notification_color', '#3b82f6', 'Default notification color', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10');

-- --------------------------------------------------------

--
-- Table structure for table `notification_templates`
--

CREATE TABLE `notification_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `category` varchar(50) DEFAULT 'general',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `target_audience` varchar(100) DEFAULT 'all_users',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_active` tinyint(1) DEFAULT 1,
  `usage_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_templates`
--

INSERT INTO `notification_templates` (`id`, `name`, `title`, `message`, `category`, `priority`, `target_audience`, `data`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES
(1, 'Welcome Message', 'Welcome to 5G Smart VPN!', 'Thank you for joining us. Enjoy secure and fast VPN connections.', 'welcome', 'normal', 'new_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(2, 'Server Maintenance', 'Scheduled Maintenance Notice', 'We will be performing scheduled maintenance on our servers. Service may be temporarily unavailable.', 'maintenance', 'high', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(3, 'New Server Added', 'New VPN Server Available', 'A new high-speed VPN server has been added to your location. Try it now!', 'update', 'normal', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(4, 'Security Alert', 'Security Update Required', 'Please update your app to the latest version for enhanced security features.', 'security', 'urgent', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `method_name` varchar(50) NOT NULL,
  `method_code` varchar(20) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `instructions` text DEFAULT NULL,
  `account_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`account_details`)),
  `is_active` tinyint(1) DEFAULT 1,
  `min_amount` decimal(10,2) DEFAULT 0.00,
  `max_amount` decimal(10,2) DEFAULT 999999.99,
  `processing_fee_percent` decimal(5,2) DEFAULT 0.00,
  `processing_fee_fixed` decimal(10,2) DEFAULT 0.00,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `method_name`, `method_code`, `display_name`, `instructions`, `account_details`, `is_active`, `min_amount`, `max_amount`, `processing_fee_percent`, `processing_fee_fixed`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'bKash', 'bkash', 'bKash Mobile Banking', 'Send money to our bKash number and provide the transaction ID', '{\"number\": \"***********\", \"type\": \"Personal\", \"name\": \"5G Smart VPN\"}', 1, '100.00', '50000.00', '0.00', '0.00', 1, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(2, 'Nagad', 'nagad', 'Nagad Mobile Banking', 'Send money to our Nagad number and provide the transaction ID', '{\"number\": \"***********\", \"type\": \"Personal\", \"name\": \"5G Smart VPN\"}', 1, '100.00', '50000.00', '0.00', '0.00', 2, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(3, 'Rocket', 'rocket', 'Rocket Mobile Banking', 'Send money to our Rocket number and provide the transaction ID', '{\"number\": \"***********\", \"type\": \"Personal\", \"name\": \"5G Smart VPN\"}', 1, '100.00', '50000.00', '0.00', '0.00', 3, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(4, 'Google Pay', 'google_pay', 'Google Pay', 'Pay using Google Pay and provide the transaction ID', '{\"email\": \"<EMAIL>\"}', 1, '100.00', '100000.00', '2.50', '0.00', 4, '2025-06-01 22:33:00', '2025-06-01 22:33:00'),
(5, 'PayPal', 'paypal', 'PayPal', 'Pay using PayPal and provide the transaction ID', '{\"email\": \"<EMAIL>\"}', 1, '500.00', '100000.00', '3.50', '0.00', 5, '2025-06-01 22:33:00', '2025-06-01 22:33:00');

-- --------------------------------------------------------

--
-- Table structure for table `servers`
--

CREATE TABLE `servers` (
  `id` int(11) NOT NULL,
  `name` text NOT NULL,
  `username` text NOT NULL,
  `password` text NOT NULL,
  `configFile` text NOT NULL,
  `flagURL` varchar(200) NOT NULL,
  `type` int(11) NOT NULL DEFAULT 1,
  `pos` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `servers`
--

INSERT INTO `servers` (`id`, `name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
(2, 'Singapore', 'muxCxgnYNPsfpSVAYGw2g2bD', 'Wduuca75VAXfMm8zfHGu9Pyk', 'client\r\ndev tun\r\nproto udp\r\nremote sg-sng.prod.surfshark.com 1194\r\nremote-random\r\nnobind\r\ntun-mtu 1500\r\nmssfix 1450\r\nping 15\r\nping-restart 0\r\nreneg-sec 0\r\n\r\nremote-cert-tls server\r\n\r\nauth-user-pass\r\n\r\n#comp-lzo\r\nverb 3\r\nfast-io\r\ncipher AES-256-CBC\r\n\r\nauth SHA512\r\n\r\n<ca>\r\n-----BEGIN CERTIFICATE-----\r\nMIIFTTCCAzWgAwIBAgIJAMs9S3fqwv+mMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV\r\nBAYTAlZHMRIwEAYDVQQKDAlTdXJmc2hhcmsxGjAYBgNVBAMMEVN1cmZzaGFyayBS\r\nb290IENBMB4XDTE4MDMxNDA4NTkyM1oXDTI4MDMxMTA4NTkyM1owPTELMAkGA1UE\r\nBhMCVkcxEjAQBgNVBAoMCVN1cmZzaGFyazEaMBgGA1UEAwwRU3VyZnNoYXJrIFJv\r\nb3QgQ0EwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDEGMNj0aisM63o\r\nSkmVJyZPaYX7aPsZtzsxo6m6p5Wta3MGASoryRsBuRaH6VVa0fwbI1nw5ubyxkua\r\nNa4v3zHVwuSq6F1p8S811+1YP1av+jqDcMyojH0ujZSHIcb/i5LtaHNXBQ3qN48C\r\nc7sqBnTIIFpmb5HthQ/4pW+a82b1guM5dZHsh7q+LKQDIGmvtMtO1+NEnmj81BAp\r\nFayiaD1ggvwDI4x7o/Y3ksfWSCHnqXGyqzSFLh8QuQrTmWUm84YHGFxoI1/8AKdI\r\nyVoB6BjcaMKtKs/pbctk6vkzmYf0XmGovDKPQF6MwUekchLjB5gSBNnptSQ9kNgn\r\nTLqi0OpSwI6ixX52Ksva6UM8P01ZIhWZ6ua/T/tArgODy5JZMW+pQ1A6L0b7egIe\r\nghpwKnPRG+5CzgO0J5UE6gv000mqbmC3CbiS8xi2xuNgruAyY2hUOoV9/BuBev8t\r\ntE5ZCsJH3YlG6NtbZ9hPc61GiBSx8NJnX5QHyCnfic/X87eST/amZsZCAOJ5v4EP\r\nSaKrItt+HrEFWZQIq4fJmHJNNbYvWzCE08AL+5/6Z+lxb/Bm3dapx2zdit3x2e+m\r\niGHekuiE8lQWD0rXD4+T+nDRi3X+kyt8Ex/8qRiUfrisrSHFzVMRungIMGdO9O/z\r\nCINFrb7wahm4PqU2f12Z9TRCOTXciQIDAQABo1AwTjAdBgNVHQ4EFgQUYRpbQwyD\r\nahLMN3F2ony3+UqOYOgwHwYDVR0jBBgwFoAUYRpbQwyDahLMN3F2ony3+UqOYOgw\r\nDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAgEAn9zV7F/XVnFNZhHFrt0Z\r\nS1Yqz+qM9CojLmiyblMFh0p7t+Hh+VKVgMwrz0LwDH4UsOosXA28eJPmech6/bjf\r\nymkoXISy/NUSTFpUChGO9RabGGxJsT4dugOw9MPaIVZffny4qYOc/rXDXDSfF2b+\r\n303lLPI43y9qoe0oyZ1vtk/UKG75FkWfFUogGNbpOkuz+et5Y0aIEiyg0yh6/l5Q\r\n5h8+yom0HZnREHhqieGbkaGKLkyu7zQ4D4tRK/mBhd8nv+09GtPEG+D5LPbabFVx\r\nKjBMP4Vp24WuSUOqcGSsURHevawPVBfgmsxf1UCjelaIwngdh6WfNCRXa5QQPQTK\r\nubQvkvXONCDdhmdXQccnRX1nJWhPYi0onffvjsWUfztRypsKzX4dvM9k7xnIcGSG\r\nEnCC4RCgt1UiZIj7frcCMssbA6vJ9naM0s7JF7N3VKeHJtqe1OCRHMYnWUZt9vrq\r\nX6IoIHlZCoLlv39wFW9QNxelcAOCVbD+19MZ0ZXt7LitjIqe7yF5WxDQN4xru087\r\nFzQ4Hfj7eH1SNLLyKZkA1eecjmRoi/OoqAt7afSnwtQLtMUc2bQDg6rHt5C0e4dC\r\nLqP/9PGZTSJiwmtRHJ/N5qYWIh9ju83APvLm/AGBTR2pXmj9G3KdVOkpIC7L35dI\r\n623cSEC3Q3UZutsEm/UplsM=\r\n-----END CERTIFICATE-----\r\n</ca>\r\n\r\nkey-direction 1\r\n\r\n<tls-auth>\r\n-----BEGIN OpenVPN Static key V1-----\r\nb02cb1d7c6fee5d4f89b8de72b51a8d0\r\nc7b282631d6fc19be1df6ebae9e2779e\r\n6d9f097058a31c97f57f0c35526a44ae\r\n09a01d1284b50b954d9246725a1ead1f\r\nf224a102ed9ab3da0152a15525643b2e\r\nee226c37041dc55539d475183b889a10\r\ne18bb94f079a4a49888da566b9978346\r\n0ece01daaf93548beea6c827d9674897\r\ne7279ff1a19cb092659e8c1860fbad0d\r\nb4ad0ad5732f1af4655dbd66214e552f\r\n04ed8fd0104e1d4bf99c249ac229ce16\r\n9d9ba22068c6c0ab742424760911d463\r\n6aafb4b85f0c952a9ce4275bc821391a\r\na65fcd0d2394f006e3fba0fd34c4bc4a\r\nb260f4b45dec3285875589c97d3087c9\r\n134d3a3aa2f904512e85aa2dc2202498\r\n-----END OpenVPN Static key V1-----\r\n</tls-auth>', 'singapore.png', 1, 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'admob_app_id', 'ca-app-pub-3940256099942544~3347511713', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(2, 'admob_banner_id', 'ca-app-pub-3940256099942544/9214589741', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(3, 'admob_interstitial_id', 'ca-app-pub-3940256099942544/1033173712', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(4, 'admob_rewarded_id', 'ca-app-pub-3940256099942544/5224354917', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(5, 'admob_native_id', 'ca-app-pub-3940256099942544/2247696110', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(6, 'banner_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(7, 'interstitial_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(8, 'rewarded_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(9, 'native_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(10, 'test_mode', '0', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(11, 'click_limit', '5', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(12, 'show_frequency', '3', '2025-05-26 20:37:45', '2025-06-01 23:18:19'),
(13, 'admob_openad_id', 'ca-app-pub-3940256099942544/9257395921', '2025-05-27 02:49:00', '2025-06-01 23:18:19'),
(14, 'openad_enabled', '1', '2025-05-27 02:49:00', '2025-06-01 23:18:19'),
(15, 'facebook_app_id', '', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(16, 'facebook_banner_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(17, 'facebook_interstitial_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(18, 'facebook_rewarded_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(19, 'facebook_native_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(20, 'banner_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(21, 'interstitial_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(22, 'rewarded_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(23, 'native_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(24, 'reward_time', '30', '2025-05-27 02:53:14', '2025-06-01 23:18:19'),
(25, 'app_name', '5G Smart VPN', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(26, 'app_version', '1.0.0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(27, 'app_description', 'Fast and secure VPN service', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(28, 'contact_email', '<EMAIL>', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(29, 'support_url', 'https://5gsmartvpn.com/support', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(30, 'privacy_policy_url', 'https://5gsmartvpn.com/privacy', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(31, 'terms_of_service_url', 'https://5gsmartvpn.com/terms', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(32, 'maintenance_mode', '0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(33, 'debug_mode', '0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(34, 'auto_connect', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(35, 'kill_switch', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(36, 'dns_leak_protection', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(37, 'connection_timeout', '30', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(38, 'max_concurrent_connections', '20', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(39, 'log_level', 'info', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(40, 'backup_frequency', 'daily', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(41, 'notification_email', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(42, 'smtp_host', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(43, 'smtp_port', '587', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(44, 'smtp_username', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(45, 'smtp_password', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(46, 'smtp_encryption', 'tls', '2025-05-27 06:25:43', '2025-05-27 06:25:43');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_ad_event` (`ad_id`,`event_type`),
  ADD KEY `idx_timestamp` (`timestamp`),
  ADD KEY `idx_session` (`session_id`);

--
-- Indexes for table `ad_packages`
--
ALTER TABLE `ad_packages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_duration` (`duration_days`);

--
-- Indexes for table `ad_tracking`
--
ALTER TABLE `ad_tracking`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_ad_type` (`ad_type`),
  ADD KEY `idx_event_type` (`event_type`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_ad_id` (`ad_id`);

--
-- Indexes for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `fk_admin_id` (`admin_id`);

--
-- Indexes for table `customer_accounts`
--
ALTER TABLE `customer_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `whatsapp_number` (`whatsapp_number`),
  ADD KEY `idx_whatsapp` (`whatsapp_number`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `customer_payments`
--
ALTER TABLE `customer_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `package_id` (`package_id`),
  ADD KEY `idx_customer` (`customer_id`),
  ADD KEY `idx_status` (`payment_status`),
  ADD KEY `idx_method` (`payment_method`),
  ADD KEY `idx_transaction` (`transaction_id`);

--
-- Indexes for table `custom_ads`
--
ALTER TABLE `custom_ads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `package_id` (`package_id`),
  ADD KEY `payment_id` (`payment_id`),
  ADD KEY `idx_provider` (`provider_id`),
  ADD KEY `idx_customer` (`customer_id`),
  ADD KEY `idx_expires` (`expires_at`),
  ADD KEY `idx_approved` (`is_approved`),
  ADD KEY `idx_url_type` (`url_type`);

--
-- Indexes for table `custom_ads_providers`
--
ALTER TABLE `custom_ads_providers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `provider_id` (`provider_id`),
  ADD UNIQUE KEY `whatsapp_number` (`whatsapp_number`),
  ADD KEY `idx_provider_id` (`provider_id`),
  ADD KEY `idx_whatsapp` (`whatsapp_number`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `custom_ads_settings`
--
ALTER TABLE `custom_ads_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`),
  ADD KEY `idx_public` (`is_public`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_schedule` (`schedule_type`,`scheduled_time`),
  ADD KEY `idx_next_run` (`next_run_time`),
  ADD KEY `idx_status_schedule` (`status`,`schedule_type`,`scheduled_time`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_target_audience` (`target_audience`);

--
-- Indexes for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notification_id` (`notification_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `notification_templates`
--
ALTER TABLE `notification_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `method_code` (`method_code`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_code` (`method_code`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=103;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ad_packages`
--
ALTER TABLE `ad_packages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `ad_tracking`
--
ALTER TABLE `ad_tracking`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `contact_messages`
--
ALTER TABLE `contact_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `customer_accounts`
--
ALTER TABLE `customer_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `customer_payments`
--
ALTER TABLE `customer_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `custom_ads`
--
ALTER TABLE `custom_ads`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `custom_ads_providers`
--
ALTER TABLE `custom_ads_providers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `custom_ads_settings`
--
ALTER TABLE `custom_ads_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `notification_logs`
--
ALTER TABLE `notification_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `notification_templates`
--
ALTER TABLE `notification_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  ADD CONSTRAINT `ad_analytics_ibfk_1` FOREIGN KEY (`ad_id`) REFERENCES `custom_ads` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD CONSTRAINT `fk_contact_messages_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `customer_payments`
--
ALTER TABLE `customer_payments`
  ADD CONSTRAINT `customer_payments_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer_accounts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_payments_ibfk_2` FOREIGN KEY (`package_id`) REFERENCES `ad_packages` (`id`);

--
-- Constraints for table `custom_ads`
--
ALTER TABLE `custom_ads`
  ADD CONSTRAINT `custom_ads_ibfk_1` FOREIGN KEY (`provider_id`) REFERENCES `custom_ads_providers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `custom_ads_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customer_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `custom_ads_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `ad_packages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `custom_ads_ibfk_4` FOREIGN KEY (`payment_id`) REFERENCES `customer_payments` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD CONSTRAINT `notification_logs_ibfk_1` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
