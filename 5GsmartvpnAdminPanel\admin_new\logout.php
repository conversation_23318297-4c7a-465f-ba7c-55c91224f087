<?php
/**
 * 5G Smart VPN Admin Panel - Logout
 */

session_start();
require_once 'includes/config.php';

// Log the logout activity if user is logged in
if (isset($_SESSION['admin_id']) && isset($_SESSION['admin_username'])) {
    // Log activity if admin_logs table exists
    $check_table = mysqli_query($conn, "SHOW TABLES LIKE 'admin_logs'");
    if (mysqli_num_rows($check_table) > 0) {
        $admin_id = $_SESSION['admin_id'];
        $admin_username = $_SESSION['admin_username'];
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $user_agent = mysqli_real_escape_string($conn, $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown');
        
        $query = "INSERT INTO admin_logs (admin_id, admin_username, action, description, ip_address, user_agent, created_at) 
                  VALUES ($admin_id, '$admin_username', 'logout', 'Admin $admin_username logged out', '$ip_address', '$user_agent', NOW())";
        
        mysqli_query($conn, $query);
    }
}

// Clear all session data
session_unset();
session_destroy();

// Redirect to login page
header('Location: login.php');
exit();
?>
