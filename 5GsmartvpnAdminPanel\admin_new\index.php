<?php
/**
 * 5G Smart VPN Admin Panel - Modern Dashboard
 * Main dashboard with analytics and overview
 * Version: 3.0 - Fixed
 */

// Start session first
session_start();

// Include unified configuration
require_once 'includes/config.php';

// Verify database connection
if (!isset($conn) || !$conn instanceof mysqli) {
    die("Database connection failed. Please check your configuration.");
}

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// Log dashboard access
$admin_username = $_SESSION['admin_username'] ?? 'admin';
$admin_id = $_SESSION['admin_id'] ?? 1;
$ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

// Check if admin_logs table exists and log the access
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'admin_logs'");
if (mysqli_num_rows($table_check) > 0) {
    $log_sql = "INSERT INTO admin_logs (admin_id, admin_username, action, description, ip_address, user_agent)
                VALUES (?, ?, 'dashboard_access', 'Accessed admin dashboard', ?, ?)";
    $stmt = $conn->prepare($log_sql);
    $stmt->bind_param("isss", $admin_id, $admin_username, $ip_address, $user_agent);
    $stmt->execute();
}

// Include functions and get dashboard stats
require_once 'includes/functions.php';

// Get comprehensive dashboard stats with error handling
try {
    $stats = getDashboardStats($conn);
} catch (Exception $e) {
    // If stats fail, use default values
    $stats = [
        'total_servers' => 0,
        'active_servers' => 0,
        'total_ads' => 0,
        'running_ads' => 0,
        'total_notifications' => 0,
        'pending_notifications' => 0,
        'total_contacts' => 0,
        'unread_contacts' => 0,
        'total_ad_views' => 0,
        'total_ad_clicks' => 0,
        'ctr' => 0,
        'recent_activities' => [],
        'servers' => []
    ];
    error_log("Dashboard stats error: " . $e->getMessage());
}

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Dashboard</h1>
                <p class="page-subtitle">Welcome to 5G Smart VPN Admin Panel</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshStats()">
                        <i class="ri-refresh-line"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-server-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_servers']; ?></h3>
                        <p class="stat-label">Total Servers</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            <?php echo $stats['active_servers']; ?> Active
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-advertisement-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_ads']; ?></h3>
                        <p class="stat-label">Custom Ads</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            <?php echo $stats['running_ads']; ?> Running
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-notification-4-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_notifications']; ?></h3>
                        <p class="stat-label">Notifications</p>
                        <span class="stat-change neutral">
                            <i class="ri-time-line"></i>
                            <?php echo $stats['pending_notifications']; ?> Pending
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-mail-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_contacts']; ?></h3>
                        <p class="stat-label">Contact Messages</p>
                        <span class="stat-change <?php echo $stats['unread_contacts'] > 0 ? 'negative' : 'neutral'; ?>">
                            <i class="ri-mail-unread-line"></i>
                            <?php echo $stats['unread_contacts']; ?> Unread
                        </span>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics -->
            <div class="dashboard-grid">
                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Activity</h3>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-ghost">
                                <i class="ri-more-2-line"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="activity-list">
                            <?php if (!empty($stats['recent_activities'])): ?>
                                <?php foreach ($stats['recent_activities'] as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="<?php echo $activity['icon']; ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="activity-text"><?php echo htmlspecialchars($activity['text']); ?></p>
                                        <span class="activity-time"><?php echo timeAgo($activity['time']); ?></span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="ri-history-line"></i>
                                    </div>
                                    <p class="empty-text">No recent activities</p>
                                    <p class="empty-subtext">Activities will appear here as you use the admin panel</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Server Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Server Status</h3>
                        <div class="card-actions">
                            <a href="servers.php" class="btn btn-sm btn-primary">
                                Manage Servers
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="server-list">
                            <?php foreach ($stats['servers'] as $server): ?>
                            <div class="server-item">
                                <div class="server-flag">
                                    <img src="../<?php echo $server['flagURL']; ?>" alt="<?php echo $server['name']; ?>">
                                </div>
                                <div class="server-info">
                                    <h4 class="server-name"><?php echo $server['name']; ?></h4>
                                    <p class="server-type">Type <?php echo $server['type']; ?></p>
                                </div>
                                <div class="server-status">
                                    <span class="status-badge <?php echo $server['status'] ? 'active' : 'inactive'; ?>">
                                        <?php echo $server['status'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Ad Performance -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Ad Performance</h3>
                        <div class="card-actions">
                            <a href="ads.php" class="btn btn-sm btn-primary">
                                Manage Ads
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="performance-metrics">
                            <div class="metric">
                                <h4 class="metric-value"><?php echo number_format($stats['total_ad_views']); ?></h4>
                                <p class="metric-label">Total Views</p>
                            </div>
                            <div class="metric">
                                <h4 class="metric-value"><?php echo number_format($stats['total_ad_clicks']); ?></h4>
                                <p class="metric-label">Total Clicks</p>
                            </div>
                            <div class="metric">
                                <h4 class="metric-value"><?php echo $stats['ctr']; ?>%</h4>
                                <p class="metric-label">CTR</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Scripts -->
<script>
function refreshStats() {
    // Add loading state
    const btn = event.target.closest('button');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="ri-loader-4-line animate-spin"></i> Refreshing...';
    btn.disabled = true;

    // Simulate refresh (you can implement actual AJAX call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

<?php include 'includes/footer.php'; ?>
