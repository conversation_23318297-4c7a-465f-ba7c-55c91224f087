<?php
/**
 * Real-time Analytics API Endpoint
 * Provides analytics data for the dashboard and mobile app
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../../config.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Verify API authentication
$timestamp = $_GET['timestamp'] ?? '';
$signature = $_GET['signature'] ?? '';

if (empty($timestamp) || empty($signature)) {
    http_response_code(401);
    echo json_encode(['error' => 'Missing authentication parameters']);
    exit;
}

$expected_signature = hash_hmac('sha256', $timestamp, $API_KEY);
if (!hash_equals($expected_signature, $signature)) {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid signature']);
    exit;
}

// Check timestamp (allow 5 minute window)
$current_time = time();
if (abs($current_time - $timestamp) > 300) {
    http_response_code(401);
    echo json_encode(['error' => 'Request timestamp expired']);
    exit;
}

try {
    // Use the global mysqli connection from config.php
    global $conn;
    
    // Get date range from parameters
    $start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
    $end_date = $_GET['end_date'] ?? date('Y-m-d');
    $action = $_GET['action'] ?? 'summary';
    
    $response = [
        'success' => true,
        'timestamp' => time(),
        'date_range' => [
            'start' => $start_date,
            'end' => $end_date
        ]
    ];
    
    switch ($action) {
        case 'summary':
            // Get overall analytics summary
            $summary = getAnalyticsSummary($db, $start_date, $end_date);
            $response['data'] = $summary;
            break;
            
        case 'daily':
            // Get daily breakdown
            $daily = getDailyAnalytics($db, $start_date, $end_date);
            $response['data'] = $daily;
            break;
            
        case 'custom_ads':
            // Get custom ads specific analytics
            $custom_ads = getCustomAdsAnalytics($db, $start_date, $end_date);
            $response['data'] = $custom_ads;
            break;
            
        case 'real_time':
            // Get real-time stats (last 24 hours)
            $real_time = getRealTimeAnalytics($db);
            $response['data'] = $real_time;
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action parameter']);
            exit;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}

function getAnalyticsSummary($db, $start_date, $end_date) {
    // Get tracking data summary
    $tracking_query = "SELECT 
        ad_type,
        event_type,
        COUNT(*) as event_count
        FROM ad_tracking 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY ad_type, event_type";
    
    $stmt = $db->prepare($tracking_query);
    $stmt->execute([$start_date, $end_date]);
    $tracking_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get custom ads data
    $custom_query = "SELECT 
        SUM(view_count) as total_views,
        SUM(click_count) as total_clicks,
        COUNT(*) as total_ads
        FROM custom_ads 
        WHERE date_start <= ? AND date_end >= ?";
    
    $custom_stmt = $db->prepare($custom_query);
    $custom_stmt->execute([$end_date, $start_date]);
    $custom_data = $custom_stmt->fetch(PDO::FETCH_ASSOC);
    
    // Process data
    $summary = [
        'total_impressions' => 0,
        'total_clicks' => 0,
        'total_revenue' => 0,
        'ctr' => 0,
        'ecpm' => 0,
        'ad_types' => [
            'custom' => ['impressions' => 0, 'clicks' => 0],
            'admob_banner' => ['impressions' => 0, 'clicks' => 0],
            'admob_interstitial' => ['impressions' => 0, 'clicks' => 0],
            'facebook' => ['impressions' => 0, 'clicks' => 0]
        ],
        'custom_ads' => [
            'total_ads' => (int)($custom_data['total_ads'] ?? 0),
            'views' => (int)($custom_data['total_views'] ?? 0),
            'clicks' => (int)($custom_data['total_clicks'] ?? 0)
        ]
    ];
    
    // Process tracking data
    foreach ($tracking_data as $track) {
        $ad_type = $track['ad_type'];
        $event_type = $track['event_type'];
        $count = (int)$track['event_count'];
        
        if ($event_type === 'view') {
            $summary['ad_types'][$ad_type]['impressions'] += $count;
            $summary['total_impressions'] += $count;
        } elseif ($event_type === 'click') {
            $summary['ad_types'][$ad_type]['clicks'] += $count;
            $summary['total_clicks'] += $count;
        }
    }
    
    // Calculate metrics
    if ($summary['total_impressions'] > 0) {
        $summary['ctr'] = round(($summary['total_clicks'] / $summary['total_impressions']) * 100, 2);
    }
    
    $summary['total_revenue'] = $summary['total_clicks'] * 0.05; // Simulate revenue
    if ($summary['total_impressions'] > 0) {
        $summary['ecpm'] = round(($summary['total_revenue'] / $summary['total_impressions']) * 1000, 2);
    }
    
    return $summary;
}

function getDailyAnalytics($db, $start_date, $end_date) {
    $daily_query = "SELECT 
        DATE(created_at) as date,
        ad_type,
        event_type,
        COUNT(*) as event_count
        FROM ad_tracking 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at), ad_type, event_type
        ORDER BY date ASC";
    
    $stmt = $db->prepare($daily_query);
    $stmt->execute([$start_date, $end_date]);
    $daily_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process daily data
    $processed_data = [];
    foreach ($daily_data as $row) {
        $date = $row['date'];
        $ad_type = $row['ad_type'];
        $event_type = $row['event_type'];
        $count = (int)$row['event_count'];
        
        if (!isset($processed_data[$date])) {
            $processed_data[$date] = [
                'date' => $date,
                'total_impressions' => 0,
                'total_clicks' => 0,
                'total_revenue' => 0,
                'ad_types' => []
            ];
        }
        
        if (!isset($processed_data[$date]['ad_types'][$ad_type])) {
            $processed_data[$date]['ad_types'][$ad_type] = [
                'impressions' => 0,
                'clicks' => 0
            ];
        }
        
        if ($event_type === 'view') {
            $processed_data[$date]['ad_types'][$ad_type]['impressions'] += $count;
            $processed_data[$date]['total_impressions'] += $count;
        } elseif ($event_type === 'click') {
            $processed_data[$date]['ad_types'][$ad_type]['clicks'] += $count;
            $processed_data[$date]['total_clicks'] += $count;
        }
        
        $processed_data[$date]['total_revenue'] = $processed_data[$date]['total_clicks'] * 0.05;
    }
    
    return array_values($processed_data);
}

function getCustomAdsAnalytics($db, $start_date, $end_date) {
    $custom_query = "SELECT 
        id,
        title,
        view_count,
        click_count,
        date_start,
        date_end,
        `on` as active
        FROM custom_ads 
        WHERE date_start <= ? AND date_end >= ?
        ORDER BY view_count DESC";
    
    $stmt = $db->prepare($custom_query);
    $stmt->execute([$end_date, $start_date]);
    $custom_ads = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get tracking data for custom ads
    $tracking_query = "SELECT 
        ad_id,
        event_type,
        COUNT(*) as event_count
        FROM ad_tracking 
        WHERE ad_type = 'custom' AND DATE(created_at) BETWEEN ? AND ?
        GROUP BY ad_id, event_type";
    
    $tracking_stmt = $db->prepare($tracking_query);
    $tracking_stmt->execute([$start_date, $end_date]);
    $tracking_data = $tracking_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Merge tracking data with custom ads
    $tracking_by_id = [];
    foreach ($tracking_data as $track) {
        $ad_id = $track['ad_id'];
        $event_type = $track['event_type'];
        $count = (int)$track['event_count'];
        
        if (!isset($tracking_by_id[$ad_id])) {
            $tracking_by_id[$ad_id] = ['views' => 0, 'clicks' => 0];
        }
        
        if ($event_type === 'view') {
            $tracking_by_id[$ad_id]['views'] += $count;
        } elseif ($event_type === 'click') {
            $tracking_by_id[$ad_id]['clicks'] += $count;
        }
    }
    
    // Add tracking data to custom ads
    foreach ($custom_ads as &$ad) {
        $ad_id = $ad['id'];
        $ad['tracking_views'] = $tracking_by_id[$ad_id]['views'] ?? 0;
        $ad['tracking_clicks'] = $tracking_by_id[$ad_id]['clicks'] ?? 0;
        $ad['total_views'] = (int)$ad['view_count'] + $ad['tracking_views'];
        $ad['total_clicks'] = (int)$ad['click_count'] + $ad['tracking_clicks'];
        
        if ($ad['total_views'] > 0) {
            $ad['ctr'] = round(($ad['total_clicks'] / $ad['total_views']) * 100, 2);
        } else {
            $ad['ctr'] = 0;
        }
    }
    
    return $custom_ads;
}

function getRealTimeAnalytics($db) {
    // Get last 24 hours data
    $real_time_query = "SELECT 
        ad_type,
        event_type,
        COUNT(*) as event_count,
        HOUR(created_at) as hour
        FROM ad_tracking 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY ad_type, event_type, HOUR(created_at)
        ORDER BY hour ASC";
    
    $stmt = $db->prepare($real_time_query);
    $stmt->execute();
    $real_time_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process hourly data
    $hourly_stats = [];
    for ($i = 0; $i < 24; $i++) {
        $hourly_stats[$i] = [
            'hour' => $i,
            'impressions' => 0,
            'clicks' => 0
        ];
    }
    
    foreach ($real_time_data as $row) {
        $hour = (int)$row['hour'];
        $event_type = $row['event_type'];
        $count = (int)$row['event_count'];
        
        if ($event_type === 'view') {
            $hourly_stats[$hour]['impressions'] += $count;
        } elseif ($event_type === 'click') {
            $hourly_stats[$hour]['clicks'] += $count;
        }
    }
    
    return [
        'hourly_stats' => array_values($hourly_stats),
        'last_updated' => date('Y-m-d H:i:s')
    ];
}
?>
