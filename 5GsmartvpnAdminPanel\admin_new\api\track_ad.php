<?php
/**
 * 5G Smart VPN Admin Panel - Modern Ad Tracking API
 * Tracks ad views and clicks for analytics
 */

require_once '../includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'timestamp' => time(),
    'api_version' => '3.0',
    'source' => 'modern_admin_panel',
    'endpoint' => 'track_ad'
];

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }

    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);

    // Fallback to $_POST if JSON decode fails
    if ($input === null) {
        $input = $_POST;
    }

    $adId = isset($input['ad_id']) ? (int)$input['ad_id'] : 0;
    $type = isset($input['type']) ? $input['type'] : 'view'; // 'view' or 'click'
    $adType = isset($input['ad_type']) ? $input['ad_type'] : 'custom'; // 'custom', 'admob', 'facebook'
    $userId = isset($input['user_id']) ? $input['user_id'] : null;
    $deviceId = isset($input['device_id']) ? $input['device_id'] : null;

    if ($adId <= 0) {
        throw new Exception("Invalid ad ID");
    }

    // Validate type parameter
    if (!in_array($type, ['view', 'click'])) {
        throw new Exception("Invalid tracking type. Must be 'view' or 'click'");
    }

    // Validate ad type parameter
    if (!in_array($adType, ['custom', 'admob', 'facebook'])) {
        throw new Exception("Invalid ad type. Must be 'custom', 'admob', or 'facebook'");
    }

    // Track custom ads (existing functionality)
    if ($adType === 'custom') {
        $column = $type . '_count';
        $stmt = $conn->prepare("UPDATE custom_ads SET $column = $column + 1 WHERE id = ?");
        $stmt->bind_param("i", $adId);
        $affected = $stmt->execute() ? $stmt->affected_rows : 0;

        if ($affected === 0) {
            throw new Exception("Custom ad not found or update failed");
        }

        $response['message'] = "Custom ad {$type} count updated successfully";
    }
    // Track AdMob/Facebook ads (new functionality)
    else {
        // Create ad tracking record
        try {
            $stmt = $conn->prepare("INSERT INTO ad_tracking (ad_id, ad_type, event_type, user_id, device_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("issss", $adId, $adType, $type, $userId, $deviceId);
            $stmt->execute();
        } catch (Exception $e) {
            // If table doesn't exist, create it
            $createTable = "CREATE TABLE IF NOT EXISTS ad_tracking (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ad_id INT NOT NULL,
                ad_type VARCHAR(20) NOT NULL,
                event_type VARCHAR(10) NOT NULL,
                user_id VARCHAR(100),
                device_id VARCHAR(100),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_ad_type (ad_type),
                INDEX idx_event_type (event_type),
                INDEX idx_created_at (created_at)
            )";

            mysqli_query($conn, $createTable);

            // Try insert again
            $stmt = $conn->prepare("INSERT INTO ad_tracking (ad_id, ad_type, event_type, user_id, device_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("issss", $adId, $adType, $type, $userId, $deviceId);
            $stmt->execute();
        }

        $response['message'] = "{$adType} ad {$type} tracked successfully";
    }

    $response['success'] = true;
    $response['data'] = [
        'ad_id' => $adId,
        'ad_type' => $adType,
        'event_type' => $type,
        'user_id' => $userId,
        'device_id' => $deviceId
    ];

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['code'] = 400;
    error_log("Ad Tracking Error: " . $e->getMessage());
}

// Output JSON response
echo json_encode($response);
?>
