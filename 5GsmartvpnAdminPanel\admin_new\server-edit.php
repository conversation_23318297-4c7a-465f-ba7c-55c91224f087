<?php
/**
 * 5G Smart VPN Admin Panel - Edit Server
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Edit Server';
$success_message = '';
$error_message = '';
$server = null;

// Get server ID
$server_id = (int)($_GET['id'] ?? 0);
if ($server_id <= 0) {
    header('Location: servers.php');
    exit();
}

// Fetch server data
$query = "SELECT * FROM servers WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, 'i', $server_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $server = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
}

if (!$server) {
    header('Location: servers.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update'])) {
    $name = trim($_POST['name'] ?? '');
    $username = trim($_POST['user'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $flagURL = trim($_POST['flag'] ?? '');
    $configFile = mysqli_real_escape_string($conn, trim($_POST['config'] ?? ''));
    $type = (int)($_POST['type'] ?? 1);

    // Validation
    $errors = [];
    if (empty($name)) $errors[] = 'Country name is required';
    if (empty($username)) $errors[] = 'Username is required';
    if (empty($password)) $errors[] = 'Password is required';
    if (empty($configFile)) $errors[] = 'Config file is required';

    if (empty($errors)) {
        // Update server using the original structure
        $update = "UPDATE `servers` SET `name`='$name', `username`='$username', `password`='$password',
                   `configFile`='$configFile', `flagURL`='$flagURL', `type`='$type' WHERE `id`='$server_id'";

        $query = mysqli_query($conn, $update);
        if ($query) {
            $success_message = 'Server updated successfully!';
            // Refresh server data
            $server['name'] = $name;
            $server['username'] = $username;
            $server['password'] = $password;
            $server['configFile'] = $configFile;
            $server['flagURL'] = $flagURL;
            $server['type'] = $type;
        } else {
            $error_message = 'Failed to update server. ' . mysqli_error($conn);
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Edit Server</h1>
                <p class="page-subtitle">Modify server configuration for <?php echo htmlspecialchars($server['name']); ?></p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="servers.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        <span class="hide-mobile">Back to Servers</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="ri-check-circle-line"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="ri-error-warning-line"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Server Configuration</h3>
                    <p class="card-subtitle">Update the server details below</p>
                </div>
                <div class="card-body">
                    <form method="POST" class="server-form">
                        <div class="form-grid">
                            <!-- Basic Information -->
                            <div class="form-section">
                                <h4 class="section-title">Basic Information</h4>

                                <div class="form-group">
                                    <label for="name" class="form-label">Country Name *</label>
                                    <input type="text" id="name" name="name" class="form-control"
                                           value="<?php echo htmlspecialchars($server['name']); ?>"
                                           placeholder="e.g., United States" required>
                                </div>

                                <div class="form-group">
                                    <label for="user" class="form-label">User Name *</label>
                                    <input type="text" id="user" name="user" class="form-control"
                                           value="<?php echo htmlspecialchars($server['username']); ?>"
                                           placeholder="VPN username" required>
                                </div>

                                <div class="form-group">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="text" id="password" name="password" class="form-control"
                                           value="<?php echo htmlspecialchars($server['password']); ?>"
                                           placeholder="VPN password" required>
                                </div>

                                <div class="form-group">
                                    <label for="flag" class="form-label">Flag URL</label>
                                    <input type="text" id="flag" name="flag" class="form-control"
                                           value="<?php echo htmlspecialchars($server['flagURL']); ?>"
                                           placeholder="e.g., flag/us.png">
                                    <?php if ($server['flagURL']): ?>
                                    <div class="current-flag" style="margin-top: 0.5rem;">
                                        <img src="../<?php echo $server['flagURL']; ?>"
                                             alt="Current flag"
                                             style="width: 40px; height: 30px; border-radius: 4px; border: 1px solid var(--gray-200);">
                                        <span style="margin-left: 0.5rem; font-size: 0.75rem; color: var(--gray-600);">Current flag</span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Configuration -->
                            <div class="form-section">
                                <h4 class="section-title">Configuration</h4>

                                <div class="form-group">
                                    <label for="config" class="form-label">Config File *</label>
                                    <textarea id="config" name="config" class="form-control" rows="8"
                                              placeholder="Paste your VPN configuration here..." required><?php echo htmlspecialchars($server['configFile']); ?></textarea>
                                    <small class="form-text">Paste the complete VPN configuration file content</small>
                                </div>

                                <div class="form-group">
                                    <label for="type" class="form-label">Server Type *</label>
                                    <select id="type" name="type" class="form-control form-select">
                                        <option value="1" <?php echo $server['type'] == 1 ? 'selected' : ''; ?>>Free Server</option>
                                        <option value="0" <?php echo $server['type'] == 0 ? 'selected' : ''; ?>>Premium Server</option>
                                    </select>
                                    <small class="form-text">Choose whether this server is free or premium for users</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" name="update" class="btn btn-primary">
                                <i class="ri-save-line"></i>
                                Update Server
                            </button>
                            <a href="servers.php" class="btn btn-secondary">
                                <i class="ri-close-line"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
// Form validation
document.querySelector('.server-form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields.');
    }
});

// Auto-resize textarea
document.getElementById('config').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>

<?php include 'includes/footer.php'; ?>
