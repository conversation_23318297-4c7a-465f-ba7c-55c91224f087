-- Initial Data for Enhanced Custom Ads System
-- Version: 1.0
-- Created: 2025-06-02

-- Insert default ad packages
INSERT INTO ad_packages (package_name, duration_days, price, currency, max_ads, features, is_active, display_order) VALUES
('Basic Package', 7, 500.00, 'BDT', 1, '{"priority": 1, "analytics": true, "support": "basic"}', TRUE, 1),
('Standard Package', 30, 1800.00, 'BDT', 3, '{"priority": 2, "analytics": true, "support": "standard", "targeting": true}', TRUE, 2),
('Premium Package', 90, 4500.00, 'BDT', 5, '{"priority": 3, "analytics": true, "support": "premium", "targeting": true, "featured": true}', TRUE, 3),
('Enterprise Package', 365, 15000.00, 'BDT', 10, '{"priority": 4, "analytics": true, "support": "enterprise", "targeting": true, "featured": true, "dedicated_support": true}', TRUE, 4);

-- Insert payment methods
INSERT INTO payment_methods (method_name, method_code, display_name, instructions, account_details, is_active, min_amount, max_amount, processing_fee_percent, processing_fee_fixed, display_order) VALUES
('bKash', 'bkash', 'bKash Mobile Banking', 'Send money to our bKash number and provide the transaction ID', '{"number": "***********", "type": "Personal", "name": "5G Smart VPN"}', TRUE, 100.00, 50000.00, 0.00, 0.00, 1),
('Nagad', 'nagad', 'Nagad Mobile Banking', 'Send money to our Nagad number and provide the transaction ID', '{"number": "***********", "type": "Personal", "name": "5G Smart VPN"}', TRUE, 100.00, 50000.00, 0.00, 0.00, 2),
('Rocket', 'rocket', 'Rocket Mobile Banking', 'Send money to our Rocket number and provide the transaction ID', '{"number": "***********", "type": "Personal", "name": "5G Smart VPN"}', TRUE, 100.00, 50000.00, 0.00, 0.00, 3),
('Google Pay', 'google_pay', 'Google Pay', 'Pay using Google Pay and provide the transaction ID', '{"email": "<EMAIL>"}', TRUE, 100.00, 100000.00, 2.50, 0.00, 4),
('PayPal', 'paypal', 'PayPal', 'Pay using PayPal and provide the transaction ID', '{"email": "<EMAIL>"}', TRUE, 500.00, 100000.00, 3.50, 0.00, 5);

-- Insert custom ads system settings
INSERT INTO custom_ads_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('system_enabled', 'true', 'boolean', 'Enable/disable the custom ads system', FALSE),
('auto_approve_ads', 'false', 'boolean', 'Automatically approve ads after payment verification', FALSE),
('max_ads_per_customer', '10', 'number', 'Maximum number of ads per customer', FALSE),
('default_ad_duration', '30', 'number', 'Default ad duration in days', FALSE),
('payment_verification_required', 'true', 'boolean', 'Require admin verification for payments', FALSE),
('whatsapp_verification_enabled', 'true', 'boolean', 'Enable WhatsApp number verification', FALSE),
('admin_notification_email', '<EMAIL>', 'string', 'Email for admin notifications', FALSE),
('customer_support_whatsapp', '***********', 'string', 'WhatsApp number for customer support', TRUE),
('terms_and_conditions_url', 'https://5gsmartvpn.com/terms', 'string', 'Terms and conditions URL', TRUE),
('privacy_policy_url', 'https://5gsmartvpn.com/privacy', 'string', 'Privacy policy URL', TRUE),
('minimum_ad_duration', '7', 'number', 'Minimum ad duration in days', FALSE),
('maximum_ad_duration', '365', 'number', 'Maximum ad duration in days', FALSE),
('ad_approval_timeout_hours', '24', 'number', 'Hours to approve ads after payment', FALSE),
('refund_policy_days', '3', 'number', 'Days for refund policy', TRUE),
('featured_ad_multiplier', '2.0', 'number', 'Price multiplier for featured ads', FALSE);

-- Insert sample provider (for testing)
INSERT INTO custom_ads_providers (provider_id, whatsapp_number, business_name, contact_person, email, status, password_hash, is_verified) VALUES
('PROV001', '***********', 'Sample Business Ltd', 'John Doe', '<EMAIL>', 'active', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE);

-- Insert sample customer account (for testing)
INSERT INTO customer_accounts (whatsapp_number, customer_name, email, status, is_verified) VALUES
('***********', 'Jane Smith', '<EMAIL>', 'active', TRUE);

-- Update existing custom_ads table with new fields (sample data)
UPDATE custom_ads SET 
    url_type = CASE 
        WHEN url LIKE '%play.google.com%' THEN 'playstore'
        WHEN url LIKE '%apps.apple.com%' THEN 'appstore'
        ELSE 'website'
    END,
    button_text = CASE 
        WHEN url LIKE '%play.google.com%' THEN 'Install'
        WHEN url LIKE '%apps.apple.com%' THEN 'Install'
        ELSE 'Visit'
    END,
    expires_at = DATE_ADD(NOW(), INTERVAL 30 DAY),
    is_approved = TRUE,
    priority = 1
WHERE id > 0;
