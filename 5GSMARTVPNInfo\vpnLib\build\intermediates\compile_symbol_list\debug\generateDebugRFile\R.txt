int array auth_retry_type 0x0
int array crm_entries 0x0
int array crm_values 0x0
int array tls_directions_entries 0x0
int array tls_directions_values 0x0
int array vpn_types 0x0
int attr certificate 0x0
int attr fileTitle 0x0
int attr pstsDividerColor 0x0
int attr pstsDividerPadding 0x0
int attr pstsDividerWidth 0x0
int attr pstsIndicatorColor 0x0
int attr pstsIndicatorHeight 0x0
int attr pstsPaddingMiddle 0x0
int attr pstsScrollOffset 0x0
int attr pstsShouldExpand 0x0
int attr pstsTabBackground 0x0
int attr pstsTabPaddingLeftRight 0x0
int attr pstsTextAllCaps 0x0
int attr pstsTextAlpha 0x0
int attr pstsTextSelectedAlpha 0x0
int attr pstsTextSelectedStyle 0x0
int attr pstsTextStyle 0x0
int attr pstsUnderlineColor 0x0
int attr pstsUnderlineHeight 0x0
int attr showClear 0x0
int bool logSildersAlwaysVisible 0x0
int bool supportFileScheme 0x0
int color accent 0x0
int color background_tab_pressed 0x0
int color dataIn 0x0
int color dataOut 0x0
int color gelb 0x0
int color primary 0x0
int color primary_dark 0x0
int color rot 0x0
int color switchbar 0x0
int color tab_text 0x0
int dimen add_button_margin 0x0
int dimen add_button_margin_topfab 0x0
int dimen diameter 0x0
int dimen elevation_high 0x0
int dimen elevation_low 0x0
int dimen paddingItemsSidebarLog 0x0
int dimen round_button_diameter 0x0
int dimen stdpadding 0x0
int dimen switchbar_pad 0x0
int dimen vpn_setting_padding 0x0
int drawable ic_menu_archive 0x0
int drawable ic_menu_close_clear_cancel 0x0
int drawable ic_menu_copy_holo_light 0x0
int drawable ic_menu_log 0x0
int drawable ic_menu_pause 0x0
int drawable ic_menu_play 0x0
int drawable ic_notification 0x0
int drawable ic_quick 0x0
int drawable ic_stat_vpn 0x0
int drawable ic_stat_vpn_empty_halo 0x0
int drawable ic_stat_vpn_offline 0x0
int drawable ic_stat_vpn_outline 0x0
int drawable vpn_item_settings 0x0
int id as_servername 0x0
int id check 0x0
int id icon 0x0
int id password 0x0
int id prompt 0x0
int id request_autologin 0x0
int id save_password 0x0
int id show_password 0x0
int id username 0x0
int id warning 0x0
int layout api_confirm 0x0
int layout import_as_config 0x0
int layout launchvpn 0x0
int layout userpass 0x0
int mipmap banner_tv 0x0
int mipmap ic_launcher 0x0
int plurals days_left 0x0
int plurals hours_left 0x0
int plurals minutes_left 0x0
int plurals months_left 0x0
int string Search 0x0
int string Use_no_proxy 0x0
int string ab_kitkat_mss 0x0
int string ab_kitkat_mss_title 0x0
int string ab_kitkat_reconnect 0x0
int string ab_kitkat_reconnect_title 0x0
int string ab_lollipop_reinstall 0x0
int string ab_lollipop_reinstall_title 0x0
int string ab_not_route_to_vpn 0x0
int string ab_not_route_to_vpn_title 0x0
int string ab_only_cidr 0x0
int string ab_only_cidr_title 0x0
int string ab_persist_tun 0x0
int string ab_persist_tun_title 0x0
int string ab_proxy 0x0
int string ab_proxy_title 0x0
int string ab_secondary_users 0x0
int string ab_secondary_users_title 0x0
int string ab_tethering_44 0x0
int string ab_vpn_reachability_44 0x0
int string ab_vpn_reachability_44_title 0x0
int string abi_mismatch 0x0
int string about 0x0
int string add 0x0
int string add_new_vpn_hint 0x0
int string add_profile 0x0
int string add_profile_name_prompt 0x0
int string add_remote 0x0
int string address 0x0
int string advanced 0x0
int string advanced_settings 0x0
int string all_app_prompt 0x0
int string allow_vpn_changes 0x0
int string allowed_apps 0x0
int string allowed_vpn_apps_info 0x0
int string app 0x0
int string app_no_longer_exists 0x0
int string appbehaviour 0x0
int string apprest_name 0x0
int string apprest_name_desc 0x0
int string apprest_ovpn 0x0
int string apprest_ovpn_desc 0x0
int string apprest_uuid 0x0
int string apprest_uuid_desc 0x0
int string apprest_ver 0x0
int string apprest_vpnconf 0x0
int string apprest_vpnlist 0x0
int string as_servername 0x0
int string auth_dialog_message 0x0
int string auth_dialog_title 0x0
int string auth_failed_behaviour 0x0
int string auth_pwquery 0x0
int string auth_username 0x0
int string avghour 0x0
int string avgmin 0x0
int string backup_dns 0x0
int string basic 0x0
int string baterry_consumption 0x0
int string battery_consumption_title 0x0
int string bits_per_second 0x0
int string blocklocal_summary 0x0
int string blocklocal_title 0x0
int string bouncy_castle 0x0
int string broken_image_cert 0x0
int string broken_image_cert_title 0x0
int string broken_images 0x0
int string broken_images_faq 0x0
int string building_configration 0x0
int string built_by 0x0
int string ca_title 0x0
int string cancel 0x0
int string cancel_connection 0x0
int string cancel_connection_long 0x0
int string cancel_connection_query 0x0
int string cannotparsecert 0x0
int string cant_read_folder 0x0
int string change_sorting 0x0
int string channel_description_background 0x0
int string channel_description_status 0x0
int string channel_description_userreq 0x0
int string channel_name_background 0x0
int string channel_name_status 0x0
int string channel_name_userreq 0x0
int string check_remote_tlscert 0x0
int string check_remote_tlscert_title 0x0
int string chipher_dialog_message 0x0
int string cipher_dialog_title 0x0
int string clear 0x0
int string clear_external_apps 0x0
int string clear_log 0x0
int string clear_log_on_connect 0x0
int string clearappsdialog 0x0
int string client_behaviour 0x0
int string client_certificate_title 0x0
int string client_key_title 0x0
int string client_no_certificate 0x0
int string client_pkcs12_title 0x0
int string complete_dn 0x0
int string config_error_found 0x0
int string configuration_changed 0x0
int string configure 0x0
int string configure_the_vpn 0x0
int string connect_timeout 0x0
int string connection_retries 0x0
int string connectretrymaxmessage 0x0
int string connectretrymaxtitle 0x0
int string connectretrymessage 0x0
int string connectretrywait 0x0
int string converted_profile 0x0
int string converted_profile_i 0x0
int string copied_entry 0x0
int string copy_of_profile 0x0
int string copying_log_entries 0x0
int string copyright_blinktgui 0x0
int string copyright_bouncycastle 0x0
int string copyright_file_dialog 0x0
int string copyright_guicode 0x0
int string copyright_logo 0x0
int string copyright_openssl 0x0
int string copyright_others 0x0
int string crash_toast_text 0x0
int string crashdump 0x0
int string crl_file 0x0
int string crl_title 0x0
int string crtext_requested 0x0
int string custom_config_summary 0x0
int string custom_config_title 0x0
int string custom_connection_options 0x0
int string custom_connection_options_warng 0x0
int string custom_option_warning 0x0
int string custom_options_title 0x0
int string custom_route_format_error 0x0
int string custom_route_message 0x0
int string custom_route_message_excluded 0x0
int string custom_routes_title 0x0
int string custom_routes_title_excluded 0x0
int string data_in 0x0
int string data_out 0x0
int string debug_build 0x0
int string default_cipherlist_test 0x0
int string default_route_summary 0x0
int string defaultport 0x0
int string defaultserver 0x0
int string defaultvpn 0x0
int string defaultvpnsummary 0x0
int string delete 0x0
int string deprecated_tls_remote 0x0
int string device_specific 0x0
int string disallowed_vpn_apps_info 0x0
int string dns 0x0
int string dns1_summary 0x0
int string dns_add_error 0x0
int string dns_override_summary 0x0
int string dns_server 0x0
int string dns_server_info 0x0
int string donatePlayStore 0x0
int string downloaded_data 0x0
int string duplicate_profile_name 0x0
int string duplicate_profile_title 0x0
int string duplicate_vpn 0x0
int string edit_profile_title 0x0
int string edit_vpn 0x0
int string enabled_connection_entry 0x0
int string enableproxyauth 0x0
int string encryption 0x0
int string encryption_cipher 0x0
int string enter_tlscn_dialog 0x0
int string enter_tlscn_title 0x0
int string error 0x0
int string error_extapp_sign 0x0
int string error_importing_file 0x0
int string error_orbot_and_proxy_options 0x0
int string error_reading_config_file 0x0
int string error_rsa_sign 0x0
int string export_config_chooser_title 0x0
int string export_config_title 0x0
int string extauth_not_configured 0x0
int string external_authenticator 0x0
int string extracahint 0x0
int string faq 0x0
int string faq_android_clients 0x0
int string faq_androids_clients_title 0x0
int string faq_copying 0x0
int string faq_duplicate_notification 0x0
int string faq_duplicate_notification_title 0x0
int string faq_hint 0x0
int string faq_howto 0x0
int string faq_howto_shortcut 0x0
int string faq_howto_title 0x0
int string faq_killswitch 0x0
int string faq_killswitch_title 0x0
int string faq_remote_api 0x0
int string faq_remote_api_title 0x0
int string faq_routing 0x0
int string faq_routing_title 0x0
int string faq_security 0x0
int string faq_security_title 0x0
int string faq_shortcut 0x0
int string faq_system_dialog_xposed 0x0
int string faq_system_dialogs 0x0
int string faq_system_dialogs_title 0x0
int string faq_tap_mode 0x0
int string faq_tethering 0x0
int string faq_vpndialog43 0x0
int string faq_vpndialog43_title 0x0
int string file_dialog 0x0
int string file_explorer_tab 0x0
int string file_icon 0x0
int string file_nothing_selected 0x0
int string file_select 0x0
int string files_missing_hint 0x0
int string float_summary 0x0
int string float_title 0x0
int string full_licenses 0x0
int string gbits_per_second 0x0
int string generalsettings 0x0
int string generated_config 0x0
int string generated_config_summary 0x0
int string getproxy_error 0x0
int string graph 0x0
int string help_translate 0x0
int string hwkeychain 0x0
int string ics_openvpn_log_file 0x0
int string ignore 0x0
int string ignore_multicast_route 0x0
int string ignore_routes_summary 0x0
int string ignored_pushed_routes 0x0
int string import_config 0x0
int string import_config_error 0x0
int string import_configuration_file 0x0
int string import_content_resolve_error 0x0
int string import_could_not_open 0x0
int string import_done 0x0
int string import_error_message 0x0
int string import_from_as 0x0
int string import_log 0x0
int string import_vpn 0x0
int string import_warning_custom_options 0x0
int string imported_from_file 0x0
int string importing_config 0x0
int string importpkcs12fromconfig 0x0
int string info_from_server 0x0
int string inline_file_data 0x0
int string inline_file_tab 0x0
int string install_keychain 0x0
int string internal_web_view 0x0
int string ip_add_error 0x0
int string ip_looks_like_subnet 0x0
int string ip_not_cidr 0x0
int string ipdns 0x0
int string ipv4 0x0
int string ipv4_address 0x0
int string ipv4_dialog_title 0x0
int string ipv4_format_error 0x0
int string ipv6 0x0
int string ipv6_address 0x0
int string ipv6_dialog_tile 0x0
int string jelly_keystore_alphanumeric_bug 0x0
int string kbits_per_second 0x0
int string keep 0x0
int string keyChainAccessError 0x0
int string keychain_access 0x0
int string keychain_nocacert 0x0
int string last5minutes 0x0
int string last_openvpn_tun_config 0x0
int string lastdumpdate 0x0
int string loading 0x0
int string local_ip_info 0x0
int string location 0x0
int string logCleared 0x0
int string log_no_last_vpn 0x0
int string log_verbosity_level 0x0
int string logview_options 0x0
int string lzo 0x0
int string lzo_copyright 0x0
int string make_selection_inline 0x0
int string management_socket_closed 0x0
int string mbits_per_second 0x0
int string menu_add_profile 0x0
int string menu_import 0x0
int string menu_import_short 0x0
int string menu_use_inline_data 0x0
int string message_no_user_edit 0x0
int string minidump_generated 0x0
int string missing_ca_certificate 0x0
int string missing_certificates 0x0
int string missing_tlsauth 0x0
int string mobile_info 0x0
int string mssfix_checkbox 0x0
int string mssfix_dialogtitle 0x0
int string mssfix_invalid_value 0x0
int string mssfix_value_dialog 0x0
int string mtu_invalid_value 0x0
int string netchange 0x0
int string netchange_summary 0x0
int string netstatus 0x0
int string no_allowed_app 0x0
int string no_bind 0x0
int string no_ca_cert_selected 0x0
int string no_certificate 0x0
int string no_data 0x0
int string no_default_vpn_set 0x0
int string no_error_found 0x0
int string no_external_app_allowed 0x0
int string no_keystore_cert_selected 0x0
int string no_orbotfound 0x0
int string no_remote_defined 0x0
int string no_vpn_profiles_defined 0x0
int string no_vpn_support_image 0x0
int string nobind_summary 0x0
int string notenoughdata 0x0
int string notifcation_title 0x0
int string notifcation_title_notconnect 0x0
int string nought_alwayson_warning 0x0
int string novpn_selected 0x0
int string obscure 0x0
int string official_build 0x0
int string onbootrestart 0x0
int string onbootrestartsummary 0x0
int string openssl 0x0
int string openssl_cipher_name 0x0
int string openssl_error 0x0
int string opentun_no_ipaddr 0x0
int string openurl_requested 0x0
int string openvpn 0x0
int string openvpn3_nostatickeys 0x0
int string openvpn3_pkcs12 0x0
int string openvpn3_socksproxy 0x0
int string openvpn_is_no_free_vpn 0x0
int string openvpn_log 0x0
int string opevpn_copyright 0x0
int string osslspeedtest 0x0
int string override_dns 0x0
int string owner_fix 0x0
int string owner_fix_summary 0x0
int string packet_auth 0x0
int string password 0x0
int string pauseVPN 0x0
int string payload_options 0x0
int string permission_description 0x0
int string permission_icon_app 0x0
int string permission_revoked 0x0
int string persistent_tun_title 0x0
int string persisttun_summary 0x0
int string pkcs12_file_encryption_key 0x0
int string pkcs12pwquery 0x0
int string port 0x0
int string private_key_password 0x0
int string profilename 0x0
int string prompt 0x0
int string protocol 0x0
int string proxy 0x0
int string pull_off_summary 0x0
int string pull_on_summary 0x0
int string pushpeerinfo 0x0
int string pushpeerinfosummary 0x0
int string pw_query_hint 0x0
int string pw_request_dialog_prompt 0x0
int string pw_request_dialog_title 0x0
int string qs_connect 0x0
int string qs_disconnect 0x0
int string qs_title 0x0
int string query_delete_remote 0x0
int string query_permissions_sdcard 0x0
int string random_host_prefix 0x0
int string random_host_summary 0x0
int string rdn 0x0
int string rdn_prefix 0x0
int string reconnect 0x0
int string reconnection_settings 0x0
int string remote_no_server_selected 0x0
int string remote_random 0x0
int string remote_tlscn_check_summary 0x0
int string remote_tlscn_check_title 0x0
int string remote_trust 0x0
int string remote_warning 0x0
int string remotetlsnote 0x0
int string remove_connection_entry 0x0
int string remove_vpn 0x0
int string remove_vpn_query 0x0
int string request_autologin 0x0
int string reread_log 0x0
int string restart 0x0
int string restart_vpn_after_change 0x0
int string resumevpn 0x0
int string route_not_cidr 0x0
int string route_not_netip 0x0
int string route_rejected 0x0
int string routes_debug 0x0
int string routes_info_excl 0x0
int string routes_info_incl 0x0
int string routing 0x0
int string running_test 0x0
int string samsung_broken 0x0
int string samsung_broken_title 0x0
int string save_password 0x0
int string screen_nopersistenttun 0x0
int string screenoff_pause 0x0
int string screenoff_summary 0x0
int string screenoff_title 0x0
int string searchdomain 0x0
int string secondary_dns_message 0x0
int string select 0x0
int string select_file 0x0
int string send 0x0
int string send_config 0x0
int string send_logfile 0x0
int string send_minidump 0x0
int string send_minidump_summary 0x0
int string server_list 0x0
int string service_restarted 0x0
int string session_ipv4string 0x0
int string session_ipv6string 0x0
int string setting_loadtun 0x0
int string setting_loadtun_summary 0x0
int string settings_auth 0x0
int string shortcut_profile_notfound 0x0
int string show_log 0x0
int string show_log_summary 0x0
int string show_log_window 0x0
int string show_password 0x0
int string sort 0x0
int string sorted_az 0x0
int string sorted_lru 0x0
int string speed_waiting 0x0
int string start_vpn_ticker 0x0
int string start_vpn_title 0x0
int string state_add_routes 0x0
int string state_assign_ip 0x0
int string state_auth 0x0
int string state_auth_failed 0x0
int string state_auth_pending 0x0
int string state_connected 0x0
int string state_connecting 0x0
int string state_disconnected 0x0
int string state_exiting 0x0
int string state_get_config 0x0
int string state_nonetwork 0x0
int string state_noprocess 0x0
int string state_reconnecting 0x0
int string state_resolve 0x0
int string state_screenoff 0x0
int string state_tcp_connect 0x0
int string state_user_vpn_password 0x0
int string state_user_vpn_password_cancelled 0x0
int string state_user_vpn_permission 0x0
int string state_user_vpn_permission_cancelled 0x0
int string state_userpause 0x0
int string state_wait 0x0
int string state_waitconnectretry 0x0
int string state_waitorbot 0x0
int string static_keys_info 0x0
int string statusline_bytecount 0x0
int string summary_block_address_families 0x0
int string tap_faq2 0x0
int string tap_faq3 0x0
int string tap_mode 0x0
int string test_algoirhtms 0x0
int string thanks_for_donation 0x0
int string timestamp_iso 0x0
int string timestamp_short 0x0
int string timestamps 0x0
int string timestamps_none 0x0
int string title_activity_open_sslspeed 0x0
int string title_block_address_families 0x0
int string title_cancel 0x0
int string tls_auth_file 0x0
int string tls_authentication 0x0
int string tls_cipher_alert 0x0
int string tls_cipher_alert_title 0x0
int string tls_direction 0x0
int string tls_key_auth 0x0
int string tls_remote_deprecated 0x0
int string tls_settings 0x0
int string tor_orbot 0x0
int string translationby 0x0
int string tun_error_helpful 0x0
int string tun_open_error 0x0
int string unhandled_exception 0x0
int string unhandled_exception_context 0x0
int string unknown_state 0x0
int string uploaded_data 0x0
int string useLZO 0x0
int string useTLSAuth 0x0
int string use_default_title 0x0
int string use_logarithmic_scale 0x0
int string use_pull 0x0
int string use_system_proxy 0x0
int string use_system_proxy_summary 0x0
int string userpw_file 0x0
int string using_proxy 0x0
int string version_and_later 0x0
int string version_info 0x0
int string version_upto 0x0
int string volume_byte 0x0
int string volume_gbyte 0x0
int string volume_kbyte 0x0
int string volume_mbyte 0x0
int string vpn_allow_bypass 0x0
int string vpn_allow_radio 0x0
int string vpn_allowed_apps 0x0
int string vpn_disallow_radio 0x0
int string vpn_import_hint 0x0
int string vpn_launch_title 0x0
int string vpn_list_title 0x0
int string vpn_shortcut 0x0
int string vpn_status 0x0
int string vpn_tethering_title 0x0
int string vpn_type 0x0
int string vpnbehaviour 0x0
int string vpnselected 0x0
int string warn_no_dns 0x0
int string weakmd 0x0
int string weakmd_title 0x0
int style blinkt 0x0
int style blinkt_baseTheme 0x0
int style blinkt_dialog 0x0
int[] styleable FileSelectLayout { 0x0, 0x0, 0x0 }
int styleable FileSelectLayout_certificate 0
int styleable FileSelectLayout_fileTitle 1
int styleable FileSelectLayout_showClear 2
int[] styleable PagerSlidingTabStrip { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable PagerSlidingTabStrip_pstsDividerColor 0
int styleable PagerSlidingTabStrip_pstsDividerPadding 1
int styleable PagerSlidingTabStrip_pstsDividerWidth 2
int styleable PagerSlidingTabStrip_pstsIndicatorColor 3
int styleable PagerSlidingTabStrip_pstsIndicatorHeight 4
int styleable PagerSlidingTabStrip_pstsPaddingMiddle 5
int styleable PagerSlidingTabStrip_pstsScrollOffset 6
int styleable PagerSlidingTabStrip_pstsShouldExpand 7
int styleable PagerSlidingTabStrip_pstsTabBackground 8
int styleable PagerSlidingTabStrip_pstsTabPaddingLeftRight 9
int styleable PagerSlidingTabStrip_pstsTextAllCaps 10
int styleable PagerSlidingTabStrip_pstsTextAlpha 11
int styleable PagerSlidingTabStrip_pstsTextSelectedAlpha 12
int styleable PagerSlidingTabStrip_pstsTextSelectedStyle 13
int styleable PagerSlidingTabStrip_pstsTextStyle 14
int styleable PagerSlidingTabStrip_pstsUnderlineColor 15
int styleable PagerSlidingTabStrip_pstsUnderlineHeight 16
int xml app_restrictions 0x0
