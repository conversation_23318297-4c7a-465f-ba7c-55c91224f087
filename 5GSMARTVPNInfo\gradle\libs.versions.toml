[versions]
agp = "8.10.1"
androidGifDrawable = "1.2.28"
appUpdate = "2.1.0"
audienceNetworkSdk = "6.20.0"
billing = "7.1.1"
compiler = "4.16.0"
coreKtx = "1.16.0"
facebook = "********"
glide = "4.16.0"
inferAnnotation = "0.18.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
localbroadcastmanager = "1.1.0"
lottie = "6.6.1"
material = "1.12.0"
activity = "1.10.1"
constraintlayout = "2.2.1"
playServicesAds = "24.3.0"
userMessagingPlatform = "3.2.0"
volley = "1.2.1"
workRuntime = "2.10.1"
navigationFragment = "2.9.0"
navigationUi = "2.9.0"
googleServices = "4.4.2"
firebaseMessaging = "24.1.1"
kotlin = "2.0.0"
coreKtxVersion = "1.16.0"
swiperefreshlayout = "1.1.0"

[libraries]
android-gif-drawable = { module = "pl.droidsonroids.gif:android-gif-drawable", version.ref = "androidGifDrawable" }
app-update = { module = "com.google.android.play:app-update", version.ref = "appUpdate" }
audience-network-sdk = { module = "com.facebook.android:audience-network-sdk", version.ref = "audienceNetworkSdk" }
billing = { module = "com.android.billingclient:billing", version.ref = "billing" }
compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "compiler" }
core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
facebook = { module = "com.google.ads.mediation:facebook", version.ref = "facebook" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
infer-annotation = { module = "com.facebook.infer.annotation:infer-annotation", version.ref = "inferAnnotation" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
localbroadcastmanager = { module = "androidx.localbroadcastmanager:localbroadcastmanager", version.ref = "localbroadcastmanager" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
play-services-ads = { module = "com.google.android.gms:play-services-ads", version.ref = "playServicesAds" }
user-messaging-platform = { module = "com.google.android.ump:user-messaging-platform", version.ref = "userMessagingPlatform" }
volley = { module = "com.android.volley:volley", version.ref = "volley" }
work-runtime = { module = "androidx.work:work-runtime", version.ref = "workRuntime" }
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version.ref = "navigationFragment" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version.ref = "navigationUi" }
google-services = { group = "com.google.gms", name = "google-services", version.ref = "googleServices" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging", version.ref = "firebaseMessaging" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtxVersion" }
androidx-swiperefreshlayout = { group = "androidx.swiperefreshlayout", name = "swiperefreshlayout", version.ref = "swiperefreshlayout" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

