package com.official.fivegfastvpn.ads;

import android.content.Context;

import com.official.fivegfastvpn.pro.ProConfig;

//Developer :--<PERSON><PERSON><PERSON>
public class AdsHelper {
    public static String admob_id = "";
    public static String admob_banner = "";
    public static String admob_interstitial = "";
    public static String admob_rewarded = "";
    public static String reward_time = "";
    public static String admob_native = "";
    public static String admob_open_ad = "";
    public static String facebook_id = "";
    public static String facebook_banner = "";
    public static String facebook_interstitial = "";
    public static String facebook_rewarded = "";
    public static String facebook_native = "";
    public static String banner_type = "";
    public static String interstitial_type = "";
    public static String rewarded_type = "";
    public static String native_type = "";

    public static boolean isAds = false;


    public static boolean isDisableAds(Context context) {
        if (ProConfig.isPremium(context)) {
            return true;
        } else {
            return !AdsHelper.isAds;
        }
    }

}
