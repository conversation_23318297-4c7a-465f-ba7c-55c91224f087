<?php
/**
 * 5G Smart VPN Admin Panel - Server Management
 */

session_start();
require_once 'includes/auth.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = 'Server Management';

// Handle server actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'toggle_status':
                $server_id = (int)$_POST['server_id'];
                $current_status = (int)$_POST['current_status'];
                $new_status = $current_status ? 0 : 1;

                $query = "UPDATE servers SET status = $new_status WHERE id = $server_id";
                if (mysqli_query($conn, $query)) {
                    logActivity($conn, 'update', "Server status changed for server ID: $server_id");
                    sendJsonResponse(['success' => true, 'new_status' => $new_status]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Failed to update server status'], 500);
                }
                break;

            case 'delete_server':
                $server_id = (int)$_POST['server_id'];

                $query = "DELETE FROM servers WHERE id = $server_id";
                if (mysqli_query($conn, $query)) {
                    logActivity($conn, 'delete', "Server deleted: ID $server_id");
                    sendJsonResponse(['success' => true, 'message' => 'Server deleted successfully']);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Failed to delete server'], 500);
                }
                break;
        }
    }
}

// Get servers with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Count total servers
$count_query = "SELECT COUNT(*) as total FROM servers";
$count_result = mysqli_query($conn, $count_query);
$total_servers = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_servers / $limit);

// Get servers
$query = "SELECT * FROM servers ORDER BY pos ASC, id DESC LIMIT $limit OFFSET $offset";
$result = mysqli_query($conn, $query);
$servers = [];
while ($row = mysqli_fetch_assoc($result)) {
    $servers[] = $row;
}

// Get dashboard stats for sidebar
$stats = getDashboardStats($conn);

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Server Management</h1>
                <p class="page-subtitle">Manage your VPN servers and configurations</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="server-add.php" class="btn btn-primary">
                        <i class="ri-add-line"></i>
                        <span class="hide-mobile">Add Server</span>
                    </a>
                    <button class="btn btn-secondary" onclick="refreshServers()">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Refresh</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-server-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $total_servers; ?></h3>
                        <p class="stat-label">Total Servers</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            <?php echo array_sum(array_column($servers, 'status')); ?> Active
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-global-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo count(array_unique(array_column($servers, 'type'))); ?></h3>
                        <p class="stat-label">Server Types</p>
                        <span class="stat-change neutral">
                            <i class="ri-information-line"></i>
                            Different protocols
                        </span>
                    </div>
                </div>
            </div>

            <!-- Servers Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">All Servers</h3>
                    <div class="card-actions">
                        <div class="search-box">
                            <input type="text" id="serverSearch" placeholder="Search servers..." class="form-control">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table" id="serversTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Server</th>
                                    <th>Type</th>
                                    <th>Position</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($servers as $server): ?>
                                <tr data-server-id="<?php echo $server['id']; ?>">
                                    <td><?php echo $server['id']; ?></td>
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="server-flag">
                                                <img src="../<?php echo $server['flagURL']; ?>"
                                                     alt="<?php echo htmlspecialchars($server['name']); ?>"
                                                     style="width: 32px; height: 24px; border-radius: 4px; object-fit: cover;">
                                            </div>
                                            <div>
                                                <h4 class="server-name"><?php echo htmlspecialchars($server['name']); ?></h4>
                                                <p class="text-muted" style="font-size: 0.75rem; margin: 0;">
                                                    <?php echo htmlspecialchars($server['username']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($server['type'] == 1): ?>
                                            <span class="badge-modern badge-success">Free Server</span>
                                        <?php else: ?>
                                            <span class="badge-modern badge-warning">Premium Server</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $server['pos']; ?></td>
                                    <td>
                                        <label class="switch">
                                            <input type="checkbox"
                                                   class="status-toggle"
                                                   data-server-id="<?php echo $server['id']; ?>"
                                                   data-current-status="<?php echo $server['status']; ?>"
                                                   <?php echo $server['status'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="server-edit.php?id=<?php echo $server['id']; ?>"
                                               class="btn btn-sm btn-secondary"
                                               title="Edit Server">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deleteServer(<?php echo $server['id']; ?>)"
                                                    title="Delete Server">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <?php echo generatePagination($page, $total_pages, 'servers.php'); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<!-- Additional CSS -->
<style>
.search-box {
    width: 250px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--success-500);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.badge-modern {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-info {
    background: var(--info-50);
    color: var(--info-600);
}

.badge-success {
    background: var(--success-50);
    color: var(--success-600);
}

.badge-warning {
    background: var(--warning-50);
    color: var(--warning-600);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    color: var(--gray-700);
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.pagination-btn:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}
</style>

<!-- Scripts -->
<script>
// Toggle server status
document.querySelectorAll('.status-toggle').forEach(toggle => {
    toggle.addEventListener('change', function() {
        const serverId = this.dataset.serverId;
        const currentStatus = parseInt(this.dataset.currentStatus);

        Admin.request('servers.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=toggle_status&server_id=${serverId}&current_status=${currentStatus}`
        })
        .then(response => {
            if (response.success) {
                this.dataset.currentStatus = response.new_status;
                Admin.showSuccess('Server status updated successfully');
            } else {
                this.checked = !this.checked; // Revert toggle
                Admin.showError('Failed to update server status');
            }
        })
        .catch(error => {
            this.checked = !this.checked; // Revert toggle
            Admin.showError('An error occurred while updating server status');
        });
    });
});

// Delete server
function deleteServer(serverId) {
    Admin.confirm('This will permanently delete the server. This action cannot be undone.', () => {
        Admin.request('servers.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete_server&server_id=${serverId}`
        })
        .then(response => {
            if (response.success) {
                document.querySelector(`tr[data-server-id="${serverId}"]`).remove();
                Admin.showSuccess('Server deleted successfully');
            } else {
                Admin.showError(response.message || 'Failed to delete server');
            }
        })
        .catch(error => {
            Admin.showError('An error occurred while deleting the server');
        });
    });
}

// Search functionality
document.getElementById('serverSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#serversTable tbody tr');

    rows.forEach(row => {
        const serverName = row.querySelector('.server-name').textContent.toLowerCase();
        const username = row.querySelector('.text-muted').textContent.toLowerCase();

        if (serverName.includes(searchTerm) || username.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Refresh servers
function refreshServers() {
    location.reload();
}
</script>

<?php include 'includes/footer.php'; ?>
