<?php
/**
 * Custom Ads System Database Fix Script
 * This script fixes all database issues in the custom ads system
 */

session_start();
require_once 'includes/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$success_messages = [];
$error_messages = [];

// Function to check if table exists
function tableExists($conn, $tableName) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    return mysqli_num_rows($result) > 0;
}

// Function to check if column exists
function columnExists($conn, $tableName, $columnName) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$tableName` LIKE '$columnName'");
    return mysqli_num_rows($result) > 0;
}

// Function to safely add column
function addColumnSafely($conn, $tableName, $columnDefinition, &$success_messages, &$error_messages) {
    $sql = "ALTER TABLE `$tableName` ADD COLUMN $columnDefinition";
    if (mysqli_query($conn, $sql)) {
        $success_messages[] = "Added column to $tableName: $columnDefinition";
        return true;
    } else {
        $error = mysqli_error($conn);
        if (strpos($error, 'Duplicate column name') === false) {
            $error_messages[] = "Failed to add column to $tableName: $error";
        }
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_fix'])) {
    try {
        // Start transaction
        mysqli_begin_transaction($conn);

        // 1. Create customer_accounts table if it doesn't exist
        if (!tableExists($conn, 'customer_accounts')) {
            $sql = "CREATE TABLE customer_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                whatsapp_number VARCHAR(20) UNIQUE NOT NULL,
                customer_name VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                total_spent DECIMAL(10,2) DEFAULT 0.00,
                total_orders INT DEFAULT 0,
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP NULL,
                verification_code VARCHAR(6),
                verification_expires TIMESTAMP NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                INDEX idx_whatsapp (whatsapp_number),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($conn, $sql)) {
                $success_messages[] = "Created customer_accounts table";
            } else {
                throw new Exception("Failed to create customer_accounts table: " . mysqli_error($conn));
            }
        } else {
            $success_messages[] = "customer_accounts table already exists";
        }

        // 2. Create customer_payments table if it doesn't exist
        if (!tableExists($conn, 'customer_payments')) {
            $sql = "CREATE TABLE customer_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_id INT NOT NULL,
                package_id INT NULL,
                transaction_id VARCHAR(100) NOT NULL,
                payment_method ENUM('bkash', 'nagad', 'rocket', 'google_pay', 'paypal', 'manual') NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'BDT',
                payment_status ENUM('pending', 'verified', 'rejected', 'refunded') DEFAULT 'pending',
                payment_proof TEXT,
                admin_notes TEXT,
                verified_by INT NULL,
                verified_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customer (customer_id),
                INDEX idx_status (payment_status),
                INDEX idx_method (payment_method)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($conn, $sql)) {
                $success_messages[] = "Created customer_payments table";
            } else {
                throw new Exception("Failed to create customer_payments table: " . mysqli_error($conn));
            }
        } else {
            $success_messages[] = "customer_payments table already exists";
        }

        // 3. Create payment_methods table if it doesn't exist
        if (!tableExists($conn, 'payment_methods')) {
            $sql = "CREATE TABLE payment_methods (
                id INT AUTO_INCREMENT PRIMARY KEY,
                method_code VARCHAR(20) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                account_number VARCHAR(100) NOT NULL,
                account_name VARCHAR(255) NOT NULL,
                instructions TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_code (method_code)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($conn, $sql)) {
                $success_messages[] = "Created payment_methods table";
            } else {
                throw new Exception("Failed to create payment_methods table: " . mysqli_error($conn));
            }
        } else {
            $success_messages[] = "payment_methods table already exists";
        }

        // 4. Create ad_packages table if it doesn't exist
        if (!tableExists($conn, 'ad_packages')) {
            $sql = "CREATE TABLE ad_packages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                package_name VARCHAR(100) NOT NULL,
                duration_days INT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'BDT',
                max_ads INT DEFAULT 1,
                features JSON,
                is_active BOOLEAN DEFAULT TRUE,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_duration (duration_days)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($conn, $sql)) {
                $success_messages[] = "Created ad_packages table";
            } else {
                throw new Exception("Failed to create ad_packages table: " . mysqli_error($conn));
            }
        } else {
            $success_messages[] = "ad_packages table already exists";
        }

        // 5. Add missing columns to custom_ads table if it exists
        if (tableExists($conn, 'custom_ads')) {
            $columns_to_add = [
                'customer_id INT NULL',
                'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                'is_approved BOOLEAN DEFAULT NULL',
                'package_id INT NULL',
                'payment_id INT NULL',
                'url_type ENUM("website", "playstore", "appstore", "other") DEFAULT "website"',
                'button_text VARCHAR(50) DEFAULT "Visit"',
                'expires_at TIMESTAMP NULL',
                'approved_by INT NULL',
                'approved_at TIMESTAMP NULL',
                'priority INT DEFAULT 0'
            ];

            foreach ($columns_to_add as $column_def) {
                $column_name = explode(' ', $column_def)[0];
                if (!columnExists($conn, 'custom_ads', $column_name)) {
                    addColumnSafely($conn, 'custom_ads', $column_def, $success_messages, $error_messages);
                }
            }
        }

        // 6. Insert default payment methods if table is empty
        if (tableExists($conn, 'payment_methods')) {
            $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM payment_methods");
            $count = mysqli_fetch_assoc($count_result)['count'];
            
            if ($count == 0) {
                $payment_methods = [
                    ['bkash', 'bKash Mobile Banking', '***********', '5G Smart VPN', 'Send money to our bKash number and provide the transaction ID', 1, 1],
                    ['nagad', 'Nagad Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Nagad number and provide the transaction ID', 1, 2],
                    ['rocket', 'Rocket Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Rocket number and provide the transaction ID', 1, 3],
                    ['google_pay', 'Google Pay', '<EMAIL>', '5G Smart VPN', 'Pay using Google Pay and provide the transaction ID', 1, 4],
                    ['paypal', 'PayPal', '<EMAIL>', '5G Smart VPN', 'Pay using PayPal and provide the transaction ID', 1, 5]
                ];

                $stmt = $conn->prepare("INSERT INTO payment_methods (method_code, display_name, account_number, account_name, instructions, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
                
                foreach ($payment_methods as $method) {
                    $stmt->bind_param("sssssii", ...$method);
                    $stmt->execute();
                }
                
                $success_messages[] = "Inserted default payment methods";
            }
        }

        // 7. Insert default ad packages if table is empty
        if (tableExists($conn, 'ad_packages')) {
            $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM ad_packages");
            $count = mysqli_fetch_assoc($count_result)['count'];
            
            if ($count == 0) {
                $packages = [
                    ['Basic Ad', 7, 500.00, 'BDT', 1, '{"description": "Basic ad package for 7 days", "features": ["Standard placement", "Basic analytics"]}', 1, 1],
                    ['Premium Ad', 15, 1000.00, 'BDT', 1, '{"description": "Premium ad package for 15 days", "features": ["Priority placement", "Advanced analytics", "Featured listing"]}', 1, 2],
                    ['Extended Ad', 30, 1800.00, 'BDT', 1, '{"description": "Extended ad package for 30 days", "features": ["Top placement", "Detailed analytics", "Featured listing", "Social media promotion"]}', 1, 3]
                ];

                $stmt = $conn->prepare("INSERT INTO ad_packages (package_name, duration_days, price, currency, max_ads, features, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                
                foreach ($packages as $package) {
                    $stmt->bind_param("sidsissi", ...$package);
                    $stmt->execute();
                }
                
                $success_messages[] = "Inserted default ad packages";
            }
        }

        // Commit transaction
        mysqli_commit($conn);
        $success_messages[] = "Database fix completed successfully!";

    } catch (Exception $e) {
        mysqli_rollback($conn);
        $error_messages[] = "Database fix failed: " . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Custom Ads Database Fix</h1>
                <p class="page-subtitle">Fix all database issues in the custom ads system</p>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success">
                    <h5><i class="ri-check-line me-2"></i>Success!</h5>
                    <ul class="mb-0">
                        <?php foreach ($success_messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_messages)): ?>
                <div class="alert alert-danger">
                    <h5><i class="ri-error-warning-line me-2"></i>Errors!</h5>
                    <ul class="mb-0">
                        <?php foreach ($error_messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Database Issues to Fix</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Issues that will be fixed:</h5>
                            <ul>
                                <li>Create missing <code>customer_accounts</code> table</li>
                                <li>Create missing <code>customer_payments</code> table</li>
                                <li>Create missing <code>payment_methods</code> table</li>
                                <li>Create missing <code>ad_packages</code> table</li>
                                <li>Add missing columns to <code>custom_ads</code> table</li>
                                <li>Insert default payment methods</li>
                                <li>Insert default ad packages</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Pages that will be fixed:</h5>
                            <ul>
                                <li><code>payment-verification.php</code> - Redirect issue</li>
                                <li><code>customer-accounts.php</code> - SQL column error</li>
                                <li><code>custom-ads-analytics.php</code> - SQL column error</li>
                                <li><code>customads-provider/payments.php</code> - Missing table</li>
                                <li><code>payment-methods.php</code> - UI improvements</li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <form method="POST">
                        <div class="text-center">
                            <button type="submit" name="run_fix" class="btn btn-primary btn-lg">
                                <i class="ri-tools-line me-2"></i>Run Database Fix
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>

<?php include 'includes/footer.php'; ?>
