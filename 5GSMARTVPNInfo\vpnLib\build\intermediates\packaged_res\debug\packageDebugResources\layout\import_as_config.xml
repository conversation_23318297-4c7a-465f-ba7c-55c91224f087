<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2012-2016 <PERSON><PERSON>
  ~ Distributed under the GNU GPL v2 with additional terms. For full terms see the file doc/LICENSE.txt
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:padding="20dp"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <EditText
        android:id="@+id/as_servername"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="4dp"
        android:hint="@string/as_servername"
        android:inputType="textUri" />

    <EditText
        android:id="@+id/username"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="4dp"
        android:hint="@string/auth_username"
        android:inputType="textEmailAddress" />

    <EditText
        android:id="@+id/password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="4dp"
        android:hint="@string/password"
        android:inputType="textPassword" />

    <CheckBox
        android:id="@+id/request_autologin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="16dp"
        android:text="@string/request_autologin" />

</LinearLayout>