<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2012-2016 <PERSON><PERSON>
  ~ Distributed under the GNU GPL v2 with additional terms. For full terms see the file doc/LICENSE.txt
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

    <EditText
            android:id="@+id/username"
            android:inputType="textEmailAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:layout_marginBottom="4dp"
            android:hint="@string/auth_username" />
    <EditText
            android:id="@+id/password"
            android:inputType="textPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:layout_marginBottom="4dp"
            android:hint="@string/password"/>
    <CheckBox
            android:id="@+id/show_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginLeft="4dp"
            android:text="@string/show_password"
            android:layout_marginRight="4dp"
            />

    <CheckBox
            android:id="@+id/save_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginLeft="4dp"
            android:text="@string/save_password"
            android:layout_marginRight="4dp"
            android:layout_marginBottom="16dp"/>


</LinearLayout>