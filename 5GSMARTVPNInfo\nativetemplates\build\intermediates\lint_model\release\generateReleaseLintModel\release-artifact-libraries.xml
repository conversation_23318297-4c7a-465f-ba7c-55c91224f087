<libraries>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367aaff0a5468a07eb127b0a27da0f79\transformed\constraintlayout-2.0.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367aaff0a5468a07eb127b0a27da0f79\transformed\constraintlayout-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e5a086bc9443d69ab2fe958744d7d0e\transformed\appcompat-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e5a086bc9443d69ab2fe958744d7d0e\transformed\appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:24.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4174daf14328b785510b24290d95f19a\transformed\play-services-ads-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:24.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4174daf14328b785510b24290d95f19a\transformed\play-services-ads-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.16@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.16\3fdb501b45ba22c6e9c0f2abdb6ed747a48c71af\error_prone_annotations-2.16.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.16"/>
  <library
      name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-api:24.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8afb58b02950750117b73c6e4cbd0d1e\transformed\play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8afb58b02950750117b73c6e4cbd0d1e\transformed\play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82cdd9a7f54d4e88d2bcf3dc67954fbd\transformed\play-services-appset-16.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82cdd9a7f54d4e88d2bcf3dc67954fbd\transformed\play-services-appset-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c0856ee9f8851848d96b463dd25e7c\transformed\play-services-base-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c0856ee9f8851848d96b463dd25e7c\transformed\play-services-base-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69430a9206f33c10119b8ff808597f5e\transformed\play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69430a9206f33c10119b8ff808597f5e\transformed\play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ced3060695179a47a25383730662811\transformed\play-services-measurement-sdk-api-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ced3060695179a47a25383730662811\transformed\play-services-measurement-sdk-api-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36328fbd2e6cdd3da6135066d24c2ca4\transformed\user-messaging-platform-3.2.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36328fbd2e6cdd3da6135066d24c2ca4\transformed\user-messaging-platform-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6aa3dbb6a1489ad95ef8e5b8222484b\transformed\play-services-measurement-base-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6aa3dbb6a1489ad95ef8e5b8222484b\transformed\play-services-measurement-base-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6bab5fff5b19d3534fbab2e749f05fd\transformed\fragment-1.1.0\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6bab5fff5b19d3534fbab2e749f05fd\transformed\fragment-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61d3416a857298d0cd11086dfac88568\transformed\appcompat-resources-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61d3416a857298d0cd11086dfac88568\transformed\appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd03adfcdd63d82053a7adf03f949f4\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd03adfcdd63d82053a7adf03f949f4\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1798530a3a51669983781b25eadf6b2\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1798530a3a51669983781b25eadf6b2\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.11.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62e0930e6278890e2494793a3284fec1\transformed\webkit-1.11.0-alpha02\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.11.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62e0930e6278890e2494793a3284fec1\transformed\webkit-1.11.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a53d1fd60ccd865912fd911c2c1f81e0\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a53d1fd60ccd865912fd911c2c1f81e0\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29754e1a456e4be877c232e17a1a033d\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29754e1a456e4be877c232e17a1a033d\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c20c150dc2888661a3da9c5b83dbccb\transformed\activity-1.0.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c20c150dc2888661a3da9c5b83dbccb\transformed\activity-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3767a76f99e038698d8db9ee18ded8e\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3767a76f99e038698d8db9ee18ded8e\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\269d71e558c2a625651837f7b221d925\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\269d71e558c2a625651837f7b221d925\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658f6772ae96433c68daed33a252d27c\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658f6772ae96433c68daed33a252d27c\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffe8fb42c4c871da1b77a11883e42fba\transformed\core-1.8.0\jars\classes.jar"
      resolved="androidx.core:core:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffe8fb42c4c871da1b77a11883e42fba\transformed\core-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90040a155c4760fb3b9c7e5dc5ab0829\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90040a155c4760fb3b9c7e5dc5ab0829\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c8be7624f6af4d1f035ad9f17d31e7\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c8be7624f6af4d1f035ad9f17d31e7\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d70180d862d9205be5d790f0a94fe38\transformed\ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d70180d862d9205be5d790f0a94fe38\transformed\ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8a76087ccc4d3cc0025bf312ab8c64\transformed\lifecycle-runtime-2.3.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8a76087ccc4d3cc0025bf312ab8c64\transformed\lifecycle-runtime-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2a58375826934c4960ab3a52820f1fa\transformed\lifecycle-viewmodel-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2a58375826934c4960ab3a52820f1fa\transformed\lifecycle-viewmodel-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c317335c08b731491a64dbe0e024d43e\transformed\savedstate-1.0.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c317335c08b731491a64dbe0e024d43e\transformed\savedstate-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da826fe1402c9118f73ef13bbfb5b373\transformed\work-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da826fe1402c9118f73ef13bbfb5b373\transformed\work-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed92a844a106ca8fd4326b4c6a1a2d55\transformed\lifecycle-livedata-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed92a844a106ca8fd4326b4c6a1a2d55\transformed\lifecycle-livedata-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c6fdd1c6d0fd96240e47ad0b121670\transformed\lifecycle-livedata-core-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c6fdd1c6d0fd96240e47ad0b121670\transformed\lifecycle-livedata-core-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.3.1\fc466261d52f4433863642fb40d12441ae274a98\lifecycle-common-2.3.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.3.1"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cec0fcb8f35002720eaf0689923b596c\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cec0fcb8f35002720eaf0689923b596c\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.1.0\b3152fc64428c9354344bd89848ecddc09b6f07e\core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df571cf2b9198d7db9e493173fd6a013\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df571cf2b9198d7db9e493173fd6a013\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.6.0\a7257339a052df0f91433cf9651231bbb802b502\annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfb435dcf1c192720c87669404b91be1\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfb435dcf1c192720c87669404b91be1\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.21\43d50ab85bc7587adfe3dda3dbe579e5f8d51265\kotlin-stdlib-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.21\d749cd5ae25da36d06e5028785038e24f9d37976\kotlin-stdlib-common-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c874bd200ed927c36d2e30ed3dac82f\transformed\startup-runtime-1.0.0\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c874bd200ed927c36d2e30ed3dac82f\transformed\startup-runtime-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.4\1f001d7db280a89a6c26b26a66eb064bb6d5efeb\constraintlayout-solver-2.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.4"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"/>
  <library
      name="androidx.core:core-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dda2e154ab55b3fd69f96701406f7fb0\transformed\core-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dda2e154ab55b3fd69f96701406f7fb0\transformed\core-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.room:room-runtime:2.2.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd756648e3c892100688e786ac32dd5\transformed\room-runtime-2.2.5\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.2.5"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd756648e3c892100688e786ac32dd5\transformed\room-runtime-2.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509dfce3cd41c8904e5e958c9f0a58c6\transformed\sqlite-framework-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509dfce3cd41c8904e5e958c9f0a58c6\transformed\sqlite-framework-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2faa3c89633f3294e11833fd7e18e0bf\transformed\sqlite-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2faa3c89633f3294e11833fd7e18e0bf\transformed\sqlite-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0d3df0c2c76edf7777ed5eaea6cecd\transformed\lifecycle-service-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0d3df0c2c76edf7777ed5eaea6cecd\transformed\lifecycle-service-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55123f7d993a811f06f552077cf1814c\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55123f7d993a811f06f552077cf1814c\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.2.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.2.5\f5e3b73a0c2ab5e276e26868e4ce3542baede207\room-common-2.2.5.jar"
      resolved="androidx.room:room-common:2.2.5"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
</libraries>
