<?php
/**
 * 5G Smart VPN Admin Panel - Logs Actions API
 * Handles log deletion and clearing operations
 */

session_start();
require_once '../includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is authenticated
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    switch ($action) {
        case 'delete_log':
            $log_id = $input['log_id'] ?? 0;
            if (!$log_id) {
                throw new Exception('Log ID is required');
            }
            
            $result = deleteLog($log_id);
            if ($result) {
                // Log this action
                logActivity($conn, 'log_delete', "Deleted log entry ID: $log_id");
                echo json_encode(['success' => true, 'message' => 'Log entry deleted successfully']);
            } else {
                throw new Exception('Failed to delete log entry');
            }
            break;
            
        case 'clear_logs':
            $days = $input['days'] ?? 30; // Default: clear logs older than 30 days
            $log_level = $input['level'] ?? 'all'; // Default: all levels
            
            $result = clearLogs($days, $log_level);
            if ($result !== false) {
                // Log this action
                logActivity($conn, 'logs_clear', "Cleared $result log entries older than $days days (level: $log_level)");
                echo json_encode([
                    'success' => true, 
                    'message' => "Successfully cleared $result log entries",
                    'deleted_count' => $result
                ]);
            } else {
                throw new Exception('Failed to clear logs');
            }
            break;
            
        case 'clear_all_logs':
            $result = clearAllLogs();
            if ($result !== false) {
                // Log this action
                logActivity($conn, 'logs_clear_all', "Cleared all log entries ($result total)");
                echo json_encode([
                    'success' => true, 
                    'message' => "Successfully cleared all $result log entries",
                    'deleted_count' => $result
                ]);
            } else {
                throw new Exception('Failed to clear all logs');
            }
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * Delete a specific log entry
 */
function deleteLog($log_id) {
    global $conn;
    
    $stmt = $conn->prepare("DELETE FROM admin_logs WHERE id = ?");
    $stmt->bind_param("i", $log_id);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Clear logs based on criteria
 */
function clearLogs($days, $level = 'all') {
    global $conn;
    
    $where_conditions = ["created_at < DATE_SUB(NOW(), INTERVAL ? DAY)"];
    $params = [$days];
    $types = "i";
    
    // Add level filter if specified
    if ($level !== 'all') {
        // Map log levels to actions (since admin_logs doesn't have a level field)
        $level_actions = [
            'info' => ['login', 'logout', 'dashboard_access', 'view'],
            'warning' => ['update', 'change', 'modify'],
            'error' => ['delete', 'clear', 'remove'],
            'debug' => ['debug', 'test']
        ];
        
        if (isset($level_actions[$level])) {
            $placeholders = str_repeat('?,', count($level_actions[$level]) - 1) . '?';
            $where_conditions[] = "action IN ($placeholders)";
            $params = array_merge($params, $level_actions[$level]);
            $types .= str_repeat('s', count($level_actions[$level]));
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // First, count how many will be deleted
    $count_sql = "SELECT COUNT(*) as count FROM admin_logs WHERE $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $count = $count_result->fetch_assoc()['count'];
    $count_stmt->close();
    
    // Then delete them
    $delete_sql = "DELETE FROM admin_logs WHERE $where_clause";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param($types, ...$params);
    $result = $delete_stmt->execute();
    $delete_stmt->close();
    
    return $result ? $count : false;
}

/**
 * Clear all logs
 */
function clearAllLogs() {
    global $conn;
    
    // First, count total logs
    $count_result = $conn->query("SELECT COUNT(*) as count FROM admin_logs");
    $count = $count_result->fetch_assoc()['count'];
    
    // Then delete all
    $result = $conn->query("DELETE FROM admin_logs");
    
    return $result ? $count : false;
}

/**
 * Log activity function (reuse from auth.php)
 */
function logActivity($conn, $action, $description) {
    $admin_id = $_SESSION['admin_id'] ?? 0;
    $admin_username = $_SESSION['admin_username'] ?? 'System';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = mysqli_real_escape_string($conn, $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown');

    $query = "INSERT INTO admin_logs (admin_id, admin_username, action, description, ip_address, user_agent, created_at)
              VALUES ($admin_id, '$admin_username', '$action', '$description', '$ip_address', '$user_agent', NOW())";

    mysqli_query($conn, $query);
}
?>
