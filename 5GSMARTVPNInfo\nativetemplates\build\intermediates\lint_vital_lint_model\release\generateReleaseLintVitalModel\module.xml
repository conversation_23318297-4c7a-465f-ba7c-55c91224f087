<lint-module
    format="1"
    dir="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\nativetemplates"
    name=":nativetemplates"
    type="LIBRARY"
    maven="5G SMART VPN:nativetemplates:unspecified"
    agpVersion="8.10.1"
    buildFolder="build"
    resourcePrefix="gnt_"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-30\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-30"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
