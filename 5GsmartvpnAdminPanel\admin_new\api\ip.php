<?php
/**
 * 5G Smart VPN Admin Panel - Modern IP Detection API
 * Returns client IP address with enhanced detection and JSON response
 */

// Set JSON response header
header('Content-Type: application/json');

// This endpoint doesn't require authentication for basic IP detection

try {
    // Trusted proxy IPs (can be configured)
    $trustProxyIPs = [
        '************',
        '127.0.0.1',
        '::1'
    ];

    // Get initial client IP
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? null;

    if (!$clientIP) {
        throw new Exception("Unable to determine client IP address");
    }

    // Check if request is coming from a trusted proxy
    if (in_array($clientIP, $trustProxyIPs)) {

        // Headers to check for real client IP (in order of preference)
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_X_FORWARDED_FOR',      // Standard proxy header
            'HTTP_X_FORWARDED',          // Alternative proxy header
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster/load balancer
            'HTTP_CLIENT_IP',            // ISP proxy
            'HTTP_FORWARDED_FOR',        // Alternative header
            'HTTP_FORWARDED'             // RFC 7239
        ];

        foreach ($headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                $headerValue = $_SERVER[$header];

                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($headerValue, ',') !== false) {
                    $ips = explode(',', $headerValue);
                    $headerValue = trim($ips[0]); // Take the first IP
                }

                // Validate IP address
                if (filter_var($headerValue, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    $clientIP = $headerValue;
                    break;
                }
            }
        }
    }

    // Validate final IP
    if (!filter_var($clientIP, FILTER_VALIDATE_IP)) {
        throw new Exception("Invalid IP address detected");
    }

    // Determine IP type
    $ipType = 'unknown';
    if (filter_var($clientIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $ipType = 'IPv4';
    } elseif (filter_var($clientIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
        $ipType = 'IPv6';
    }

    // Check if IP is private/local
    $isPrivate = !filter_var($clientIP, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);

    // Response format options
    $format = $_GET['format'] ?? 'json';

    if ($format === 'plain' || $format === 'text') {
        // Plain text response for backward compatibility
        header('Content-Type: text/plain');
        echo $clientIP;
    } else {
        // JSON response (default)
        $response = [
            'success' => true,
            'ip' => $clientIP,
            'type' => $ipType,
            'is_private' => $isPrivate,
            'timestamp' => time(),
            'api_version' => '3.0',
            'source' => 'modern_admin_panel',
            'endpoint' => 'ip'
        ];

        // Add debug info if requested
        if (isset($_GET['debug']) && $_GET['debug'] === '1') {
            $response['debug'] = [
                'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? null,
                'headers_checked' => $headers,
                'is_proxy_request' => in_array($_SERVER['REMOTE_ADDR'] ?? '', $trustProxyIPs),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ];
        }

        echo json_encode($response);
    }

} catch (Exception $e) {
    // Error response
    $response = [
        'success' => false,
        'error' => 'IP Detection Failed',
        'message' => $e->getMessage(),
        'timestamp' => time(),
        'api_version' => '3.0',
        'source' => 'modern_admin_panel',
        'endpoint' => 'ip'
    ];

    http_response_code(500);
    echo json_encode($response);
}
?>
