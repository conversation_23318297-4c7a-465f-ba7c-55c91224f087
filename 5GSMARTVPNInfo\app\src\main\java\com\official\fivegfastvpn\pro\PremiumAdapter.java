package com.official.fivegfastvpn.pro;


//Developer :--<PERSON><PERSON>er


import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.billingclient.api.ProductDetails;
import com.official.fivegfastvpn.R;


import java.util.List;

public class PremiumAdapter extends RecyclerView.Adapter<PremiumAdapter.PremiumHolder> {

    List<ProductDetails> list;
    PremiumBuy premiumBuy;

    public PremiumAdapter(List<ProductDetails> list, PremiumBuy premiumBuy) {
        this.list = list;
        this.premiumBuy = premiumBuy;
    }

    @NonNull
    @Override
    public PremiumHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new PremiumHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_premium,parent,false));
    }

    @Override
    public void onBindViewHolder(@NonNull PremiumHolder holder, int position) {

        ProductDetails details = list.get(position);

        holder.plan.setText(details.getName());
        holder.price.setText(details.getSubscriptionOfferDetails().get(0).getPricingPhases().getPricingPhaseList().get(0).getFormattedPrice());

        holder.itemView.setOnClickListener(view -> premiumBuy.click(details));

    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public static class PremiumHolder extends RecyclerView.ViewHolder{

        TextView price,plan;

        public PremiumHolder(@NonNull View itemView) {
            super(itemView);
            price = itemView.findViewById(R.id.item_pro_price);
            plan = itemView.findViewById(R.id.item_pro_time);

        }
    }
}
