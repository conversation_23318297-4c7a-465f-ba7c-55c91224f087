http://schemas.android.com/apk/res-auto;;${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/untranslatable.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/attrs.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values-sw600dp/dimens.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/bools.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values-v29/bools.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/colours.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/color/tab_text.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values-sw600dp/styles.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_stat_vpn_offline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_stat_vpn_offline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_stat_vpn_offline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_stat_vpn_offline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_stat_vpn_outline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_stat_vpn_outline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_stat_vpn_outline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_stat_vpn_outline.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/refs.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_stat_vpn.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_stat_vpn.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_stat_vpn.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_stat_vpn.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_quick.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_quick.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_quick.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_quick.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_menu_log.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_menu_log.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_menu_log.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_menu_log.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_menu_archive.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_menu_archive.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_menu_archive.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_stat_vpn_empty_halo.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_stat_vpn_empty_halo.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_stat_vpn_empty_halo.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_stat_vpn_empty_halo.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/vpn_item_settings.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/vpn_item_settings.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/vpn_item_settings.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_menu_copy_holo_light.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_menu_copy_holo_light.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_menu_copy_holo_light.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_menu_copy_holo_light.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/layout/import_as_config.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/layout/userpass.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/layout/api_confirm.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/layout/launchvpn.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/banner_tv.png,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/plurals.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:vpnLib*release*MAIN*sourceProvider*0*resDir*0}/xml/app_restrictions.xml,+array:tls_directions_values,0,V4002708a8,13002d0981,;0,1,,tls-crypt,tls-crypt-v2,;crm_entries,1,V4001a03bc,13002004dd,;No reconnection retries,One reconnection retry,Five reconnection retries,Fifty reconnection retries,Unlimited reconnection retries,;crm_values,0,V4002e0986,1300340a44,;1,2,5,50,-1,;vpn_types,1,V40008011c,13001202a9,;Certificates,PKCS12 File,Android Certificate,Username/Password,Static Keys,User/PW \+ Certificates,User/PW \+ PKCS12,User/PW \+ Android,External Auth Provider,;tls_directions_entries,1,V4001302ae,13001903b7,;0,1,Unspecified,Encryption (--tls-crypt),TLS Crypt V2,;auth_retry_type,1,V4002104e2,130025059e,;Disconnect\, forget password,Disconnect\, keep password,Ignore\, retry,;+attr:pstsScrollOffset,2,V800180413,3b00180446,;dimension:;pstsTextAlpha,2,V8002706c0,34002706ec,;float:;pstsTextSelectedStyle,2,V8002205f9,f002606b7,;flags:normal:0,bold:1,italic:2,;pstsUnderlineHeight,2,V800150353,3e00150389,;dimension:;certificate,2,V6000a0143,32000a016f,;boolean:;pstsTabBackground,2,V80019044f,3c00190483,;reference:;pstsTextAllCaps,2,V8001b04c6,38001b04f6,;boolean:;pstsUnderlineColor,2,V800110266,**********,;color:;showClear,2,V7000c01ac,31000c01d6,;boolean:;pstsTextSelectedAlpha,2,V8002806f5,3c00280729,;float:;pstsDividerColor,2,V8001202a0,37001202cf,;color:;pstsTextStyle,2,V8001d053a,f002105f0,;flags:normal:0,bold:1,italic:2,;pstsIndicatorColor,2,V80010022c,390010025d,;color:;fileTitle,2,V700090109,3a0009013c,;reference|string:;pstsTabPaddingLeftRight,2,V8001703d0,420017040a,;dimension:;pstsShouldExpand,2,V8001a048c,39001a04bd,;boolean:;pstsPaddingMiddle,2,V8001c04ff,3a001c0531,;boolean:;pstsDividerPadding,2,V800160392,3d001603c7,;dimension:;pstsDividerWidth,2,V8001302d8,3b0013030b,;dimension:;pstsIndicatorHeight,2,V800140314,3e0014034a,;dimension:;+bool:logSildersAlwaysVisible,3,V400090133,3500090164,;"false";logSildersAlwaysVisible,4,V4000700d4,3400070104,;"true";supportFileScheme,5,V4000700d4,2e000700fe,;"true";supportFileScheme,6,V4000700d4,2f000700ff,;"false";+color:background_tab_pressed,7,V40012025c,3a00120292,;"#1AFFFFFF";switchbar,7,V4000f021b,3e000f0255,;"@android\:color/darker_gray";dataOut,7,V4001602c2,29001602e7,;"#0000ff";gelb,7,V4000c01cd,26000c01ef,;"#ffff00";rot,7,V4000d01f4,25000d0215,;"#ff0000";tab_text,8,F;primary_dark,7,V4000a0150,2e000a017a,;"#303F9F";accent,7,V4000b018a,28000b01ae,;"#FFA726";dataIn,7,V400150299,28001502bd,;"#ff0000";primary,7,V400090116,290009013b,;"#3F51B5";+dimen:add_button_margin_topfab,3,V4000f021c,37000f024f,;"96dp";switchbar_pad,3,V400110289,2c001102b1,;"16dp";paddingItemsSidebarLog,3,V4000700d4,3500070105,;"20dp";elevation_low,3,V4000c0192,2b000c01b9,;"1dp";diameter,3,V4000b016a,27000b018d,;"48dp";elevation_high,3,V4000d01be,2c000d01e6,;"4dp";vpn_setting_padding,3,V4001202b6,32001202e4,;"16dp";round_button_diameter,3,V400100254,3400100284,;"56dp";stdpadding,3,V40008010a,280008012e,;"8dp";stdpadding,9,V4000800d5,29000800fa,;"16dp";add_button_margin,3,V4000e01eb,30000e0217,;"16dp";+drawable:ic_stat_vpn_offline,10,F;ic_stat_vpn_offline,11,F;ic_stat_vpn_offline,12,F;ic_stat_vpn_offline,13,F;ic_stat_vpn_outline,14,F;ic_stat_vpn_outline,15,F;ic_stat_vpn_outline,16,F;ic_stat_vpn_outline,17,F;ic_menu_play,18,V40008013c,4c00080184,;"@android\:drawable/ic_media_play";ic_stat_vpn,19,F;ic_stat_vpn,20,F;ic_stat_vpn,21,F;ic_stat_vpn,22,F;ic_quick,23,F;ic_quick,24,F;ic_quick,25,F;ic_quick,26,F;ic_menu_log,27,F;ic_menu_log,28,F;ic_menu_log,29,F;ic_menu_log,30,F;ic_menu_archive,31,F;ic_menu_archive,32,F;ic_menu_archive,33,F;ic_notification,34,F;ic_stat_vpn_empty_halo,35,F;ic_stat_vpn_empty_halo,36,F;ic_stat_vpn_empty_halo,37,F;ic_stat_vpn_empty_halo,38,F;vpn_item_settings,39,F;vpn_item_settings,40,F;vpn_item_settings,41,F;ic_menu_close_clear_cancel,18,V4000700d4,6700070137,;"@android\:drawable/ic_menu_close_clear_cancel";ic_menu_copy_holo_light,42,F;ic_menu_copy_holo_light,43,F;ic_menu_copy_holo_light,44,F;ic_menu_copy_holo_light,45,F;ic_menu_pause,18,V400090189,4e000901d3,;"@android\:drawable/ic_media_pause";+id:password,46,F;password,47,F;save_password,47,F;icon,48,F;warning,48,F;request_autologin,46,F;check,48,F;as_servername,46,F;prompt,48,F;show_password,47,F;username,46,F;username,47,F;+layout:api_confirm,48,F;launchvpn,49,F;userpass,47,F;import_as_config,46,F;+mipmap:ic_launcher,50,F;ic_launcher,51,F;ic_launcher,52,F;ic_launcher,53,F;ic_launcher,54,F;banner_tv,55,F;+plurals:days_left,56,V4000600d0,e0009015d,;one:One day left,other:%d days left,;months_left,56,V400020038,e000500cb,;one:One month left,other:%d months left,;minutes_left,56,V4000e01f7,e0011028d,;one:One minute left,other:%d minutes left,;hours_left,56,V4000a0162,e000d01f2,;one:One hour left,other:%d hours left,;+string:cancel,57,V4000b0207,29000b022c,;"Cancel";importing_config,57,V4008f2fba,53008f3009,;"Importing config file from source %1$s";state_waitorbot,57,V400fd6ad6,4600fd6b18,;"Waiting for Orbot to start";crashdump,57,V4010a6ef7,2f010a6f22,;"Crashdump";duplicate_profile_name,57,V400290ad5,5500290b26,;"Please enter a unique Profile Name";file_explorer_tab,57,V400832c3c,3b00832c73,;"File Explorer";importpkcs12fromconfig,57,V400c84ea2,6900c84f07,;"Import PKCS12 from configuration into Android Keystore";vpn_type,57,V400180581,29001805a6,;"Type";ab_lollipop_reinstall_title,57,V401709ec9,4d01709f12,;"Reinstalling VPN apps";import_content_resolve_error,57,V4008b2e6b,59008b2ec0,;"Could not read profile to import";error_orbot_and_proxy_options,57,V401deca3c,8d01decac5,;"Cannot use extra http-proxy-option statement and Orbot integration at the same time";channel_name_background,57,V401c8c055,4901c8c09a,;"Connection statistics";ab_proxy,57,V40167971a,1460167985c,;"Android will keep using your proxy settings specified for the mobile/Wi-Fi connection when no DNS servers are set. OpenVPN for Android will warn you about this in the log.When a VPN sets a DNS server Android will not use a proxy. There is no API to set a proxy for a VPN connection."Android will keep using your proxy settings specified for the mobile/Wi-Fi connection when no DNS servers are set. OpenVPN for Android will warn you about this in the log.<p>When a VPN sets a DNS server Android will not use a proxy. There is no API to set a proxy for a VPN connection.</p>;import_configuration_file,57,V4009431d4,4f0094321f,;"Import configuration file";start_vpn_ticker,57,V401016bee,4101016c2b,;"Connecting to VPN %s";edit_profile_title,57,V400ab3f4d,3d00ab3f86,;"Editing \"%s\"";faq_killswitch_title,57,V401eacdb7,5a01eace0d,;"Block non VPN connection (\"Killswitch\")";menu_import_short,57,V4008a2e36,34008a2e66,;"Import";enabled_connection_entry,57,V4018aaade,3c018aab16,;"Enabled";proxy,57,V401d6c620,2701d6c643,;"Proxy";vpn_launch_title,57,V400330db2,3f00330ded,;"Connecting to VPN…";generalsettings,57,V400a73dce,3400a73dfe,;"Settings";faq_vpndialog43_title,57,V4012a7bf3,49012a7c38,;"Vpn Confirmation Dialog";timestamp_iso,57,V401317dd7,2d01317e00,;"ISO";tls_auth_file,57,V40047158e,37004715c1,;"TLS Auth File";connectretrymaxmessage,57,V401a2b445,de01a2b51f,;"Enter the maximum time between connection attempts. OpenVPN will slowly raise its waiting time after an unsuccessful connection attempt up to this value. Defaults to 300s.";permission_description,0,V40021074c,58002107a0,;"Allows another app to control OpenVPN";add_profile,57,V4008d2f15,33008d2f44,;"add Profile";ip_not_cidr,57,V4006b20ec,c6006b21ae,;"Got interface information %1$s and %2$s\, assuming second address is peer address of remote. Using /32 netmask for local IP. Mode given by OpenVPN is \"%3$s\".";persisttun_summary,57,V400dc5c9d,7100dc5d0a,;"Do not fallback to no VPN connection when OpenVPN is reconnecting.";battery_consumption_title,57,V400e05dca,4900e05e0f,;"Battery consumption";defaultport,0,V40012030f,410012034c,;"1194";onbootrestartsummary,57,V400cd504e,cd00cd5117,;"OpenVPN will connect the specified VPN if it was active on system boot. Please read the connection warning FAQ before using this option on Android < 5.0.";ipv4_address,57,V4002007ab,35002007dc,;"IPv4 Address";mbits_per_second,57,V401b7bb0d,3801b7bb41,;"%.1f Mbit/s";reread_log,57,V40197af4b,500197af97,;"Reread (%d) log items from log cache file";reconnect,57,V4019eb378,2f019eb3a3,;"Reconnect";extracahint,57,V400b040e7,ad00b04190,;"The CA cert is usually returned from the Android keystore. Specify a separate certificate if you get certificate verification errors.";import_done,57,V40091310c,4100913149,;"Done reading config file.";owner_fix_summary,57,V400a83e03,a500a83ea4,;"Tries to set the owner of /dev/tun to system. Some CM9 images need this to make the VPNService API work. Requires root.";state_user_vpn_password,0,V400390b56,6600390bb8,;"Waiting for user VPN password";file_nothing_selected,57,V4001b0615,48001b0659,;"You must select a file";Use_no_proxy,57,V401d7c648,2d01d7c671,;"None";timestamp_short,57,V401307da5,3101307dd2,;"Short";address,57,V400060107,3300060136,;"Server Address\:";save_password,57,V4011d76ef,37011d7722,;"Save Password";copyright_blinktgui,0,V4000700d4,d00080150,;"Copyright 2012–2018 Arne Schwabe <<EMAIL>>";custom_routes_title_excluded,57,V4005a1bc2,4a005a1c08,;"Excluded Networks";summary_block_address_families,57,V401ecd18b,af01ecd236,;"This option instructs Android to not allow protocols (IPv4/IPv6) if the VPN does not set any IPv4 or IPv6 addresses.";sorted_lru,57,V401a9b7a4,4c01a9b7ec,;"Profiles sorted by last recently used";openvpn3_nostatickeys,57,V401d4c494,a501d4c535,;"The OpenVPN 3 C\+\+ implementation does not support static keys. Please change to OpenVPN 2.x under general settings.";remove_vpn_query,57,V400601d59,4b00601da0,;"Remove the VPN Profile '%s'?";faq_howto_title,57,V400c54da5,3700c54dd8,;"Quick Start";state_screenoff,57,V4012177cf,430121780e,;"VPN paused - screen off";connectretrywait,57,V400e764fe,4800e76542,;"Seconds between connections";state_assign_ip,57,V400f26839,4200f26877,;"Assigning IP addresses";state_reconnecting,57,V400f66927,3b00f6695e,;"Reconnecting";channel_name_userreq,57,V401e0cb10,4a01e0cb56,;"User interaction required";copyright_bouncycastle,0,V4002307fe,d002508a2,;"Copyright © 2000–2012 The Legion Of The Bouncy Castle (http\://www.bouncycastle.org)";file_select,57,V4001a05e5,2f001a0610,;"Select…";keychain_access,57,V4006e2289,133006e23b8,;"Cannot access the Android Keychain Certificates. This can be caused by a firmware upgrade or by restoring a backup of the app/app settings. Please edit the VPN and reselect the certificate under basic settings to recreate the permission to access the certificate.";rdn,57,V4010f704a,31010f7077,;"RDN (common name)";make_selection_inline,57,V401096eb3,4301096ef2,;"Copy into profile";ab_persist_tun_title,57,V4017ba239,41017ba276,;"Persist tun mode";faq_remote_api_title,57,V401dbc7a7,3b01dbc7de,;"Remote API";tor_orbot,57,V401d8c676,3101d8c6a3,;"Tor (Orbot)";apprest_name,0,V400461018,2d00461041,;"Name";no_error_found,57,V4002d0c0a,39002d0c3f,;"No error found";apprest_vpnconf,0,V40048108d,3d004810c6,;"VPN configuration";permission_icon_app,57,V4012879a5,5d012879fe,;"Icon of app trying to use OpenVPN for Android";default_route_summary,57,V400551993,54005519e3,;"Redirects all Traffic over the VPN";ipv4_dialog_title,57,V4001f0741,69001f07a6,;"Enter IPv4 Address/Netmask in CIDR Format (e.g. *******/24)";add,57,V4010b6f27,23010b6f46,;"Add";lzo,0,V4001e0685,38001e06b9,;"LZO";nought_alwayson_warning,57,V401a5b5e0,10c01a5b6e8,;"If you did not get a VPN confirmation dialog\, you have \"Always on VPN\" enabled for another app. In that case only that app is allowed to connect to a VPN. Check under Settings-> Networks more .. -> VPNS"<![CDATA[If you did not get a VPN confirmation dialog\, you have \\\"Always on VPN\\\" enabled for another app. In that case only that app is allowed to connect to a VPN. Check under Settings-> Networks more .. -> VPNS]]>;vpn_allow_radio,57,V401508a39,5201508a87,;"VPN is used for only for selected apps";no_default_vpn_set,57,V401f2d3b2,7b01f2d429,;"Default VPN not set. Please set the Default VPN before enabling this option.";dns_server,57,V400501813,3100501840,;"DNS Server";graph,57,V401adb900,2701adb923,;"Graph";chipher_dialog_message,57,V400802ae6,8c00802b6e,;"Enter the encryption cipher algorithm used by OpenVPN. Leave empty to use default cipher.";port,57,V40007013b,2d00070164,;"Server Port\:";configuration_changed,57,V400d25234,4700d25277,;"Configuration changed";no_allowed_app,57,V40187a955,9d0187a9ee,;"No allowed app added. Adding ourselves (%s) to have at least one app in the allowed app list to not allow all apps";version_upto,57,V401719f17,3701719f4a,;"%s and earlier";connectretrymessage,57,V400e66497,6600e664f9,;"Number of seconds to wait between connection attempts.";remote_warning,57,V4011471b8,13d011472f1,;"By proceeding\, you are giving the application permission to completely control OpenVPN for Android and to intercept all network traffic.Do NOT accept unless you trust the application. Otherwise\, you run the risk of having your data compromised by malicious software."By proceeding\, you are giving the application permission to completely control OpenVPN for Android and to intercept all network traffic.<b>Do NOT accept unless you trust the application.</b> Otherwise\, you run the risk of having your data compromised by malicious software.\";ab_lollipop_reinstall,57,V401689861,8a016898e7,;"VPN apps may stop working when uninstalled and reinstalled again. For details see #80074";remote_trust,57,V4011572f6,**********,;"I trust this application.";route_rejected,57,V400390f8a,4400390fca,;"Route rejected by Android";configure_the_vpn,57,V4002609ff,3f00260a3a,;"Configure the VPN";show_log,57,V401608e60,2d01608e89,;"Show log";faq_routing,57,V400db5775,52700db5c98,;"The Routing and interface configuration is not done via traditional ifconfig/route commands but by using the VPNService API. This results in a different routing configuration than on other OSes. 
The configuration of the VPN tunnel consists of the IP address and the networks that should be routed over this interface. Especially\, no peer partner address or gateway address is needed or required. Special routes to reach the VPN Server (for example added when using redirect-gateway) are not needed either. The application will consequently ignore these settings when importing a configuration. The app ensures with the VPNService API that the connection to the server is not routed through the VPN tunnel.
The VPNService API does not allow specifying networks that should not be routed via the VPN. As a workaround the app tries to detect networks that should not be routed over tunnel (e.g. route x.x.x.x y.y.y.y net_gateway) and calculates a set of routes that excludes this routes to emulate the behaviour of other platforms. The log windows shows the configuration of the VPNService upon establishing a connection.
Behind the scenes\: Android 4.4\+ does use policy routing. Using route/ifconfig will not show the installed routes. Instead use ip rule\, iptables -t mangle -L";ab_tethering_44,57,V4016595eb,**********,;"Tethering works while the VPN is active. The tethered connection will NOT use the VPN.";error_reading_config_file,57,V4008c2ec5,4f008c2f10,;"Error reading config file";internal_web_view,57,V401f3d42e,3e01f3d468,;"Internal WebView";faq_tap_mode,57,V400752509,**********,;"Tap Mode is not possible with the non root VPN API. Therefore this application cannot provide tap support";ignore,57,V400cf5156,2900cf517b,;"Ignore";volume_byte,57,V401c4bf93,2e01c4bfbd,;"%.0f B";file_icon,57,V400a43d13,2f00a43d3e,;"file icon";no_vpn_profiles_defined,57,V400d65569,4c00d655b1,;"No VPN profiles defined.";copyright_others,57,V400150497,8d00150520,;"This program uses the following components\; see the source code for full details on the licenses";send_minidump,57,V400e965d4,4400e96614,;"Send Minidump to developer";custom_options_title,57,V4005e1ce2,3f005e1d1d,;"Custom Options";send_minidump_summary,57,V400ea6619,6b00ea6680,;"Sends debugging information about last crash to developer";start_vpn_title,57,V401006bad,4001006be9,;"Connecting to VPN %s";pauseVPN,57,V4011e7727,2e011e7751,;"Pause VPN";change_sorting,57,V401a7b744,3901a7b779,;"Change sorting";openurl_requested,57,V401e4cbeb,5501e4cc3c,;"Open URL to continue VPN authentication";getproxy_error,57,V400c94f0c,4b00c94f53,;"Error getting proxy settings\: %s";prompt,57,V401137177,40011371b3,;"%1$s attempts to control %2$s";remote_tlscn_check_title,57,V4004312a9,4f004312f4,;"Certificate Hostname Check";select,57,V4000a01dd,29000a0202,;"Select";add_profile_name_prompt,57,V400280a78,5c00280ad0,;"Enter a name identifying the new Profile";state_waitconnectretry,57,V401a4b57e,6101a4b5db,;"Waiting %ss seconds between connection attempt";advanced,57,V400bf4823,2d00bf484c,;"Advanced";running_test,57,V401d1c36e,3601d1c3a0,;"Running test…";copyright_file_dialog,0,V400130351,77001303c4,;"File Dialog based on work by Alexander Ponomarev";faq_remote_api,57,V401dcc7e3,21001dcc9ef,;"OpenVPN for Android supports two remote APIs\, a sophisticated API using AIDL (remoteEXample in the git repository) and a simple one using Intents. <p>Examples using adb shell and the intents. Replace profilname with your profile name<p><p> adb shell am start-activity -a android.intent.action.MAIN de.blinkt.openvpn/.api.DisconnectVPN<p> adb shell am start-activity -a android.intent.action.MAIN -e de.blinkt.openvpn.api.profileName Blinkt de.blinkt.openvpn/.api.ConnectVPN";no_keystore_cert_selected,57,V4002b0b60,58002b0bb4,;"You must select a User certificate";ip_looks_like_subnet,57,V4014384b3,b801438567,;"Vpn topology \"%3$s\" specified but ifconfig %1$s %2$s looks more like an IP address with a network mask. Assuming \"subnet\" topology.";ip_add_error,57,V400c34a0e,6e00c34a78,;"Could not configure IP Address \"%1$s\"\, rejected by the system\: %2$s";vpnbehaviour,57,V4012578e8,360125791a,;"VPN behaviour";last5minutes,57,V401b2ba15,3701b2ba48,;"Last 5 minutes";copyright_openssl,0,V40016044b,d001b05f6,;"This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit
 Copyright © 1998-2008 The OpenSSL Project. All rights reserved.

 This product includes cryptographic software written by Eric Young (<EMAIL>)
 Copyright © 1995-1998 Eric Young (<EMAIL>) All rights reserved.";profilename,57,V4002a0b2b,34002a0b5b,;"Profile Name";screenoff_pause,57,V4011b75d6,6b011b763d,;"Pausing connection in screen off state\: less than %1$s in %2$ss";dns1_summary,57,V4004f17d3,3f004f180e,;"DNS Server to be used.";pushpeerinfo,57,V4018dac2a,37018dac5d,;"Push Peer info";client_pkcs12_title,57,V40011035d,3b00110394,;"PKCS12 File";import_log,57,V401428480,32014284ae,;"Import log\:";channel_description_userreq,57,V401e1cb5b,d01e3cbe6,;"OpenVPN connection requires a user input\, e.g. two factor authentification";mtu_invalid_value,57,V4014585db,6c01458643,;"The MTU override value has to be a integer between 64 and 9000";help_translate,57,V4011270f8,7e01127172,;"You can help translating by visiting https\://crowdin.net/project/ics-openvpn/invite";state_user_vpn_permission_cancelled,0,V4003b0c34,78003b0ca8,;"VPN API permission dialog cancelled";route_not_cidr,57,V4006c21b3,82006c2231,;"Cannot make sense of %1$s and %2$s as IP route with CIDR netmask\, using /32 as netmask.";faq_howto,57,V400c44a7d,32700c44da0,;"<p>Get a working config (tested on your computer or download from your provider/organisation)</p><p>If it is a single file with no extra pem/pkcs12 files you can email the file yourself and open the attachment. If you have multiple files put them on your sd card.</p><p>Click on the email attachment/Use the folder icon in the vpn list to import the config file</p><p>If there are errors about missing files put the missing files on your sd card.</p><p>Click on the save symbol to add the imported VPN to your VPN list</p><p>Connect the VPN by clicking on the name of the VPN</p><p>If there are error or warnings in the log try to understand the warnings/error and try to fix them</p>";dns_override_summary,57,V4004d1753,49004d1798,;"Use your own DNS Servers";connection_retries,57,V400e4640d,4100e4644a,;"Connection retries";extauth_not_configured,57,V401e9cd5e,5801e9cdb2,;"External Authneticator not configured";dns,57,V4004b16e6,23004b1705,;"DNS";faq_vpndialog43,57,V401297a03,1ef01297bee,;"Starting with Android 4.3 the VPN confirmation is guarded against \"overlaying apps\". This results in the dialog not reacting to touch input. If you have an app that uses overlays it may cause this behaviour. If you find an offending app contact the author of the app. This problem affect all VPN applications on Android 4.3 and later. See also <a href=\"https\://github.com/schwabe/ics-openvpn/issues/185\">Issue 185<a> for additional details";state_get_config,57,V400f167ef,4900f16834,;"Getting client configuration";loading,57,V4014b889a,2c014b88c2,;"Loading…";kbits_per_second,57,V401b6bad4,3801b6bb08,;"%.1f kbit/s";import_error_message,57,V400862cf6,5600862d48,;"Could not import File from filesystem";deprecated_tls_remote,57,V401abb82f,8501abb8b0,;"Config uses option tls-remote that was deprecated in 2.3 and finally removed in 2.4";import_from_as,57,V401f1d365,4c01f1d3ad,;"Import Profile from Access Server";apprest_ovpn,0,V400440fa2,2f00440fcd,;"Config";building_configration,57,V400ac3f8b,4900ac3fd0,;"Building configuration…";notifcation_title_notconnect,57,V400ff6b66,4600ff6ba8,;"Not connected";auth_pwquery,57,V400240955,3100240982,;"Password";state_tcp_connect,57,V400fa6a0c,3e00fa6a46,;"Connecting (TCP)";vpn_allowed_apps,57,V401598ca8,3901598cdd,;"Allowed Apps";app_no_longer_exists,57,V4014e895f,7c014e89d7,;"Package %s is no longer installed\, removing it from app allow/disallow list";ipv4_format_error,57,V4002f0c8a,4c002f0cd2,;"Error parsing the IPv4 address";logCleared,57,V4012d7cef,33012d7d1e,;"Log cleared.";config_error_found,57,V4002e0c44,45002e0c85,;"Error in Configuration";custom_config_summary,57,V400380f31,5800380f85,;"Specify custom options. Use with care!";translationby,57,V400ba46e9,6500ba474a,;"English translation by Arne Schwabe<<EMAIL>>";error_rsa_sign,57,V400b643a2,5d00b643fb,;"Error signing with Android keystore key %1$s\: %2$s";enableproxyauth,57,V401ddc9f4,4701ddca37,;"Enable Proxy Authentication";no_ca_cert_selected,57,V4002c0bb9,50002c0c05,;"You must select a CA certificate";packet_auth,57,V401046d85,3d01046dbe,;"Packet authentication";vpn_list_title,57,V40017054d,330017057c,;"Profiles";faq_routing_title,57,V400da5727,4d00da5770,;"Routing/Interface Configuration";static_keys_info,57,V400250987,77002509fa,;"For the static configuration the TLS Auth Keys will be used as static keys";timestamps_none,57,V401337e37,3001337e63,;"None";full_licenses,57,V4013b8110,37013b8143,;"Full licenses";enter_tlscn_title,57,V4004514f9,480045153d,;"Remote certificate subject";custom_route_message_excluded,57,V400581aef,9400581b7f,;"Routes that should NOT be routed over the VPN. Use the same syntax as for included routes.";tap_mode,57,V4007424db,2d00742504,;"Tap Mode";auth_dialog_message,57,V400812b73,8400812bf3,;"Enter the authentication digest used for OpenVPN. Leave empty to use default digest.";backup_dns,57,V4005218bd,38005218f1,;"Backup DNS Server";tun_error_helpful,57,V400611da5,e400611e85,;"On some custom ICS images the permission on /dev/tun might be wrong\, or the tun module might be missing completely. For CM9 images try the fix ownership option under general settings";cancel_connection_long,57,V4003b1008,41003b1045,;"Disconnect VPN";userpw_file,57,V4013e8271,3e013e82ab,;"Username/Password file";inline_file_tab,57,V400842c78,3700842cab,;"Inline File";ignore_multicast_route,57,V4016394c2,4f0163950d,;"Ignoring multicast route\: %s";mssfix_invalid_value,57,V40144856c,6e014485d6,;"The MSS override value has to be a integer between 0 and 9000";ab_vpn_reachability_44,57,V4016d9da6,9f016d9e41,;"Only destination can be reached over the VPN that are reachable without VPN. IPv6 VPNs does not work at all.";apprest_ver,0,V4004910cb,680049112f,;"Version of the managed configuration schema (Currently always 1)";bits_per_second,57,V401b5ba9d,3601b5bacf,;"%.0f bit/s";no_external_app_allowed,57,V401167339,560116738b,;"No app allowed to use external API";apprest_ovpn_desc,0,V400430e1e,18300430f9d,;"Content of the OpenVPN configuration file. These files are usually have the extension .ovpn (sometimes also .conf) and are plain text multi line configuration files. If your MDM does not support multiline configuration entries\, you can also use a base64 encoded string here. A text file can be converted to base64 using openssl base64 -A -in";broken_image_cert,57,V400993645,10200993743,;"Got an exception trying to show the Android 4.0\+ certificate selection dialog. This should never happen as this a standard feature of Android 4.0\+. Maybe your Android ROM support for certificate storage is broken";copied_entry,57,V400732493,47007324d6,;"Copied log entry to clip board";allow_vpn_changes,57,V40126791f,4b01267966,;"Allow changes to VPN Profiles";faq_tethering,57,V400e262b7,11300e263c6,;"The Android Tethering feature (over WiFi\, USB or Bluetooth) and the VPNService API (used by this program) do not work together. For more details see the <a href=\"https\://github.com/schwabe/ics-openvpn/issues/34\">issue #34</a>";clear_external_apps,57,V4014a884e,4b014a8895,;"Clear allowed external apps";query_permissions_sdcard,57,V40188a9f3,bc0188aaab,;"OpenVPN for Android can try to discover the missing file(s) on the sdcard automatically. Tap this message start the permission request.";pw_request_dialog_title,57,V4018facdd,3d018fad16,;"Need %1$s";copyright_logo,0,V400090155,d000b01de,;"App Logo design by Helen Beierling <<EMAIL>>";faq_duplicate_notification_title,57,V400d452df,5400d4532f,;"Duplicate notifications";screenoff_summary,57,V401197440,1430119757f,;"Pause VPN when screen is off and less than 64 kB transferred data in 60s. When the \"Persistent Tun\" option is enabled pausing the VPN will leave your device with NO network connectivity. Without the \"Persistent Tun\" option the device will have no VPN connection/protection.";use_logarithmic_scale,57,V401aeb928,4701aeb96b,;"Use logarithmic scale";no_remote_defined,57,V4015d8d94,3f015d8dcf,;"No remote defined";channel_name_status,57,V401cac113,4801cac157,;"Connection status change";export_config_title,57,V400c04851,4200c0488f,;"ICS Openvpn Config";owner_fix,57,V400a93ea9,3f00a93ee4,;"Fix ownership of /dev/tun";statusline_bytecount,57,V400fe6b1d,4800fe6b61,;"↓%2$s %1$s - ↑%4$s %3$s";add_remote,57,V401558b72,3501558ba3,;"Add new remote";state_user_vpn_permission,0,V400380ae0,7500380b51,;"Waiting for user permission to use VPN API";samsung_broken_title,57,V40199b1dd,3f0199b218,;"Samsung phones";sort,57,V401a8b77e,2501a8b79f,;"Sort";ab_secondary_users,57,V4016b9c14,5c016b9c6c,;"VPN does not work at all for secondary users.";notifcation_title,57,V400eb6685,3f00eb66c0,;"5G SMART VPN - %s";ab_proxy_title,57,V4016f9e85,43016f9ec4,;"Proxy behaviour for VPNs";check_remote_tlscert_title,57,V4004111ed,540041123d,;"Expect TLS server certificate";basic,57,V400bc477c,2700bc479f,;"Basic";volume_kbyte,57,V401c5bfc2,3001c5bfee,;"%.1f kB";faq_androids_clients_title,57,V40162945b,66016294bd,;"Differences between the OpenVPN Android clients";install_keychain,57,V401eed2a0,4401eed2e0,;"Install new certificate";imported_from_file,57,V4013f82b0,42013f82ee,;"[Imported from\: %s]";osslspeedtest,57,V401cfc2e6,4301cfc325,;"OpenSSL Crypto Speed test";routes_info_incl,57,V400682015,3e0068204f,;"Routes\: %1$s %2$s";novpn_selected,57,V4019ab21d,3b019ab254,;"No VPN selected.";screenoff_title,57,V4011a7584,51011a75d1,;"Pause VPN connection after screen off";avghour,57,V401b0b9aa,3401b0b9da,;"Average per hour";client_no_certificate,57,V4000e0290,40000e02cc,;"No Certificate";custom_routes_title,57,V400591b84,3d00591bbd,;"Custom Routes";ipv6_address,57,V4002107e1,3500210812,;"IPv6 Address";vpn_allow_bypass,57,V401518a8c,4901518ad1,;"Allow apps to bypass the VPN";speed_waiting,57,V4009c3794,44009c37d4,;"Waiting for state message…";client_key_title,57,V400100319,4300100358,;"Client Certificate Key";duplicate_profile_title,57,V4015f8e14,4b015f8e5b,;"Duplicating profile\: %s";delete,57,V401548b48,2901548b6d,;"Delete";override_dns,57,V4004c170a,48004c174e,;"Override DNS Settings by Server";all_app_prompt,57,V401d3c3ea,a901d3c48f,;"An external app tries to control %s. The app requesting access cannot be determined. Allowing this app grants ALL apps access.";routes_info_excl,57,V400692054,4700692097,;"Routes excluded\: %1$s %2$s";test_algoirhtms,57,V401d2c3a5,4401d2c3e5,;"Test selected algorithms";menu_add_profile,57,V400270a3f,3800270a73,;"Add Profile";ipv4,57,V4009a3748,25009a3769,;"IPv4";state_disconnected,57,V400f568ed,3900f56922,;"Disconnect";ipv6,57,V4009b376e,25009b378f,;"IPv6";crtext_requested,57,V401e5cc41,5c01e5cc99,;"Answer challenge to continue VPN authentication";inline_file_data,57,V400872d4d,4100872d8a,;"[[Inline file data]]";pkcs12pwquery,57,V4001905ab,39001905e0,;"PKCS12 Password";auth_dialog_title,57,V401056dc3,5001056e0f,;"Enter packet authentication method";show_password,57,V4012e7d23,37012e7d56,;"Show password";error_extapp_sign,57,V400b74400,6e00b7446a,;"Error signing with external authenticator app (%3$s)\: %1$s\: %2$s";log_no_last_vpn,57,V400d3527c,6200d352da,;"Could not determine last connected profile for editing";ipv6_dialog_tile,57,V4001e06d4,6c001e073c,;"Enter IPv6 Address/Netmask in CIDR Format (e.g. 2000\:dd\:\:23/64)";remote_tlscn_check_summary,57,V400421242,66004212a4,;"Checks the Remote Server Certificate Subject DN";nobind_summary,57,V40092314e,500092319a,;"Do not bind to local address and port";connectretrymaxtitle,57,V401a3b524,5901a3b579,;"Maximum time between connection attempts";management_socket_closed,57,V401a6b6ed,5601a6b73f,;"Connection to OpenVPN closed (%s)";notenoughdata,57,V401afb970,3901afb9a5,;"Not enough data";state_connected,57,V400f468b7,3500f468e8,;"Connected";faq_system_dialogs_title,57,V400b9468a,5e00b946e4,;"Connection warning and notification sound";last_openvpn_tun_config,57,V400651f2a,4a00651f70,;"Opening tun interface\:";appbehaviour,57,V4012478a9,3e012478e3,;"Application behaviour";error_importing_file,57,V400852cb0,4500852cf1,;"Error importing File";tls_settings,57,V4015c8d5e,35015c8d8f,;"TLS Settings";about,57,V400160525,2700160548,;"About";local_ip_info,57,V400661f75,5400661fc5,;"Local IPv4\: %1$s/%2$d IPv6\: %3$s MTU\: %4$d";select_file,57,V400b14195,2e00b141bf,;"Select";state_userpause,57,V401207787,47012077ca,;"VPN pause requested by user";ab_kitkat_mss_title,57,V401749fdf,520174a02d,;"Wrong MSS value for VPN connection";password,57,V400a33ce5,2d00a33d0e,;"Password";state_wait,57,V400ef6779,3f00ef67b4,;"Waiting for server reply";unknown_state,0,V4002006ff,4c00200747,;"Unknown state";clear_log,57,V4003c104a,2f003c1075,;"clear log";check_remote_tlscert,57,V400401159,93004011e8,;"Checks whether the server uses a certificate with TLS Server extensions (--remote-cert-tls server)";tls_remote_deprecated,57,V4011170ae,49011170f3,;"tls-remote (DEPRECATED)";send_config,57,V4010c6f4b,38010c6f7f,;"Send config file";broken_image_cert_title,57,V4009835ed,5700983640,;"Error showing certificate selection";minidump_generated,57,V400e86547,8c00e865cf,;"OpenVPN crashed unexpectedly. Please consider using the send Minidump option in the main menu";app,57,V4000500d3,3300050102,;"OpenVPN for Android";openvpn3_socksproxy,57,V401d9c6a8,7801d9c71c,;"OpenVPN 3 C\+\+ implementation does not support connecting via Socks proxy";allowed_vpn_apps_info,57,V4014c88c7,48014c890b,;"Allowed VPN apps\: %1$s";state_exiting,57,V400f76963,3100f76990,;"Exiting";pw_request_dialog_prompt,57,V40190ad1b,5f0190ad76,;"Please enter the password for profile %1$s";state_noprocess,57,V400f86995,3700f869c8,;"Not running";version_and_later,57,V4017ca27b,3a017ca2b1,;"%s and later";unhandled_exception_context,57,V401397f8b,4a01397fd1,;"%3$s\: %1$s

%2$s";secondary_dns_message,57,V400511845,77005118b8,;"Secondary DNS Server used if the normal DNS Server cannot be reached.";resumevpn,57,V4011f7756,30011f7782,;"Resume VPN";onbootrestart,57,V400ce511c,3900ce5151,;"Connect on boot";state_add_routes,57,V400f3687c,3a00f368b2,;"Adding routes";encryption_cipher,57,V401036d45,3f01036d80,;"Encryption cipher";no_vpn_support_image,57,V4007d2a02,69007d2a67,;"Your image does not support the VPNService API\, sorry \:(";no_bind,57,V40093319f,34009331cf,;"No local binding";keychain_nocacert,57,V400b241c4,9000b24250,;"No CA Certificate returned while reading from Android keystore. Authentication will probably fail.";export_config_chooser_title,57,V40192adbc,510192ae09,;"Export configuration file";log_verbosity_level,57,V4005b1c0d,43005b1c4c,;"Log verbosity level";float_summary,57,V4005c1c51,52005c1c9f,;"Allows authenticated packets from any IP";import_warning_custom_options,57,*********e,fd00903107,;"Your configuration had a few configuration options that are not mapped to UI configurations. These options were added as custom configuration options. The custom configuration is displayed below\:";copying_log_entries,57,V4007927e1,4300792820,;"Copying log entries";faq_duplicate_notification,57,V400d55334,23400d55564,;"If Android is under system memory (RAM) pressure\, apps and service which are not needed at the moment are removed from active memory. This terminates an ongoing VPN connection. To ensure that the connection/OpenVPN survives the service runs with higher priority. To run with higher priority the application must display a notification. The key notification icon is imposed by the system as described in the previous FAQ entry. It does not count as app notification for purpose of running with higher priority.";routing,57,V400bd47a4,2b00bd47cb,;"Routing";encryption,57,V4007e2a6c,31007e2a99,;"Encryption";custom_route_message,57,V400571a28,c600571aea,;"Enter custom routes. Only enter destination in CIDR format. \"10.0.0.0/8 2002\:\:/16\" would direct the networks 10.0.0.0/8 and 2002\:\:/16 over the VPN.";custom_route_format_error,57,V400300cd7,5500300d28,;"Error parsing the custom routes";dns_add_error,57,V400c249a4,6900c24a09,;"Could not add DNS Server \"%1$s\"\, rejected by the system\: %2$s";tls_authentication,57,V400a53d43,4c00a53d8b,;"TLS Authentication/Encryption";show_log_window,57,V400b442e7,3b00b4431e,;"Show log window";import_config,57,V400df5d83,4600df5dc5,;"Import OpenVPN configuration";hwkeychain,57,V40127796b,39012779a0,;"Hardware Keystore\:";channel_description_background,57,V401c9c09f,7301c9c10e,;"Ongoing statistics of the established OpenVPN connection";version_info,57,V4006f23bd,32006f23eb,;"%1$s %2$s";info_from_server,57,V401dfcaca,4501dfcb0b,;"Info from server\: '%s'";opentun_no_ipaddr,57,V400882d8f,6000882deb,;"Refusing to open tun device without IP information";random_host_summary,57,V400360e92,5800360ee6,;"Adds 6 random chars in front of hostname";title_cancel,57,V4003d107a,3c003d10b2,;"Cancel Confirmation";vpn_disallow_radio,57,V4014f89dc,5c014f8a34,;"VPN is used for all apps but exclude selected";volume_gbyte,57,V401c7c024,3001c7c050,;"%.1f GB";ics_openvpn_log_file,57,V40072244d,450072248e,;"ICS OpenVPN log file";avgmin,57,V401b1b9df,3501b1ba10,;"Average per minute";vpnselected,57,V4019db331,46019db373,;"Currently selected VPN\: '%s'";private_key_password,57,V400a23c9f,4500a23ce0,;"Private Key Password";client_certificate_title,57,V4000f02d1,47000f0314,;"Client Certificate";state_resolve,57,V400f969cd,3e00f96a07,;"Resolving host names";rdn_prefix,57,V40110707c,31011070a9,;"RDN prefix";mssfix_dialogtitle,57,V4014887ca,450148880b,;"Set MSS of TCP payload";state_nonetwork,57,V400fc6a8f,4600fc6ad1,;"Waiting for usable network";connect_timeout,57,V40186a919,3b0186a950,;"Connect Timeout";remote_no_server_selected,57,V401578c04,6f01578c6f,;"You need to define and enable at least one remote server.";ab_not_route_to_vpn,57,V4016998ec,f0016999d8,;"The configured client IP and the IPs in its network mask are not routed to the VPN. OpenVPN works around this bug by explicitly adding a route that corrosponds to the client IP and its netmask";mssfix_value_dialog,57,V401468648,13301468777,;"Announce to TCP sessions running over the tunnel that they should limit their send packet sizes such that after OpenVPN has encapsulated them\, the resulting UDP packet size that OpenVPN sends to its peer will not exceed this number of bytes. (default is 1450)";tls_cipher_alert_title,57,V4017da2b6,7e017da330,;"Connections fails with SSL23_GET_SERVER_HELLO\:sslv3 alert handshake failure";unhandled_exception,57,V401387f39,5101387f86,;"Unhandled exception\: %1$s

%2$s";openssl_cipher_name,57,V401cec2a1,4401cec2e1,;"OpenSSL cipher names";copyright_guicode,57,V400140416,8000140492,;"Source code and issue tracker available at https\://github.com/schwabe/ics-openvpn/";weakmd_title,57,V401ccc1db,7901ccc250,;"Weak (MD5) hashes in certificate signature (SSL_CTX_use_certificate md too weak)";file_dialog,0,V4001d063c,48001d0680,;"File Dialog";Search,57,V40183a84d,300183a879,;"Search Server";defaultvpnsummary,57,V4019cb28c,a4019cb32c,;"VPN used in places where a default VPN needed. These are currently on boot\, for Always-On and the Quick Settings Tile.";title_block_address_families,57,V401edd23b,6401edd29b,;"Block IPv6 (or IPv4) if not used by the VPN";uploaded_data,57,V401347e68,3001347e94,;"Upload";pushpeerinfosummary,57,V4018eac62,7a018eacd8,;"Send extra information to the server\, e.g. SSL version and Android version";title_activity_open_sslspeed,57,V401cdc255,4b01cdc29c,;"OpenSSL Speed Test";allowed_apps,57,V401177390,39011773c5,;"Allowed apps\: %s";no_data,57,V4000c0231,2b000c0258,;"No Data";faq_copying,57,V4007a2825,de007a28ff,;"To copy a single log entry press and hold on the log entry. To copy/send the whole log use the Send Log option. Use the hardware menu button\, if the button is not visible in the GUI.";reconnection_settings,57,V400e5644f,4700e56492,;"Reconnection settings";donatePlayStore,57,V4012b7c3d,69012b7ca2,;"Alternatively you can send me a donation with the Play Store\:";complete_dn,57,V4010d6f84,33010d6fb3,;"Complete DN";apprest_name_desc,0,V400450fd2,**********,;"Name of the VPN profile";broken_images_faq,57,V400a03894,3b800a03c48,;"<p>Official HTC images are known to have a strange routing problem causing traffic not to flow through the tunnel (See also <a href=https\://github.com/schwabe/ics-openvpn/issues/18>Issue 18</a> in the bug tracker.)</p><p>Older official SONY images from Xperia Arc S and Xperia Ray have been reported to be missing the VPNService completely from the image. (See also <a href=https\://github.com/schwabe/ics-openvpn/issues/29>Issue 29</a> in the bug tracker.)</p><p>On custom build images the tun module might be missing or the rights of /dev/tun might be wrong. Some CM9 images need the Fix ownershipoption under Device specific hacksenabled.</p><p>Most importantly\: If your device has a broken Android image\, report it to your vendor. The more people who report an issue to the vendor\, the more likely they are to fix it.</p>";obscure,57,V400be47d0,5200be481e,;"Obscure OpenVPN Settings. Normally not needed.";lzo_copyright,0,V4001403c9,d00150446,;"Copyright © 1996 – 2011 Markus Franz Xaver Johannes Oberhumer";duplicate_vpn,57,V4015e8dd4,3f015e8e0f,;"Duplicate VPN profile";edit_vpn,57,V4005f1d22,36005f1d54,;"Edit VPN Settings";faq_howto_shortcut,57,V4007c293f,c2007c29fd,;"You can place a shortcut to start OpenVPN on your desktop. Depending on your homescreen program you will have to add either a shortcut or a widget.";missing_ca_certificate,57,V40195aeb9,490195aefe,;"Missing CA certificate";remotetlsnote,57,V4010e6fb8,91010e7045,;"Your imported configuration used the old DEPRECATED tls-remote option which uses a different DN format.";use_system_proxy_summary,57,V400cc4fd4,7900cc5049,;"Use the system wide configuration for HTTP/HTTPS proxies to connect.";crl_file,57,V40180a720,400180a75c,;"Certificate Revocation List";import_config_error,57,V40182a7e7,650182a848,;"Importing the config yielded an error\, cannot save it";default_cipherlist_test,0,V4003c0cad,60003c0d09,;"aes-256-gcm bf-cbc sha1";state_user_vpn_password_cancelled,0,V4003a0bbd,76003a0c2f,;"VPN password input dialog cancelled";device_specific,57,V401227813,4201227851,;"Device specifics Hacks";volume_mbyte,57,V401c6bff3,3001c6c01f,;"%.1f MB";server_list,57,V401588c74,3301588ca3,;"Server List";abi_mismatch,57,V4018bab1b,93018babaa,;"Preferred native ABI precedence of this device (%1$s) and ABI reported by native libraries (%2$s) mismatch";openvpn,0,V4001c05fb,40001c0637,;"OpenVPN";use_pull,57,V4004a16b3,32004a16e1,;"Pull Settings";samsung_broken,57,V40198af9c,2400198b1d8,;"Even though Samsung phones are among the most selling Android phones\, Samsung's firmware are also among the most buggy Android firmwares. The bugs are not limited to the VPN operation on these devices but many of them can be workarounded. In the following some of these bugs are described.

DNS does not work unless the DNS server in the VPN range.

On many Samsung 5.x devices the allowed/disallowed apps feature does not work.
On Samsung 6.x VPN is reported not to work unless the VPN app is exempted from Powersave features.";request_autologin,57,V401f0d31d,4701f0d360,;"Request autologin profile";gbits_per_second,57,V401b8bb46,3801b8bb7a,;"%.1f Gbit/s";configure,57,V401e8cd2e,2f01e8cd59,;"Configure";custom_connection_options_warng,57,V40176a07e,750176a0ef,;"Specify custom connection specific options. Use with care";custom_option_warning,57,V400220817,10a0022091d,;"Enter custom OpenVPN options. Use with caution. Also note that many of the tun related OpenVPN settings cannot be supported by design of the VPNSettings. If you think an important option is missing contact the author";ab_secondary_users_title,57,V40175a032,4b0175a079,;"Secondary tablet users";faq_killswitch,57,V401ebce12,37801ebd186,;"It is often desired to block connections without VPN. Other apps often use markting terms like \"Killswitch\" or \"Seamless tunnel\" for this feature. OpenVPN and this app offer persist-tun\, a feature to implement this functionality.<p>The problem with all these methods offered by apps is that they can only provide best effort and are no complete solutions. On boot\, app crashing and other corner cases the app cannot ensure that this block of non VPN connection works. Thus giving the user a false sense of security.<p>The <b>only</b> reliable way to ensure non VPN connections are blocked is to use Android 8.0 or later and use the \"block connections without VPN\" setting that can be found under Settings > Network & Internet > Advanced/VPN > OpenVPN for Android > Enable Always ON VPN\, Enable Block Connections without VPN";state_auth,57,V400f067b9,3500f067ea,;"Authenticating";blocklocal_summary,57,V4013c8148,dd013c8221,;"Networks directly connected to the local interfaces will not be routed over the VPN. Deselecting this option will redirect all traffic indented for local networks to the VPN.";downloaded_data,57,V401357e99,3401357ec9,;"Download";routes_debug,57,V4006a209c,4f006a20e7,;"VpnService routes installed\: %1$s %2$s";generated_config_summary,57,V400aa3ee9,6300aa3f48,;"Shows the generated OpenVPN Configuration File";advanced_settings,57,V4015a8ce2,3f015a8d1d,;"Advanced Settings";faq_system_dialogs,57,V400b8446f,21a00b84685,;"The VPN connection warning telling you that this app can intercept all traffic is imposed by the system to prevent abuse of the VPNService API.
The VPN connection notification (The key symbol) is also imposed by the Android system to signal an ongoing VPN connection. On some images this notification plays a sound.
Android introduced these system dialogs for your own safety and made sure that they cannot be circumvented. (On some images this unfortunately includes a notification sound)";menu_import,57,V400892df0,4500892e31,;"Import Profile from ovpn file";location,57,V400080169,2d00080192,;"Location";vpn_import_hint,57,V400d85622,9e00d856bc,;"Use the <img src=\"ic_menu_archive\"/> icon to import an existing (.ovpn or .conf) profile from your sdcard.";as_servername,57,V401efd2e5,3701efd318,;"AS servername";ab_persist_tun,57,V4016a99dd,236016a9c0f,;"Opening a tun device while another tun device is active\, which is used for persist-tun support\, crashes the VPNServices on the device. A reboot is required to make VPN work again. OpenVPN for Android tries to avoid reopening the tun device and if really needed first closes the current TUN before opening the new TUN device to avoid to crash. This may lead to a short window where packets are sent over the non-VPN connection. Even with this workaround the VPNServices sometimes crashes and requires a reboot of the device.";ab_only_cidr,57,V401649512,d8016495e6,;"Android supports only CIDR routes to the VPN. Since non-CIDR routes are almost never used\, OpenVPN for Android will use a /32 for routes that are not CIDR and issue a warning.";pull_on_summary,57,V4004815c6,6d0048162f,;"Requests IP addresses\, routes and timing options from the server.";query_delete_remote,57,V401528ad6,4b01528b1d,;"Remove remote server entry?";openvpn_is_no_free_vpn,57,V401418370,10f0141847b,;"To use this app you need a VPN provider/VPN gateway supporting OpenVPN (often provided by your employer). Check out https\://community.openvpn.net/ for more information on OpenVPN and how to setup your own OpenVPN server.";setting_loadtun,57,V400c74e66,3b00c74e9d,;"Load tun module";cannotparsecert,57,V401237856,52012378a4,;"Cannot display certificate information";defaultserver,0,V4001102b7,570011030a,;"openvpn.uni-paderborn.de";faq,57,V4007827bd,23007827dc,;"FAQ";service_restarted,57,V40181a761,850181a7e2,;"Restarting OpenVPN Service (App crashed probably crashed or killed for memory pressure)";netchange_summary,57,V400ad3fd5,9600ad4067,;"Turning this option on will force a reconnect if the network state is changed (e.g. WiFi to/from mobile)";lastdumpdate,57,V40184a87e,4d0184a8c7,;"(Last dump is %1$d\:%2$dh old (%3$s))";generated_config,57,V400a63d90,3d00a63dc9,;"Generated Config";ab_vpn_reachability_44_title,57,V4017aa1e2,56017aa234,;"Remote networks not reachable";faq_hint,57,V400d956c1,6500d95722,;"Be sure to also check out the FAQ. There is a quick start guide.";permission_revoked,57,V4018cabaf,7a018cac25,;"VPN permission revoked by OS (e.g. other VPN program started)\, stopping VPN";import_could_not_open,57,V4008e2f49,70008e2fb5,;"Could not find file %1$s mentioned in the imported config file";screen_nopersistenttun,57,V4011c7642,ac011c76ea,;"Warning\: Persistent tun not enabled for this VPN. Traffic will use the normal Internet connection when the screen is off.";apprest_vpnlist,0,V400471046,4600471088,;"List of VPN configurations";copy_of_profile,57,V401729f4f,3601729f81,;"Copy of %s";pkcs12_file_encryption_key,57,V400a13c4d,5100a13c9a,;"PKCS12 File Encryption Key";setting_loadtun_summary,57,V400c64ddd,8800c64e61,;"Try to load the tun.ko kernel module before trying to connect. Needs rooted devices.";show_log_summary,57,V400b34255,9100b342e2,;"Shows the log window on connect. The log window can always be accessed from the notification status.";state_connecting,57,V400ee6741,3700ee6774,;"Connecting";openssl_error,57,V401d0c32a,4301d0c369,;"OpenSSL returned an error";converted_profile_i,57,V4009e3818,43009e3857,;"imported profile %d";ca_title,57,V400120399,33001203c8,;"CA Certificate";tap_faq2,57,V40076259c,a50076263d,;"Again? Are you kidding? No\, tap mode is really not supported and sending more mail asking if it will be supported will not help.";tap_faq3,57,V400772642,17a007727b8,;"A third time? Actually\, one could write a tap emulator based on tun that would add layer2 information on send and strip layer2 information on receive. But this tap emulator would also have to implement ARP and possibly a DHCP client. I am not aware of anybody doing any work in this direction. Contact me if you want to start coding on this.";openssl,0,V4001f06be,40001f06fa,;"OpenSSL";remove_vpn,57,V4003f1127,31003f1154,;"Remove VPN";opevpn_copyright,0,V4000d01e4,d001002b2,;"Copyright © 2002–2010 OpenVPN Technologies\, Inc. <<EMAIL>>
 OpenVPNis a trademark of OpenVPN Technologies\, Inc.
";files_missing_hint,57,V4014082f3,7c0140836b,;"Some files could not be found. Please select the files to import the profile\:";custom_connection_options,57,V40177a0f4,440177a134,;"Custom Options";tun_open_error,57,V400621e8a,4b00621ed1,;"Failed to open the tun interface";debug_build,57,V401076e45,3301076e74,;"debug build";converted_profile,57,V4009d37d9,3e009d3813,;"imported profile";logview_options,57,V401377f00,3801377f34,;"View options";vpn_shortcut,57,V400320d78,3900320dad,;"OpenVPN Shortcut";send_logfile,57,V4007023f0,3600702422,;"Send log file";cipher_dialog_title,57,V4007f2a9e,47007f2ae1,;"Enter encryption method";warn_no_dns,57,V400c14894,10f00c1499f,;"No DNS servers being used. Name resolution may not work. Consider setting custom DNS Servers. Please also note that Android will keep using your proxy settings specified for your mobile/Wi-Fi connection when no DNS servers are set.";vpn_status,57,V401367ece,3101367efb,;"Vpn Status";ab_kitkat_mss,57,V40166966e,ab01669715,;"Early KitKat version set the wrong MSS value on TCP connections (#61948). Try to enable the mssfix option to workaround this bug.";auth_username,57,V400230922,3200230950,;"Username";mssfix_checkbox,57,V40147877c,4d014787c5,;"Override MSS value of TCP payload";cancel_connection_query,57,V4003e10b7,6f003e1122,;"Disconnect the connected VPN/cancel the connection attempt?";payload_options,57,V4015b8d22,3b015b8d59,;"Payload options";persistent_tun_title,57,V400dd5d0f,3f00dd5d4a,;"Persistent tun";ipdns,57,V400bb474f,2c00bb4777,;"IP and DNS";ignore_routes_summary,57,V40054193d,550054198e,;"Ignore routes pushed by the server.";defaultvpn,57,V4019bb259,32019bb287,;"Default VPN";restart,57,V400d05180,2b00d051a7,;"Restart";state_auth_failed,57,V400fb6a4b,4300fb6a8a,;"Authentication failed";restart_vpn_after_change,57,V400d151ac,8700d1522f,;"Configuration changes are applied after restarting the VPN. (Re)start the VPN now?";openvpn_log,57,V400de5d4f,3300de5d7e,;"OpenVPN Log";session_ipv4string,57,V400ec66c5,3a00ec66fb,;"%1$s - %2$s";sorted_az,57,V401aab7f1,3d01aab82a,;"Profiles sorted by name";pw_query_hint,57,V400310d2d,4a00310d73,;"(leave empty to query on demand)";baterry_consumption,57,V400e15e14,4a200e162b2,;"In my personal tests the main reason for high battery consumption of OpenVPN are the keepalive packets. Most OpenVPN servers have a configuration directive like 'keepalive 10 60' which causes the client and server to exchange keepalive packets every ten seconds. <p> While these packets are small and do not use much traffic\, they keep the mobile radio network busy and increase the energy consumption. (See also <a href=https\://developer.android.com/training/efficient-downloads/efficient-network-access.html#RadioStateMachine>The Radio State Machine | Android Developers</a>) <p> This keepalive setting cannot be changed on the client. Only the system administrator of the OpenVPN can change the setting. <p> Unfortunately using a keepalive larger than 60 seconds with UDP can cause some NAT gateways to drop the connection due to an inactivity timeout. Using TCP with a long keep alive timeout works\, but tunneling TCP over TCP performs extremely poorly on connections with high packet loss. (See <a href=http\://sites.inka.de/bigred/devel/tcp-tcp.html>Why TCP Over TCP Is A Bad Idea</a>)";no_certificate,57,V4001303cd,4800130411,;"You must select a certificate";thanks_for_donation,57,V4012c7ca7,47012c7cea,;"Thanks for donating %s!";no_orbotfound,57,V401dac721,8501dac7a2,;"Orbot application cannot be found. Please install Orbot or use manual Socks v5 integration.";faq_system_dialog_xposed,57,V4013a7fd6,139013a810b,;"If you have rooted your Android device you can install the <a href=\"http\://xposed.info/\">Xposed framework</a> and the <a href=\"http\://repo.xposed.info/module/de.blinkt.vpndialogxposed\">VPN Dialog confirm module</a> at your own risk";add_new_vpn_hint,57,V400d755b6,6b00d7561d,;"Use the <img src=\"ic_menu_add\"/> icon to add a new VPN";built_by,57,V401066e14,3001066e40,;"built by %s";qs_title,57,V4019fb3a8,2f019fb3d3,;"Toggle VPN";shortcut_profile_notfound,57,V400340df2,5d00340e4b,;"Profile specified in shortcut not found";tls_key_auth,57,V400461542,4b00461589,;"Enables the TLS Key Authentication";mobile_info,57,V400b54323,7e00b5439d,;"%10$s %9$s running on %3$s %1$s (%2$s)\, Android %6$s (%7$s) API %4$d\, ABI %5$s\, (%8$s)";using_proxy,57,V400ca4f58,3d00ca4f91,;"Using proxy %1$s %2$s";searchdomain,57,V4004e179d,35004e17ce,;"searchDomain";broken_images,57,V4009f385c,37009f388f,;"Broken Images";cancel_connection,57,V4003a0fcf,38003a1003,;"Disconnect";use_system_proxy,57,V400cb4f96,3d00cb4fcf,;"Use system proxy";official_build,57,V401086e79,3901086eae,;"official build";netstatus,57,V400af40ae,3800af40e2,;"Network Status\: %s";auth_failed_behaviour,57,V401acb8b5,4a01acb8fb,;"Behaviour on AUTH_FAILED";protocol,57,V40189aab0,2d0189aad9,;"Protocol";remove_connection_entry,57,V40178a139,4b0178a180,;"Remove connection entry";clear_log_on_connect,57,V40185a8cc,4c0185a914,;"Clear log on new connection";ab_not_route_to_vpn_title,57,V401739f86,5801739fda,;"Route to the configured IP address";menu_use_inline_data,57,V40191ad7b,400191adb7,;"Use inline data";dns_server_info,57,V400671fca,4a00672010,;"DNS Server\: %1$s\, Domain\: %2$s";faq_shortcut,57,V4007b2904,3a007b293a,;"Shortcut to start";faq_security,57,V40096326b,353009635ba,;"As OpenVPN is security sensitive a few notes about security are sensible. All data on the sdcard is inherently insecure. Every app can read it (for example this program requires no special sd card rights). The data of this application can only be read by the application itself. By using the import option for cacert/cert/key in the file dialog the data is stored in the VPN profile. The VPN profiles are only accessible by this application. (Do not forget to delete the copies on the sd card afterwards). Even though accessible only by this application the data is still unencrypted. By rooting the telephone or other exploits it may be possible to retrieve the data. Saved passwords are stored in plain text as well. For pkcs12 files it is highly recommended that you import them into the android keystore.";vpn_tethering_title,57,V400e363cb,4100e36408,;"VPN and Tethering";ab_kitkat_reconnect,57,V4016c9c71,134016c9da1,;"Multiple users report that the mobile connection/mobile data connection is frequently dropped while using the VPN app. The behaviour seems to affect only some mobile provider/device combination and so far no cause/workaround for the bug could be identified. ";data_out,57,V401b4ba74,2801b4ba98,;"Out";openvpn3_pkcs12,57,V401d5c53a,e501d5c61b,;"Using PKCS12 files directly with OpenVPN 3 C\+\+ implementation is not supported. Please import the pkcs12 files into the Android keystore or change to OpenVPN 2.x under general settings.";qs_connect,57,V401a0b3d8,3401a0b408,;"Connect to %s";message_no_user_edit,57,V4017fa68f,90017fa71b,;"This profile has been added from an external app (%s) and has been marked as not user editable.";float_title,57,V4005d1ca4,3d005d1cdd,;"Allow floating server";use_default_title,57,V4005619e8,3f00561a23,;"Use default Route";custom_config_title,57,V400370eeb,4500370f2c,;"Enable Custom Options";useTLSAuth,57,V4001c065e,3d001c0697,;"Use TLS Authentication";random_host_prefix,57,V400350e50,4100350e8d,;"Random Host Prefix";useLZO,57,V4000d025d,32000d028b,;"LZO Compression";settings_auth,57,V400822bf8,4300822c37,;"Authentication/Encryption";tls_cipher_alert,57,V4017ea335,359017ea68a,;"Newer OpenVPN for Android versions (0.6.29/March 2015) use a more secure default for the allowed cipher suites (tls-cipher \"DEFAULT\:!EXP\:!PSK\:!SRP\:!kRSA\"). Unfortunately\, omitting the less secure cipher suites and export cipher suites\, especially the omission of cipher suites that do not support Perfect Forward Secrecy (Diffie-Hellman) causes some problems. This usually caused by an well-intentioned but poorly executed attempt to strengthen TLS security by setting tls-cipher on the server or some embedded OSes with stripped down SSL (e.g. MikroTik).
To solve this problem the problem\, set the tls-cipher settings on the server to reasonable default like tls-cipher \"DEFAULT\:!EXP\:!PSK\:!SRP\:!kRSA\". To work around the problem on the client add the custom option tls-cipher DEFAULT on the Android client.";apprest_uuid_desc,0,V4003f0d34,d00410deb,;"Unique UUID that identifies the profile (example\: 0E910C15–9A85-4DD9-AE0D-E6862392E638). Generate using uuidgen or similar tools";external_authenticator,57,V401e7cce4,4901e7cd29,;"External Authenticator";keep,57,V401538b22,2501538b43,;"Keep";cant_read_folder,57,V400090197,45000901d8,;"Unable to read directory";channel_description_status,57,V401cbc15c,7e01cbc1d6,;"Status changes of the OpenVPN connection (Connecting\, authenticating\,…)";blocklocal_title,57,V4013d8226,4a013d826c,;"Bypass VPN for local networks";send,57,V400712427,2500712448,;"Send";ab_only_cidr_title,57,V4016e9e46,3e016e9e80,;"Non CIDR Routes";pull_off_summary,57,V400491634,7e004916ae,;"No information is requested from the server. Settings need to be specified below.";ignored_pushed_routes,57,V4005318f6,4600531938,;"Ignore pushed routes";weakmd,57,V401b9bb7f,d01c3bf8e,;"<p>Starting with OpenSSL version 1.1\, OpenSSL rejects weak signatures in certificates like MD5.</p><p><b>MD5 signatures are completely insecure and should not be used anymore.</b> MD5 collisions can be created in <a href=https\://natmchugh.blogspot.de/2015/02/create-your-own-md5-collisions.html>few hours at a minimal cost.</a>. You should update the VPN certificates as soon as possible.</p><p>Unfortunately\, older easy-rsa distributions included the config option default_md md5. If you are using an old easy-rsa version\, update to the <a href=https\://github.com/OpenVPN/easy-rsa/releases>latest version</a>) or change md5 to sha256 and regenerate your certificates.</p><p>If you really want to use old and broken certificates use the custom configuration option tls-cipher DEFAULT\:@SECLEVEL=0under advanced configuration or as additional line in your imported configuration</p>";timestamps,57,V401327e05,3101327e32,;"Timestamps";qs_disconnect,57,V401a1b40d,3701a1b440,;"Disconnect %s";disallowed_vpn_apps_info,57,V4014d8910,4e014d895a,;"Disallowed VPN apps\: %1$s";faq_security_title,57,V400953224,4600953266,;"Security considerations";error,57,V400631ed6,2b00631efd,;"Error\: ";faq_android_clients,57,V401618e8e,5cc01619456,;"Multiple OpenVPN clients for Android exist. The most common ones are OpenVPN for Android (this client)\, OpenVPN Connect and OpenVPN Settings.<p>The clients can be grouped into two groups\: OpenVPN for Android and OpenVPN Connect use the official VPNService API (Android 4.0\+) and require no root and OpenVPN Settings which uses root.<p>OpenVPN for Android is an open source client and developed by Arne Schwabe. It is targeted at more advanced users and offers many settings and the ability to import profiles from files and to configure/change profiles inside the app. The client is based on the community version of OpenVPN. It is based on the OpenVPN 2.x source code. This client can be seen as the semi officially client of the community. <p>OpenVPN Connect is non open source client that is developed by OpenVPN Technologies\, Inc. The client is indented to be general use client and more targeted at the average user and allows the import of OpenVPN profiles. This client is based on the OpenVPN C\+\+ reimplementation of the OpenVPN protocol (This was required to allow OpenVPN Technologies\, Inc to publish an iOS OpenVPN app). This client is the official client of the OpenVPN technologies <p> OpenVPN Settings is the oldest of the clients and also a UI for the open source OpenVPN. In contrast to OpenVPN for Android it requires root and does not use the VPNService API. It does not depend on Android 4.0\+";route_not_netip,57,V4006d2236,52006d2284,;"Corrected route %1$s/%2$s to %3$s/%2$s";missing_certificates,57,V40194ae53,650194aeb4,;"Missing user certificate or user certifcate key file";tls_direction,57,V4001d069c,37001d06cf,;"TLS Direction";clearappsdialog,57,V4011873ca,750118743b,;"Clear list of allowed external apps?
Current list of allowed apps\:

%s";enter_tlscn_dialog,57,V4004412f9,1ff004414f4,;"Specify the check used to verify the remote certificate DN (e.g. C=DE\, L=Paderborn\, OU=Avian IP Carriers\, CN=openvpn.blinkt.de)

Specify the complete DN or the RDN (openvpn.blinkt.de in the example) or an RDN prefix for verification.

When using RDN prefix \"Server\" matches \"Server-1\" and \"Server-2\"

Leaving the text field empty will check the RDN against the server hostname.

For more details see the OpenVPN 2.3.1\+ manpage under —verify-x509-name";apprest_uuid,0,V400420df0,2d00420e19,;"UUID";session_ipv6string,57,V400ed6700,4000ed673c,;"%1$s - %3$s\, %2$s";remote_random,57,V401568ba8,5b01568bff,;"Use connection entries in random order on connect";data_in,57,V401b3ba4d,2601b3ba6f,;"In";netchange,57,V400ae406c,4100ae40a9,;"Reconnect on network change";clear,57,V400641f02,2700641f25,;"Clear";import_vpn,57,V4009735bf,2d009735e8,;"Import";client_behaviour,57,V401498810,3d01498849,;"Client behaviour";missing_tlsauth,57,V40193ae0e,440193ae4e,;"tls-auth file is missing";jelly_keystore_alphanumeric_bug,57,V401026c30,11401026d40,;"Some versions of Android 4.1 have problems if the name of the keystore certificate contains non alphanumeric characters (like spaces\, underscores or dashes). Try to reimport the certificate without special characters";keyChainAccessError,57,V4012f7d5b,49012f7da0,;"KeyChain Access error\: %s";bouncy_castle,0,V4002207a5,58002207f9,;"Bouncy Castle Crypto APIs";state_auth_pending,57,V401e6cc9e,4501e6ccdf,;"Authentication pending";ab_kitkat_reconnect_title,57,V40179a185,5c0179a1dd,;"Random disconnects from mobile network";crl_title,57,V40196af03,470196af46,;"Certifcate Revoke List (optional)";crash_toast_text,0,V400350a49,5800350a9d,;"OpenVPN for Android crashed\, crash reported";+style:blinkt,58,V400030088,35000300b9,;Dblinkt.baseTheme,;blinkt.baseTheme,58,V400020037,5000020083,;Dandroid\:Theme.DeviceDefault.Light,;blinkt.dialog,58,V4000500bf,540005010f,;Dandroid\:Theme.DeviceDefault.Light.Dialog,;+styleable:PagerSlidingTabStrip,2,V4000f01f4,1800290742,;-pstsIndicatorColor:color:-pstsUnderlineColor:color:-pstsDividerColor:color:-pstsDividerWidth:dimension:-pstsIndicatorHeight:dimension:-pstsUnderlineHeight:dimension:-pstsDividerPadding:dimension:-pstsTabPaddingLeftRight:dimension:-pstsScrollOffset:dimension:-pstsTabBackground:reference:-pstsShouldExpand:boolean:-pstsTextAllCaps:boolean:-pstsPaddingMiddle:boolean:-pstsTextStyle:flags:normal:0,bold:1,italic:2,-pstsTextSelectedStyle:flags:normal:0,bold:1,italic:2,-pstsTextAlpha:float:-pstsTextSelectedAlpha:float:;FileSelectLayout,2,V5000800d6,17000d01ee,;-fileTitle:reference|string:-certificate:boolean:-showClear:boolean:;+xml:app_restrictions,59,F;