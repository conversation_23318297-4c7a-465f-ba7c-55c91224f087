package com.official.fivegfastvpn.model;

import com.official.fivegfastvpn.utils.UrlDetectionUtils;

/**
 * Enhanced Custom Ad Model for 5G Smart VPN
 * Represents a custom advertisement from the admin panel with smart URL detection
 */
public class CustomAd {
    private int id;
    private String title;
    private String image;
    private String text;
    private String url;
    private String urlType; // New field for URL type detection
    private String buttonText; // New field for button text
    private String dateStart;
    private String dateEnd;
    private String expiresAt; // New field for expiration
    private int priority; // New field for ad priority
    private int on; // 1 = active, 0 = inactive
    private int viewCount;
    private int clickCount;
    private boolean isApproved; // New field for approval status

    // Metadata fields
    private String apiVersion;
    private long timestamp;
    private String source;
    private String endpoint;
    private int totalActiveAds;

    // Enhanced metadata
    private boolean smartUrlDetection;
    private boolean supportsAppStoreDetection;
    
    // Constructors
    public CustomAd() {}

    public CustomAd(int id, String title, String text, String url) {
        this.id = id;
        this.title = title;
        this.text = text;
        this.url = url;

        // Auto-detect URL type and button text
        if (url != null && !url.isEmpty()) {
            UrlDetectionUtils.UrlType detectedType = UrlDetectionUtils.detectUrlType(url);
            this.urlType = detectedType.getType();
            this.buttonText = detectedType.getDefaultButtonText();
        }
    }

    /**
     * Constructor from JSON object with enhanced fields
     * @param json JSON object containing ad data
     */
    public static CustomAd fromJson(org.json.JSONObject json) {
        CustomAd ad = new CustomAd();
        try {
            ad.id = json.optInt("id", 0);
            ad.title = json.optString("title", "");
            ad.image = json.optString("image", "");
            ad.text = json.optString("text", "");
            ad.url = json.optString("url", "");
            ad.urlType = json.optString("url_type", "website");
            ad.buttonText = json.optString("button_text", "");
            ad.dateStart = json.optString("date_start", "");
            ad.dateEnd = json.optString("date_end", "");
            ad.expiresAt = json.optString("expires_at", "");
            ad.priority = json.optInt("priority", 0);
            ad.on = json.optInt("on", 0);
            ad.viewCount = json.optInt("view_count", 0);
            ad.clickCount = json.optInt("click_count", 0);
            ad.isApproved = json.optBoolean("is_approved", true);
            ad.apiVersion = json.optString("api_version", "");
            ad.timestamp = json.optLong("timestamp", 0);
            ad.source = json.optString("source", "");
            ad.endpoint = json.optString("endpoint", "");
            ad.totalActiveAds = json.optInt("total_active_ads", 0);
            ad.smartUrlDetection = json.optBoolean("smart_url_detection", false);
            ad.supportsAppStoreDetection = json.optBoolean("supports_app_store_detection", false);

            // Auto-detect URL type and button text if not provided by API
            if (ad.urlType == null || ad.urlType.isEmpty() || ad.urlType.equals("other")) {
                UrlDetectionUtils.UrlType detectedType = UrlDetectionUtils.detectUrlType(ad.url);
                ad.urlType = detectedType.getType();
            }

            // Set button text based on URL type if not provided
            if (ad.buttonText == null || ad.buttonText.isEmpty()) {
                ad.buttonText = UrlDetectionUtils.getButtonTextFromType(ad.urlType, null);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return ad;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getImage() {
        return image;
    }
    
    public void setImage(String image) {
        this.image = image;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getDateStart() {
        return dateStart;
    }
    
    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }
    
    public String getDateEnd() {
        return dateEnd;
    }
    
    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }
    
    public int getOn() {
        return on;
    }
    
    public void setOn(int on) {
        this.on = on;
    }
    
    public int getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(int viewCount) {
        this.viewCount = viewCount;
    }
    
    public int getClickCount() {
        return clickCount;
    }
    
    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public int getTotalActiveAds() {
        return totalActiveAds;
    }
    
    public void setTotalActiveAds(int totalActiveAds) {
        this.totalActiveAds = totalActiveAds;
    }

    // New getters and setters for enhanced fields
    public String getUrlType() {
        return urlType;
    }

    public void setUrlType(String urlType) {
        this.urlType = urlType;
    }

    public String getButtonText() {
        return buttonText;
    }

    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    public String getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(String expiresAt) {
        this.expiresAt = expiresAt;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isApproved() {
        return isApproved;
    }

    public void setApproved(boolean approved) {
        isApproved = approved;
    }

    public boolean isSmartUrlDetection() {
        return smartUrlDetection;
    }

    public void setSmartUrlDetection(boolean smartUrlDetection) {
        this.smartUrlDetection = smartUrlDetection;
    }

    public boolean isSupportsAppStoreDetection() {
        return supportsAppStoreDetection;
    }

    public void setSupportsAppStoreDetection(boolean supportsAppStoreDetection) {
        this.supportsAppStoreDetection = supportsAppStoreDetection;
    }
    
    /**
     * Check if the ad is active
     * @return true if ad is active
     */
    public boolean isActive() {
        return on == 1;
    }
    
    /**
     * Get the full image URL
     * @param baseUrl Base URL for images
     * @return Full image URL
     */
    public String getFullImageUrl(String baseUrl) {
        if (image == null || image.isEmpty()) {
            return null;
        }
        
        // If image already contains full URL, return as is
        if (image.startsWith("http://") || image.startsWith("https://")) {
            return image;
        }
        
        // Construct full URL
        return baseUrl + "/" + image;
    }
    
    /**
     * Get click-through rate
     * @return CTR as percentage
     */
    public double getClickThroughRate() {
        if (viewCount == 0) {
            return 0.0;
        }
        return (double) clickCount / viewCount * 100.0;
    }

    /**
     * Check if the ad is a Play Store app
     * @return true if it's a Play Store URL
     */
    public boolean isPlayStoreApp() {
        return "playstore".equals(urlType);
    }

    /**
     * Check if the ad is an App Store app
     * @return true if it's an App Store URL
     */
    public boolean isAppStoreApp() {
        return "appstore".equals(urlType);
    }

    /**
     * Check if the ad is a website
     * @return true if it's a website URL
     */
    public boolean isWebsite() {
        return "website".equals(urlType);
    }

    /**
     * Check if the ad is an app (either Play Store or App Store)
     * @return true if it's an app
     */
    public boolean isApp() {
        return isPlayStoreApp() || isAppStoreApp();
    }

    /**
     * Get the appropriate action text for the URL
     * @return Action text (Install, Visit, etc.)
     */
    public String getActionText() {
        if (buttonText != null && !buttonText.isEmpty()) {
            return buttonText;
        }
        return UrlDetectionUtils.getButtonTextFromType(urlType, null);
    }

    /**
     * Open the ad URL using the appropriate action
     * @param context Android context
     * @return true if successfully opened
     */
    public boolean openUrl(android.content.Context context) {
        return UrlDetectionUtils.openUrl(context, url, urlType);
    }

    /**
     * Check if the ad has expired
     * @return true if expired
     */
    public boolean isExpired() {
        if (expiresAt == null || expiresAt.isEmpty()) {
            return false;
        }

        try {
            // Simple date comparison (you might want to use a proper date parser)
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            java.util.Date expireDate = sdf.parse(expiresAt);
            return expireDate.before(new java.util.Date());
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String toString() {
        return "CustomAd{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", image='" + image + '\'' +
                ", text='" + text + '\'' +
                ", url='" + url + '\'' +
                ", dateStart='" + dateStart + '\'' +
                ", dateEnd='" + dateEnd + '\'' +
                ", on=" + on +
                ", viewCount=" + viewCount +
                ", clickCount=" + clickCount +
                ", apiVersion='" + apiVersion + '\'' +
                ", timestamp=" + timestamp +
                ", source='" + source + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", totalActiveAds=" + totalActiveAds +
                '}';
    }
}
