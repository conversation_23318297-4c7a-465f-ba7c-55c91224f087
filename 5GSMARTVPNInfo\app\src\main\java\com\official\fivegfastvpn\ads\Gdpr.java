package com.official.fivegfastvpn.ads;

import android.app.Activity;
import android.util.Log;

import com.google.android.gms.ads.MobileAds;
import com.google.android.ump.ConsentForm;
import com.google.android.ump.ConsentInformation;
import com.google.android.ump.ConsentRequestParameters;
import com.google.android.ump.UserMessagingPlatform;

import java.util.concurrent.atomic.AtomicBoolean;
//Developer :--Md <PERSON><PERSON>l Hasan <PERSON>er
public class Gdpr {

    Activity activity;

    String TAG = "GDRP_DEBUG_CHECK";

    private final AtomicBoolean isMobileAdsInitializeCalled = new AtomicBoolean(false);


    public Gdpr(Activity activity) {
        this.activity = activity;
    }


    public void setGdpr() {
        ConsentRequestParameters params = new ConsentRequestParameters.Builder().setTagForUnderAgeOfConsent(false).build();

        ConsentInformation consentInformation = UserMessagingPlatform.getConsentInformation(activity);
        consentInformation.requestConsentInfoUpdate(activity, params, (ConsentInformation.OnConsentInfoUpdateSuccessListener) () -> {
            UserMessagingPlatform.loadAndShowConsentFormIfRequired(activity, (ConsentForm.OnConsentFormDismissedListener) loadAndShowError -> {
                if (loadAndShowError != null) {
                    Log.w(TAG, String.format("%s: %s", loadAndShowError.getErrorCode(), loadAndShowError.getMessage()));
                }

                if (consentInformation.canRequestAds()) {
                    initializeMobileAdsSdk();
                }

            });
        }, (ConsentInformation.OnConsentInfoUpdateFailureListener) requestConsentError -> {
            Log.w(TAG, String.format("%s: %s", requestConsentError.getErrorCode(), requestConsentError.getMessage()));
        });


        if (consentInformation.canRequestAds()) {
            initializeMobileAdsSdk();
        }

    }

    private void initializeMobileAdsSdk() {
        if (isMobileAdsInitializeCalled.getAndSet(true)) {
            return;
        }
        MobileAds.initialize(activity);
    }


}
