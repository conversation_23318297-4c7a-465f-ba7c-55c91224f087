-- =====================================================
-- Fix for Contact Messages Table Creation
-- =====================================================

-- First, ensure admin_users table exists
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','moderator','viewer') DEFAULT 'admin',
  `status` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>IQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user if not exists
INSERT IGNORE INTO `admin_users` (`id`, `username`, `email`, `password`, `role`, `status`) 
VALUES (1, 'admin', '<EMAIL>', '12345', 'admin', 1);

-- Now create the contact_messages table with proper foreign key
DROP TABLE IF EXISTS `contact_messages`;
CREATE TABLE `contact_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('unread','read','replied','archived') NOT NULL DEFAULT 'unread',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `category` varchar(50) DEFAULT 'general',
  `reply_text` text DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `replied_at` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_category` (`category`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_email` (`email`),
  KEY `fk_admin_id` (`admin_id`),
  CONSTRAINT `fk_contact_messages_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample data for testing
INSERT INTO `contact_messages` (`name`, `email`, `subject`, `message`, `status`, `priority`, `category`, `ip_address`) VALUES
('John Doe', '<EMAIL>', 'Connection Issue', 'I am having trouble connecting to the VPN servers. Can you help?', 'unread', 'normal', 'technical', '*************'),
('Jane Smith', '<EMAIL>', 'Feature Request', 'Would it be possible to add more server locations?', 'unread', 'low', 'feature', '*************'),
('Mike Johnson', '<EMAIL>', 'Billing Question', 'I have a question about my subscription billing.', 'unread', 'high', 'billing', '*************');

-- Set auto increment
ALTER TABLE `contact_messages` AUTO_INCREMENT=10;

SELECT 'Contact messages table created successfully!' as Result;
