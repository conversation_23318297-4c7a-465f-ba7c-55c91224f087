<?php
/**
 * 5G Smart VPN Admin Panel - Authentication System
 * Handles user authentication and session management
 */

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_username']);
}

// Redirect to login if not authenticated
function requireAuth() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// Login function
function login($username, $password, $conn) {
    $username = mysqli_real_escape_string($conn, $username);
    $password = mysqli_real_escape_string($conn, $password);

    $query = "SELECT id, username FROM admin WHERE username = '$username' AND password = '$password'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $admin = mysqli_fetch_assoc($result);

        // Set session variables
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['login_time'] = time();

        // Log login activity
        logActivity($conn, 'login', "Admin {$admin['username']} logged in");

        return true;
    }

    return false;
}

// Logout function
function logout($conn = null) {
    if ($conn && isLoggedIn()) {
        logActivity($conn, 'logout', "Admin {$_SESSION['admin_username']} logged out");
    }

    session_unset();
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get current admin info
function getCurrentAdmin($conn) {
    if (!isLoggedIn()) {
        return null;
    }

    $admin_id = $_SESSION['admin_id'];
    $query = "SELECT * FROM admin WHERE id = $admin_id";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

// Log activity
function logActivity($conn, $action, $description) {
    $admin_id = $_SESSION['admin_id'] ?? 0;
    $admin_username = $_SESSION['admin_username'] ?? 'System';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = mysqli_real_escape_string($conn, $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown');

    $query = "INSERT INTO admin_logs (admin_id, admin_username, action, description, ip_address, user_agent, created_at)
              VALUES ($admin_id, '$admin_username', '$action', '$description', '$ip_address', '$user_agent', NOW())";

    mysqli_query($conn, $query);
}

// Check if not authenticated and redirect (only for non-login pages)
$current_page = basename($_SERVER['PHP_SELF']);
if (!isLoggedIn() && $current_page !== 'login.php' && $current_page !== 'logout.php') {
    requireAuth();
}
?>
