<?php
/**
 * 5G Smart VPN Admin Panel - Admin Profile Management
 */

// Start session and include authentication
session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Admin Profile';
$success_message = '';
$error_message = '';

// Get current admin info
$admin_id = $_SESSION['admin_id'];
$query = "SELECT * FROM admin_users WHERE id = $admin_id";
$result = mysqli_query($conn, $query);
$admin = mysqli_fetch_assoc($result);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $current_password = trim($_POST['current_password']);
    $new_password = trim($_POST['new_password']);
    $confirm_password = trim($_POST['confirm_password']);

    // Validate input
    if (empty($username)) {
        $error_message = 'Username is required.';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error_message = 'New passwords do not match.';
    } elseif (!empty($new_password) && empty($current_password)) {
        $error_message = 'Current password is required to change password.';
    } else {
        // Verify current password if changing password
        if (!empty($new_password)) {
            $verify_query = "SELECT id FROM admin_users WHERE id = $admin_id AND password = '$current_password'";
            $verify_result = mysqli_query($conn, $verify_query);

            if (mysqli_num_rows($verify_result) === 0) {
                $error_message = 'Current password is incorrect.';
            }
        }

        if (empty($error_message)) {
            // Update profile
            $username = mysqli_real_escape_string($conn, $username);
            $email = mysqli_real_escape_string($conn, $email);

            if (!empty($new_password)) {
                $new_password = mysqli_real_escape_string($conn, $new_password);
                $update_query = "UPDATE admin_users SET username = '$username', email = '$email', password = '$new_password' WHERE id = $admin_id";
            } else {
                $update_query = "UPDATE admin_users SET username = '$username', email = '$email' WHERE id = $admin_id";
            }

            if (mysqli_query($conn, $update_query)) {
                $_SESSION['admin_username'] = $username;
                $success_message = 'Profile updated successfully!';

                // Refresh admin data
                $result = mysqli_query($conn, "SELECT * FROM admin_users WHERE id = $admin_id");
                $admin = mysqli_fetch_assoc($result);
            } else {
                $error_message = 'Error updating profile: ' . mysqli_error($conn);
            }
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Admin Profile</h1>
                <p class="page-subtitle">Manage your account settings and preferences</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </header>

        <!-- Profile Content -->
        <div class="content-body">
            <!-- Flash Messages -->
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="ri-check-line"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i class="ri-error-warning-line"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="dashboard-grid">
                <!-- Profile Information -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Profile Information</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="profile.php">
                            <div class="form-group">
                                <label for="username" class="form-label">Username</label>
                                <input type="text"
                                       id="username"
                                       name="username"
                                       class="form-control"
                                       value="<?php echo htmlspecialchars($admin['username'] ?? ''); ?>"
                                       required>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       class="form-control"
                                       value="<?php echo htmlspecialchars($admin['email'] ?? ''); ?>"
                                       placeholder="Enter your email address">
                            </div>

                            <div class="form-group">
                                <label for="created_at" class="form-label">Account Created</label>
                                <input type="text"
                                       id="created_at"
                                       class="form-control"
                                       value="<?php echo date('F j, Y g:i A', strtotime($admin['created_at'] ?? 'now')); ?>"
                                       readonly>
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="ri-save-line"></i>
                                Update Profile
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Change Password</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="profile.php">
                            <input type="hidden" name="username" value="<?php echo htmlspecialchars($admin['username'] ?? ''); ?>">
                            <input type="hidden" name="email" value="<?php echo htmlspecialchars($admin['email'] ?? ''); ?>">

                            <div class="form-group">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password"
                                       id="current_password"
                                       name="current_password"
                                       class="form-control"
                                       placeholder="Enter current password">
                            </div>

                            <div class="form-group">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password"
                                       id="new_password"
                                       name="new_password"
                                       class="form-control"
                                       placeholder="Enter new password">
                            </div>

                            <div class="form-group">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password"
                                       id="confirm_password"
                                       name="confirm_password"
                                       class="form-control"
                                       placeholder="Confirm new password">
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-warning">
                                <i class="ri-lock-password-line"></i>
                                Change Password
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Account Statistics -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Account Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="stats-list">
                            <div class="stat-item">
                                <span class="stat-label">Admin ID:</span>
                                <span class="stat-value">#<?php echo $admin['id']; ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Last Login:</span>
                                <span class="stat-value"><?php echo isset($_SESSION['login_time']) ? date('F j, Y g:i A', $_SESSION['login_time']) : 'Current session'; ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Session Status:</span>
                                <span class="stat-value status-active">Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-success {
    background: #f0fdf4;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: var(--gray-600);
}

.stat-value {
    font-weight: 600;
    color: var(--gray-900);
}

.status-active {
    color: var(--success-600);
}
</style>

<?php include 'includes/footer.php'; ?>
