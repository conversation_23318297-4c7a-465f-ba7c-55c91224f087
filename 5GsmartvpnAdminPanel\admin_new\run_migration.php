<?php
/**
 * Database Migration Runner
 * This script safely runs the database migration to fix custom ads system issues
 * Run this once to resolve the database-related errors
 */

// Include database connection
require_once 'includes/config.php';

// Set execution time limit for migration
set_time_limit(300); // 5 minutes

// Function to execute database migration
function runDatabaseMigration($conn) {
    $results = [];
    $errors = [];

    // Helper function to check if column exists
    function columnExists($conn, $table, $column) {
        $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
        return mysqli_num_rows($result) > 0;
    }

    // Helper function to check if table exists
    function tableExists($conn, $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        return mysqli_num_rows($result) > 0;
    }

    try {
        // 1. Create customer_accounts table if it doesn't exist
        if (!tableExists($conn, 'customer_accounts')) {
            $sql = "CREATE TABLE customer_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                whatsapp_number VARCHAR(20) UNIQUE NOT NULL,
                customer_name VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                total_spent DECIMAL(10,2) DEFAULT 0.00,
                total_orders INT DEFAULT 0,
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP NULL,
                verification_code VARCHAR(6),
                verification_expires TIMESTAMP NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                INDEX idx_whatsapp (whatsapp_number),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if (mysqli_query($conn, $sql)) {
                $results[] = "Created customer_accounts table";
            } else {
                $errors[] = "Failed to create customer_accounts table: " . mysqli_error($conn);
            }
        } else {
            $results[] = "customer_accounts table already exists";
        }

        // 2. Create customer_payments table if it doesn't exist
        if (!tableExists($conn, 'customer_payments')) {
            $sql = "CREATE TABLE customer_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_id INT NOT NULL,
                package_id INT NULL,
                transaction_id VARCHAR(100) NOT NULL,
                payment_method ENUM('bkash', 'nagad', 'rocket', 'google_pay', 'paypal', 'manual') NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'BDT',
                payment_status ENUM('pending', 'verified', 'rejected', 'refunded') DEFAULT 'pending',
                payment_proof TEXT,
                admin_notes TEXT,
                verified_by INT NULL,
                verified_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customer (customer_id),
                INDEX idx_status (payment_status),
                INDEX idx_method (payment_method),
                INDEX idx_transaction (transaction_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if (mysqli_query($conn, $sql)) {
                $results[] = "Created customer_payments table";
            } else {
                $errors[] = "Failed to create customer_payments table: " . mysqli_error($conn);
            }
        } else {
            $results[] = "customer_payments table already exists";
        }

        // 3. Create payment_methods table if it doesn't exist
        if (!tableExists($conn, 'payment_methods')) {
            $sql = "CREATE TABLE payment_methods (
                id INT AUTO_INCREMENT PRIMARY KEY,
                method_code VARCHAR(20) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                account_number VARCHAR(100) NOT NULL,
                account_name VARCHAR(255) NOT NULL,
                instructions TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_code (method_code)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if (mysqli_query($conn, $sql)) {
                $results[] = "Created payment_methods table";
            } else {
                $errors[] = "Failed to create payment_methods table: " . mysqli_error($conn);
            }
        } else {
            $results[] = "payment_methods table already exists";
        }

        // 4. Create ad_packages table if it doesn't exist
        if (!tableExists($conn, 'ad_packages')) {
            $sql = "CREATE TABLE ad_packages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                package_name VARCHAR(100) NOT NULL,
                duration_days INT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'BDT',
                max_ads INT DEFAULT 1,
                features JSON,
                is_active BOOLEAN DEFAULT TRUE,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_duration (duration_days)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if (mysqli_query($conn, $sql)) {
                $results[] = "Created ad_packages table";
            } else {
                $errors[] = "Failed to create ad_packages table: " . mysqli_error($conn);
            }
        } else {
            $results[] = "ad_packages table already exists";
        }

        // 5. Add missing columns to custom_ads table
        $columns_to_add = [
            'customer_id' => 'INT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'is_approved' => 'BOOLEAN DEFAULT NULL',
            'package_id' => 'INT NULL',
            'payment_id' => 'INT NULL'
        ];

        foreach ($columns_to_add as $column => $definition) {
            if (!columnExists($conn, 'custom_ads', $column)) {
                $sql = "ALTER TABLE custom_ads ADD COLUMN $column $definition";
                if (mysqli_query($conn, $sql)) {
                    $results[] = "Added column $column to custom_ads table";
                } else {
                    $errors[] = "Failed to add column $column to custom_ads table: " . mysqli_error($conn);
                }
            } else {
                $results[] = "Column $column already exists in custom_ads table";
            }
        }

    } catch (Exception $e) {
        $errors[] = "Exception: " . $e->getMessage();
    }

    return [
        'success' => empty($errors),
        'message' => empty($errors) ? 'Migration completed successfully' : 'Migration completed with errors',
        'results' => $results,
        'errors' => $errors
    ];
}

// Check if this is a POST request (form submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    $migration_result = runDatabaseMigration($conn);

    // Insert default data if tables were created successfully
    if ($migration_result['success']) {
        // Insert default payment methods
        $payment_methods = [
            ['bkash', 'bKash Mobile Banking', '***********', '5G Smart VPN', 'Send money to our bKash number and provide the transaction ID', 1, 1],
            ['nagad', 'Nagad Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Nagad number and provide the transaction ID', 1, 2],
            ['rocket', 'Rocket Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Rocket number and provide the transaction ID', 1, 3],
            ['google_pay', 'Google Pay', '<EMAIL>', '5G Smart VPN', 'Send payment to our Google Pay account and include your WhatsApp number', 1, 4],
            ['paypal', 'PayPal', '<EMAIL>', '5G Smart VPN', 'Send payment to our PayPal account and include your WhatsApp number', 1, 5]
        ];

        foreach ($payment_methods as $method) {
            $check_sql = "SELECT COUNT(*) as count FROM payment_methods WHERE method_code = ?";
            $stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($stmt, "s", $method[0]);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);

            if ($row['count'] == 0) {
                $insert_sql = "INSERT INTO payment_methods (method_code, display_name, account_number, account_name, instructions, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $insert_sql);
                mysqli_stmt_bind_param($stmt, "sssssii", $method[0], $method[1], $method[2], $method[3], $method[4], $method[5], $method[6]);
                if (mysqli_stmt_execute($stmt)) {
                    $migration_result['results'][] = "Inserted payment method: " . $method[1];
                }
            }
        }

        // Insert default ad packages
        $ad_packages = [
            ['Basic Ad', 7, 500.00, 'BDT', 1, '{"description": "Basic ad package for 7 days", "features": ["Standard placement", "Basic analytics"]}', 1, 1],
            ['Premium Ad', 15, 1000.00, 'BDT', 1, '{"description": "Premium ad package for 15 days", "features": ["Priority placement", "Advanced analytics", "Featured listing"]}', 1, 2],
            ['Extended Ad', 30, 1800.00, 'BDT', 1, '{"description": "Extended ad package for 30 days", "features": ["Top placement", "Detailed analytics", "Featured listing", "Social media promotion"]}', 1, 3]
        ];

        foreach ($ad_packages as $package) {
            $check_sql = "SELECT COUNT(*) as count FROM ad_packages WHERE package_name = ?";
            $stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($stmt, "s", $package[0]);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);

            if ($row['count'] == 0) {
                $insert_sql = "INSERT INTO ad_packages (package_name, duration_days, price, currency, max_ads, features, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $insert_sql);
                mysqli_stmt_bind_param($stmt, "sidsissi", $package[0], $package[1], $package[2], $package[3], $package[4], $package[5], $package[6], $package[7]);
                if (mysqli_stmt_execute($stmt)) {
                    $migration_result['results'][] = "Inserted ad package: " . $package[0];
                }
            }
        }

        // Update existing custom_ads records to have proper timestamps
        $update_sql = "UPDATE custom_ads SET created_at = NOW() WHERE created_at IS NULL";
        if (mysqli_query($conn, $update_sql)) {
            $migration_result['results'][] = "Updated existing custom_ads records with timestamps";
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - 5G Smart VPN</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="ri-database-2-line me-2"></i>
                            Database Migration Tool
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($migration_result)): ?>
                            <div class="alert alert-<?php echo $migration_result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
                                <h5 class="alert-heading">
                                    <i class="ri-<?php echo $migration_result['success'] ? 'check' : 'error-warning'; ?>-line me-2"></i>
                                    <?php echo $migration_result['message']; ?>
                                </h5>
                                
                                <?php if (!empty($migration_result['results'])): ?>
                                    <hr>
                                    <h6>Executed Statements:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($migration_result['results'] as $result): ?>
                                            <li><small><?php echo htmlspecialchars($result); ?></small></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <?php if (!empty($migration_result['errors'])): ?>
                                    <hr>
                                    <h6 class="text-danger">Errors:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($migration_result['errors'] as $error): ?>
                                            <li class="text-danger"><small><?php echo htmlspecialchars($error); ?></small></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-4">
                            <h5>Migration Purpose</h5>
                            <p>This migration will fix the following database issues:</p>
                            <ul>
                                <li><strong>customer-accounts.php:</strong> Missing <code>created_at</code> column in custom_ads table</li>
                                <li><strong>custom-ads-analytics.php:</strong> Missing <code>created_at</code> and <code>is_approved</code> columns</li>
                                <li><strong>payment-methods.php:</strong> Null value handling for account information</li>
                            </ul>
                        </div>
                        
                        <div class="mb-4">
                            <h5>What This Migration Does</h5>
                            <ul>
                                <li>Creates missing tables: <code>customer_accounts</code>, <code>customer_payments</code>, <code>payment_methods</code>, <code>ad_packages</code></li>
                                <li>Adds missing columns to <code>custom_ads</code> table safely</li>
                                <li>Inserts default payment methods and ad packages</li>
                                <li>Updates existing records with proper timestamps</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="ri-information-line me-2"></i>
                            <strong>Important:</strong> This migration is safe to run multiple times. It will only add missing tables and columns without affecting existing data.
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('Are you sure you want to run the database migration?');">
                            <div class="d-grid gap-2">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    <i class="ri-play-line me-2"></i>
                                    Run Database Migration
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-2"></i>
                                    Back to Admin Panel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
