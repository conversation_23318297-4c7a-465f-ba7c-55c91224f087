<?php
/**
 * 5G Smart VPN Admin Panel - System Settings
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'System Settings';
$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $settings = [
        'app_name' => trim($_POST['app_name'] ?? ''),
        'app_version' => trim($_POST['app_version'] ?? ''),
        'app_description' => trim($_POST['app_description'] ?? ''),
        'contact_email' => trim($_POST['contact_email'] ?? ''),
        'support_url' => trim($_POST['support_url'] ?? ''),
        'privacy_policy_url' => trim($_POST['privacy_policy_url'] ?? ''),
        'terms_of_service_url' => trim($_POST['terms_of_service_url'] ?? ''),
        'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
        'debug_mode' => isset($_POST['debug_mode']) ? 1 : 0,
        'auto_connect' => isset($_POST['auto_connect']) ? 1 : 0,
        'kill_switch' => isset($_POST['kill_switch']) ? 1 : 0,
        'dns_leak_protection' => isset($_POST['dns_leak_protection']) ? 1 : 0,
        'connection_timeout' => (int)($_POST['connection_timeout'] ?? 30),
        'max_concurrent_connections' => (int)($_POST['max_concurrent_connections'] ?? 5),
        'log_level' => trim($_POST['log_level'] ?? 'info'),
        'backup_frequency' => trim($_POST['backup_frequency'] ?? 'daily'),
        'notification_email' => trim($_POST['notification_email'] ?? ''),
        'smtp_host' => trim($_POST['smtp_host'] ?? ''),
        'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
        'smtp_username' => trim($_POST['smtp_username'] ?? ''),
        'smtp_password' => trim($_POST['smtp_password'] ?? ''),
        'smtp_encryption' => trim($_POST['smtp_encryption'] ?? 'tls')
    ];
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($settings as $key => $value) {
        // Check if setting exists
        $check_query = "SELECT id FROM settings WHERE setting_key = ?";
        $check_stmt = mysqli_prepare($conn, $check_query);
        
        if ($check_stmt) {
            mysqli_stmt_bind_param($check_stmt, 's', $key);
            mysqli_stmt_execute($check_stmt);
            $result = mysqli_stmt_get_result($check_stmt);
            
            if (mysqli_num_rows($result) > 0) {
                // Update existing setting
                $update_query = "UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?";
                $update_stmt = mysqli_prepare($conn, $update_query);
                if ($update_stmt) {
                    mysqli_stmt_bind_param($update_stmt, 'ss', $value, $key);
                    if (mysqli_stmt_execute($update_stmt)) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    mysqli_stmt_close($update_stmt);
                }
            } else {
                // Insert new setting
                $insert_query = "INSERT INTO settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())";
                $insert_stmt = mysqli_prepare($conn, $insert_query);
                if ($insert_stmt) {
                    mysqli_stmt_bind_param($insert_stmt, 'ss', $key, $value);
                    if (mysqli_stmt_execute($insert_stmt)) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    mysqli_stmt_close($insert_stmt);
                }
            }
            mysqli_stmt_close($check_stmt);
        }
    }
    
    if ($error_count === 0) {
        $success_message = 'Settings saved successfully!';
    } else {
        $error_message = "Some settings could not be saved. Saved: $success_count, Failed: $error_count";
    }
}

// Load current settings
$current_settings = [];
$settings_query = "SELECT setting_key, setting_value FROM settings";
$settings_result = mysqli_query($conn, $settings_query);

if ($settings_result) {
    while ($row = mysqli_fetch_assoc($settings_result)) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
}

// Default values
$defaults = [
    'app_name' => '5G Smart VPN',
    'app_version' => '1.0.0',
    'app_description' => 'Fast and secure VPN service',
    'contact_email' => '<EMAIL>',
    'support_url' => 'https://5gsmartvpn.com/support',
    'privacy_policy_url' => 'https://5gsmartvpn.com/privacy',
    'terms_of_service_url' => 'https://5gsmartvpn.com/terms',
    'maintenance_mode' => 0,
    'debug_mode' => 0,
    'auto_connect' => 1,
    'kill_switch' => 1,
    'dns_leak_protection' => 1,
    'connection_timeout' => 30,
    'max_concurrent_connections' => 5,
    'log_level' => 'info',
    'backup_frequency' => 'daily',
    'notification_email' => '',
    'smtp_host' => '',
    'smtp_port' => 587,
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_encryption' => 'tls'
];

foreach ($defaults as $key => $default) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default;
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">System Settings</h1>
                <p class="page-subtitle">Configure application settings and preferences</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportSettings()">
                        <i class="ri-download-line"></i>
                        <span class="hide-mobile">Export Settings</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="ri-check-circle-line"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="ri-error-warning-line"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <form method="POST" class="settings-form">
                <div class="settings-tabs">
                    <div class="tab-nav">
                        <button type="button" class="tab-btn active" data-tab="general">
                            <i class="ri-settings-3-line"></i>
                            General
                        </button>
                        <button type="button" class="tab-btn" data-tab="vpn">
                            <i class="ri-shield-line"></i>
                            VPN Settings
                        </button>
                        <button type="button" class="tab-btn" data-tab="email">
                            <i class="ri-mail-line"></i>
                            Email Settings
                        </button>
                        <button type="button" class="tab-btn" data-tab="advanced">
                            <i class="ri-code-line"></i>
                            Advanced
                        </button>
                    </div>
                    
                    <div class="tab-content">
                        <!-- General Settings -->
                        <div class="tab-pane active" id="general">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Application Information</h3>
                                    <p class="card-subtitle">Basic application details and branding</p>
                                </div>
                                <div class="card-body">
                                    <div class="form-section">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="app_name" class="form-label">Application Name</label>
                                                <input type="text" id="app_name" name="app_name" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['app_name']); ?>" 
                                                       placeholder="5G Smart VPN">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="app_version" class="form-label">Version</label>
                                                <input type="text" id="app_version" name="app_version" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['app_version']); ?>" 
                                                       placeholder="1.0.0">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="app_description" class="form-label">Description</label>
                                            <textarea id="app_description" name="app_description" class="form-control form-textarea" 
                                                      placeholder="Brief description of your VPN service"><?php echo htmlspecialchars($current_settings['app_description']); ?></textarea>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="contact_email" class="form-label">Contact Email</label>
                                                <input type="email" id="contact_email" name="contact_email" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['contact_email']); ?>" 
                                                       placeholder="<EMAIL>">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="support_url" class="form-label">Support URL</label>
                                                <input type="url" id="support_url" name="support_url" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['support_url']); ?>" 
                                                       placeholder="https://example.com/support">
                                            </div>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="privacy_policy_url" class="form-label">Privacy Policy URL</label>
                                                <input type="url" id="privacy_policy_url" name="privacy_policy_url" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['privacy_policy_url']); ?>" 
                                                       placeholder="https://example.com/privacy">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="terms_of_service_url" class="form-label">Terms of Service URL</label>
                                                <input type="url" id="terms_of_service_url" name="terms_of_service_url" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['terms_of_service_url']); ?>" 
                                                       placeholder="https://example.com/terms">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- VPN Settings -->
                        <div class="tab-pane" id="vpn">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">VPN Configuration</h3>
                                    <p class="card-subtitle">Connection and security settings</p>
                                </div>
                                <div class="card-body">
                                    <div class="form-section">
                                        <div class="form-group">
                                            <label class="form-label">Security Features</label>
                                            <div class="checkbox-grid">
                                                <div class="form-check">
                                                    <label class="switch">
                                                        <input type="checkbox" name="auto_connect" 
                                                               <?php echo $current_settings['auto_connect'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="form-check-label">Auto Connect on Startup</span>
                                                </div>
                                                
                                                <div class="form-check">
                                                    <label class="switch">
                                                        <input type="checkbox" name="kill_switch" 
                                                               <?php echo $current_settings['kill_switch'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="form-check-label">Kill Switch</span>
                                                </div>
                                                
                                                <div class="form-check">
                                                    <label class="switch">
                                                        <input type="checkbox" name="dns_leak_protection" 
                                                               <?php echo $current_settings['dns_leak_protection'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="form-check-label">DNS Leak Protection</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="connection_timeout" class="form-label">Connection Timeout (seconds)</label>
                                                <input type="number" id="connection_timeout" name="connection_timeout" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['connection_timeout']); ?>" 
                                                       min="10" max="300" placeholder="30">
                                                <small class="form-text">Time to wait before connection timeout</small>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="max_concurrent_connections" class="form-label">Max Concurrent Connections</label>
                                                <input type="number" id="max_concurrent_connections" name="max_concurrent_connections" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['max_concurrent_connections']); ?>" 
                                                       min="1" max="20" placeholder="5">
                                                <small class="form-text">Maximum simultaneous connections per user</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email Settings -->
                        <div class="tab-pane" id="email">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Email Configuration</h3>
                                    <p class="card-subtitle">SMTP settings for sending notifications</p>
                                </div>
                                <div class="card-body">
                                    <div class="form-section">
                                        <div class="form-group">
                                            <label for="notification_email" class="form-label">Notification Email</label>
                                            <input type="email" id="notification_email" name="notification_email" class="form-control" 
                                                   value="<?php echo htmlspecialchars($current_settings['notification_email']); ?>" 
                                                   placeholder="<EMAIL>">
                                            <small class="form-text">Email address to receive system notifications</small>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                                <input type="text" id="smtp_host" name="smtp_host" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['smtp_host']); ?>" 
                                                       placeholder="smtp.gmail.com">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                                <input type="number" id="smtp_port" name="smtp_port" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['smtp_port']); ?>" 
                                                       min="1" max="65535" placeholder="587">
                                            </div>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                                <input type="text" id="smtp_username" name="smtp_username" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['smtp_username']); ?>" 
                                                       placeholder="<EMAIL>">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                                <input type="password" id="smtp_password" name="smtp_password" class="form-control" 
                                                       value="<?php echo htmlspecialchars($current_settings['smtp_password']); ?>" 
                                                       placeholder="Your SMTP password">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="smtp_encryption" class="form-label">SMTP Encryption</label>
                                            <select id="smtp_encryption" name="smtp_encryption" class="form-control form-select">
                                                <option value="none" <?php echo $current_settings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                                                <option value="tls" <?php echo $current_settings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo $current_settings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings -->
                        <div class="tab-pane" id="advanced">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Advanced Configuration</h3>
                                    <p class="card-subtitle">System maintenance and debugging options</p>
                                </div>
                                <div class="card-body">
                                    <div class="form-section">
                                        <div class="form-group">
                                            <label class="form-label">System Modes</label>
                                            <div class="checkbox-grid">
                                                <div class="form-check">
                                                    <label class="switch">
                                                        <input type="checkbox" name="maintenance_mode" 
                                                               <?php echo $current_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="form-check-label">Maintenance Mode</span>
                                                </div>
                                                
                                                <div class="form-check">
                                                    <label class="switch">
                                                        <input type="checkbox" name="debug_mode" 
                                                               <?php echo $current_settings['debug_mode'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="form-check-label">Debug Mode</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="log_level" class="form-label">Log Level</label>
                                                <select id="log_level" name="log_level" class="form-control form-select">
                                                    <option value="debug" <?php echo $current_settings['log_level'] === 'debug' ? 'selected' : ''; ?>>Debug</option>
                                                    <option value="info" <?php echo $current_settings['log_level'] === 'info' ? 'selected' : ''; ?>>Info</option>
                                                    <option value="warning" <?php echo $current_settings['log_level'] === 'warning' ? 'selected' : ''; ?>>Warning</option>
                                                    <option value="error" <?php echo $current_settings['log_level'] === 'error' ? 'selected' : ''; ?>>Error</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="backup_frequency" class="form-label">Backup Frequency</label>
                                                <select id="backup_frequency" name="backup_frequency" class="form-control form-select">
                                                    <option value="hourly" <?php echo $current_settings['backup_frequency'] === 'hourly' ? 'selected' : ''; ?>>Hourly</option>
                                                    <option value="daily" <?php echo $current_settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>Daily</option>
                                                    <option value="weekly" <?php echo $current_settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                                    <option value="monthly" <?php echo $current_settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="ri-save-line"></i>
                        Save Settings
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <i class="ri-refresh-line"></i>
                        Reset
                    </button>
                    <button type="button" class="btn btn-warning" onclick="testEmailSettings()">
                        <i class="ri-mail-send-line"></i>
                        Test Email
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

<script>
// Tab functionality
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const tabId = this.dataset.tab;
        
        // Remove active class from all tabs and panes
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
        
        // Add active class to clicked tab and corresponding pane
        this.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    });
});

// Reset form
function resetForm() {
    if (confirm('Are you sure you want to reset all settings? This will reload the page.')) {
        location.reload();
    }
}

// Export settings
function exportSettings() {
    // Create a simple JSON export of current form values
    const formData = new FormData(document.querySelector('.settings-form'));
    const settings = {};
    
    for (let [key, value] of formData.entries()) {
        settings[key] = value;
    }
    
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'vpn_settings_' + new Date().toISOString().split('T')[0] + '.json';
    link.click();
}

// Test email settings
function testEmailSettings() {
    alert('Email test functionality would be implemented here. This would send a test email using the current SMTP settings.');
}

// Add visual feedback for form changes
document.querySelectorAll('input, select, textarea').forEach(element => {
    element.addEventListener('change', function() {
        this.classList.add('changed');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
