    <!-- Footer -->
    <footer class="admin-footer">
        <div class="footer-content">
            <p>&copy; <?php echo date('Y'); ?> 5G Smart VPN. All rights reserved.</p>
            <div class="footer-links">
                <a href="#" class="footer-link">Documentation</a>
                <a href="#" class="footer-link">Support</a>
                <a href="#" class="footer-link">Version <?php echo ADMIN_PANEL_VERSION; ?></a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Additional Scripts -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Main Admin Script -->
    <script>
        // Global admin functions
        const Admin = {
            // Show loading state
            showLoading: function(element) {
                if (element) {
                    const originalContent = element.innerHTML;
                    element.setAttribute('data-original-content', originalContent);
                    element.innerHTML = '<i class="ri-loader-4-line animate-spin"></i> Loading...';
                    element.disabled = true;
                }
            },

            // Hide loading state
            hideLoading: function(element) {
                if (element && element.hasAttribute('data-original-content')) {
                    element.innerHTML = element.getAttribute('data-original-content');
                    element.removeAttribute('data-original-content');
                    element.disabled = false;
                }
            },

            // Show success message
            showSuccess: function(message) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: message,
                    timer: 3000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            },

            // Show error message
            showError: function(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: message,
                    confirmButtonColor: '#dc2626'
                });
            },

            // Show confirmation dialog
            confirm: function(message, callback) {
                Swal.fire({
                    title: 'Are you sure?',
                    text: message,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc2626',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, proceed!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed && callback) {
                        callback();
                    }
                });
            },

            // Make AJAX request
            request: function(url, options = {}) {
                const defaultOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': ADMIN_CONFIG.csrfToken
                    }
                };

                return fetch(url, { ...defaultOptions, ...options })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .catch(error => {
                        console.error('Request failed:', error);
                        Admin.showError('Request failed. Please try again.');
                        throw error;
                    });
            },

            // Format number with commas
            formatNumber: function(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            },

            // Format file size
            formatFileSize: function(bytes) {
                const units = ['B', 'KB', 'MB', 'GB'];
                let size = bytes;
                let unitIndex = 0;

                while (size >= 1024 && unitIndex < units.length - 1) {
                    size /= 1024;
                    unitIndex++;
                }

                return `${size.toFixed(2)} ${units[unitIndex]}`;
            },

            // Time ago function
            timeAgo: function(date) {
                const now = new Date();
                const past = new Date(date);
                const diffInSeconds = Math.floor((now - past) / 1000);

                if (diffInSeconds < 60) return 'just now';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
                if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
                if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;

                return `${Math.floor(diffInSeconds / 31536000)} years ago`;
            }
        };

        // Initialize tooltips and other UI enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add ripple effect to buttons
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Auto-hide flash messages
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                setTimeout(() => {
                    closeFlashMessage();
                }, 5000);
            }

            // Initialize form validation
            const forms = document.querySelectorAll('form[data-validate]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (!validateForm(this)) {
                        e.preventDefault();
                    }
                });
            });
        });

        // Form validation function
        function validateForm(form) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            return isValid;
        }

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });
    </script>

    <!-- Ripple effect CSS -->
    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .btn {
            position: relative;
            overflow: hidden;
        }

        .form-control.error {
            border-color: var(--error-500);
            box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
        }

        .admin-footer {
            margin-top: auto;
            padding: var(--spacing-lg) var(--spacing-xl);
            border-top: 1px solid var(--gray-200);
            background: white;
            position: relative;
            z-index: 1;
            transition: transform var(--transition-fast), opacity var(--transition-fast);
        }

        .footer-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--font-size-sm);
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .footer-link {
            color: var(--gray-500);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        .footer-link:hover {
            color: var(--primary-600);
        }

        /* Dark theme footer */
        [data-theme="dark"] .admin-footer {
            background: var(--dark-card-bg);
            border-top: 1px solid var(--gray-300);
        }

        [data-theme="dark"] .footer-content {
            color: var(--gray-400);
        }

        [data-theme="dark"] .footer-link {
            color: var(--gray-500);
        }

        [data-theme="dark"] .footer-link:hover {
            color: var(--primary-500);
        }

        /* Mobile sidebar open state */
        @media (max-width: 768px) {
            body.sidebar-open .admin-footer {
                opacity: 0;
                pointer-events: none;
            }
        }

        @media (max-width: 768px) {
            .admin-footer {
                padding: var(--spacing-sm) var(--spacing-md);
                margin-top: var(--spacing-md);
                margin-bottom: 0;
                position: relative;
                bottom: auto;
                left: 0;
                right: 0;
                width: 100%;
                box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
            }

            .footer-content {
                flex-direction: row;
                gap: var(--spacing-xs);
                text-align: center;
                font-size: var(--font-size-xs);
                justify-content: space-between;
                align-items: center;
            }

            .footer-links {
                gap: var(--spacing-sm);
                flex-wrap: nowrap;
                justify-content: flex-end;
            }

            .footer-link {
                font-size: var(--font-size-xs);
                padding: var(--spacing-xs);
                white-space: nowrap;
            }
        }

        @media (max-width: 480px) {
            .admin-footer {
                padding: var(--spacing-xs) var(--spacing-sm);
                margin-top: var(--spacing-sm);
            }

            .footer-content {
                flex-direction: column;
                gap: var(--spacing-xs);
                font-size: 10px;
            }

            .footer-links {
                gap: var(--spacing-xs);
                flex-direction: row;
            }

            .footer-link {
                font-size: 10px;
                padding: 2px 4px;
            }
        }
    </style>
</body>
</html>
